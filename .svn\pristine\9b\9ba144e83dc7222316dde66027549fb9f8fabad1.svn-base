<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayout">
  <Include name="Header"></Include>
      
  <Component type="TextArea" customJS="true">
    <![CDATA[{
      templateInEvent: "html/textArea.dot",
      css: "styles/textArea.css",
      size: "",
      valign: false,
      id: "",
      isHidden: false,
      animate: "fadeInLeft",
      header: "!{Extra_OpenQuestion_Input_Header}",
      subheader: "!{}",
      instructions: "!{Extra_OpenQuestion_Input_Instructions}",
      titleLabel: "!{}",
      title: "!{}",
      isInput: false, isInLine: false,
      only1word: false,
      sideLottie: {
        width: "3",
        src: "!{Input_Lottie}",
        loop: true, autoplay: true, background: "", speed: 1
      },
      _sideGIF: {
        width: "3",
        src: "!{Input_GIF}",
        alt: "GIF"
      },
      inputs: [
        {          
          subtitle: "!{}",
          placeHolder: "!{Extra_OpenQuestion_Input_Placeholder}",
          _remaining: {
            length: "!{InputQuestionLength}",
            label: "!{InputRemaining}"
          },
          bind: "Q_Extra_OpenQuestion1_Input"
        }
      ],
      clearBtn: {
        hidden: true,
        label: "!{InputClear}",
        toast: "!{InputCleared}"
      },
      submitBtn: {
        hidden: false,
        clearAfter: false,
        hideAfter: true, blockAfter: true,
        label: "!{InputSubmit}",
        toast: "!{InputSubmited}",
        idToShow: "",
        scrollToTop: false,
        scrollToDown: false
      },      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


</Action>