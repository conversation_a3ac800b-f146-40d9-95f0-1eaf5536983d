@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

.wizlet.wizletHCPieChart {

    @include header-badge;

    .card {
        .card-content {
            @include card-title; 

            padding: 6px 12px;
            .row {
                margin: 0;
            }
        }
    }

    .highcharts-container {
        &>svg {
            width: 100% !important;
        }

        .highcharts-background {
            fill: transparent;
        }

        .highcharts-data-label {
            
            @media #{$medium-and-up} {  font-size: 150%;  }
            
            span {
                color: color("client-colors", "font2");
            }
            tspan {
                fill: color("client-colors", "font2");
            }

        }

        .highcharts-data-labels .highcharts-label span {
            text-align: center;
            top: -2px !important;

            img {
                display: block;
                margin-bottom: -8px;
                padding: 5px;
            }
        }

        @for $i from 1 through 12 {
            .highcharts-color-#{$i - 1} {
                fill: color("client-colors", "chart#{$i}");
                //stroke: color("client-colors", "chart#{$i}");
            }
        } 
        

        .highcharts-tooltip {
            table {
                tr, td {
                    padding: 0;
                }
            }
        }
    }

    .bach-content-pieChart--container {

        &.semaphore {
            .highcharts-color-0 {
                fill: color("client-colors", "red");
            }
            .highcharts-color-1 {
                fill: color("client-colors", "yellow");
            }
            .highcharts-color-2 {
                fill: color("client-colors", "green");
            }

        }

        &.yesno {
            .highcharts-color-0 {
                fill: color("client-colors", "green");
            }
            .highcharts-color-1 {
                fill: color("client-colors", "red");
            }
        }
        &.noyes {
            .highcharts-color-0 {
                fill: color("client-colors", "red");
            }
            .highcharts-color-1 {
                fill: color("client-colors", "green");
            }
        }
        
        &.labels {
            &.medium {
                .highcharts-data-label {                
                    @media #{$medium-and-up} {  font-size: 125%;  }
                }
            }
            &.small {
                .highcharts-data-label {                
                    @media #{$medium-and-up} {  font-size: 100%;  }
                }
            }
        }

        // &.withIcon {
            
        //     .highcharts-data-labels .highcharts-label span {
        //         top: -18px !important;
        //     }
        // }

        &.noclickablelegend {
            .highcharts-legend {
                pointer-events: none;
            }
        }
    }

    .answers.card-panel {
        padding: 8px 12px;

        .btn-text {
            //display: inline-block;
            @include vertical-align-middle;
        }
    }
}