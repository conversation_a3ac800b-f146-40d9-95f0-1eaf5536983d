/* 
    "Plain Roles" wizlet that takes a template and some data as the only input
    and renders the template with functionality from wizletBase, and nothing else.
*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT'], function ($, Q, WizerApi, WizletBase, doT) {

    var Roles = function () {
        this.type = 'Roles';
        this.level = 1;
        this.isFollower = false;
        this.visited = [];
    };

    Roles.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    Roles.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
    };

    Roles.prototype.render = function (options) {
        var self = this;
        var qids = [];
        var count = 0;
        return self.validate().then(()=>{
            self.templateDefer.promise.then(function (template) {
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);
                var swipeContainer = options.context.find('.psn-infoContainer').get(0);
                var $swipeContainer = $(swipeContainer);
                $swipeContainer.data("pan", 0);
                
                var length = options.context.find('.psn-thumbnail').length
                for (let idx = 0; idx < length; idx++) {
                    self.visited [idx] = false;                
                }
                self.visited [0] = true;
                options.context.find('.psn-thumbnail0').addClass('visited');
    
                options.context.find('.psn-thumbnail').on('click', function (event) {
                    var panVal = $swipeContainer.data('pan') + 1;
                    buttonVal = parseInt($(this).data('buttonnumber'))+1;
                    var swipeVal = buttonVal - panVal;
                    if (swipeVal < 0) {
                        self.panRightHandler(Math.abs(swipeVal));
                    }else if(swipeVal==0){}
                    else {
                        self.panLeftHandler(swipeVal);
                    }
                    
                    self.visited[buttonVal-1] = true;
                    
                    if (options.wizletInfo.showWhenAllVisited) {
                        var allTrue = self.visited.every( function (val, i, arr) { return val === arr[0] } ) 
                        if( allTrue ){
                            if( !self.isFollower ){
                                $( options.wizletInfo.showWhenAllVisited ).removeAttr( 'hidden' ).find( 'a' ).removeAttr( 'disabled' );
                            }
                            $( options.wizletInfo.hideWhenAllVisited ).hide();
                        } 
                    }
                    
    
                    $(this).addClass('visited');
    
                });
    
                /*var qid1 = self.wizerApi.getQuestionIdByName("Meet1_Feedback");
                qids.push(qid1);
                var qid2 = self.wizerApi.getQuestionIdByName("Meet2_Feedback");
                qids.push(qid2);
                var qid3 = self.wizerApi.getQuestionIdByName("Meet3_Feedback");
                qids.push(qid3);
                var qid4 = self.wizerApi.getQuestionIdByName("Meet4_Feedback");
                qids.push(qid4);
                var qid5 = self.wizerApi.getQuestionIdByName("Meet5_Feedback");
                qids.push(qid5);
                var qid6 = self.wizerApi.getQuestionIdByName("Meet6_Feedback");
                qids.push(qid6);
                var qid7 = self.wizerApi.getQuestionIdByName("Meet7_Feedback");
                qids.push(qid7);
    
                self.wizerApi.getMyVotes(qids).then(function (response) {
                    for(i=0; i<qids.length; i++)
                    {
                        if(response.votes[qids[i]].length != 0)
                        {
                            count++
                        }
                    }
                    options.context.find('.meetingRemainingText').html(self.wizletInfo.meetingText + " " + ":" + " " + count+ " " +"of 3");
                });*/
                self.wizletContext.find('.psn-thumbnail'+0).addClass('psn-thumbnail--highlight');
                return true;
            })
            .fail(this.wizerApi.showError);
        });
    };

    Roles.prototype.iAmFollower = function () {
        var self = this;
        return (self.wizletInfo.isFollower && 
                self.wizletInfo.DB[self.wizletInfo.isFollower] == 1);
    };

    Roles.prototype.validate = function () {
        var self = this;
        var validating = new Q.defer();
        var promiseArr = [], promise;
        if (self.iAmFollower()) {
            self.isFollower = true;
            var teamId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackTeam);
            $.each(self.wizletInfo.scope, function (idx, condition) {
                condition = ( typeof condition === "string" ) ? condition : condition.name;
                var questionId = self.wizerApi.getQuestionIdByName(condition);
                promise = self.wizerApi.getForemanVotes(teamId,[questionId]);    
                promiseArr.push(promise); 
                promise.then(function (response) {
                    if ( response.votes[questionId].length > 0 ) {
                        self.wizletInfo.DB[condition] = response.votes[questionId][0];
                    }
                });  
            });
            Q.all(promiseArr).then(function (response) {
                validating.resolve();
            });
        } else {
            promiseArr.push(null);
            validating.resolve();
        };
        return validating.promise;
    };

    Roles.prototype.panRightHandler = function(timesToPan){
        var self = this;
        var $swipeContainer = $(self.wizletContext.find('.psn-infoContainer').get(0));
        if (typeof timesToPan == "object") {
            timesToPan = 1;
        }
        var panVal = $swipeContainer.data('pan');
        if (panVal === 0) // nothing to pan to
            return;
        else { // pan to previous component
            $swipeContainer.stop(true,true)
            panVal = panVal - timesToPan;
            var marginLeft = Number($swipeContainer.css('margin-left').replace("px", ""));
            var componentWidth = $swipeContainer.find('div:first').width();
            marginLeft = marginLeft + (componentWidth * timesToPan);
            $swipeContainer.data('pan', panVal);
            for(i=0; i<self.wizletInfo.characters.length; i++){
                if(panVal == i)
                {
                    self.wizletContext.find('.psn-thumbnail'+i).addClass('psn-thumbnail--highlight');
                }
                else
                {
                    self.wizletContext.find('.psn-thumbnail'+i).removeClass('psn-thumbnail--highlight');
                }
            }
            $swipeContainer.animate({marginLeft: marginLeft}, 'slow');
        }
    }
    Roles.prototype.panLeftHandler = function(timesToPan){
        var self = this;
        var $swipeContainer = $(self.wizletContext.find('.psn-infoContainer').get(0));
        if (typeof timesToPan == "object") {
            timesToPan = 1;
        }
        var countLeft=0;
        var panVal = $swipeContainer.data('pan');
        if (panVal === $swipeContainer.children().length - 1) // nothing to pan to
            return;
        else { // pan to previous component
            $swipeContainer.stop(true,true);
            panVal = panVal + timesToPan;
            var marginLeft = Number($swipeContainer.css('margin-left').replace("px", ""));
            var componentWidth = $swipeContainer.find('div:first').width();
            marginLeft = marginLeft - (componentWidth * timesToPan);
            $swipeContainer.data('pan', panVal);
            countLeft++;
            for(i=0; i<self.wizletInfo.characters.length; i++){
                if(panVal == i)
                {
                    self.wizletContext.find('.psn-thumbnail'+i).addClass('psn-thumbnail--highlight');
                }
                else
                {
                    self.wizletContext.find('.psn-thumbnail'+i).removeClass('psn-thumbnail--highlight');
                }
            }
            $swipeContainer.animate({marginLeft: marginLeft}, 'slow');
        }
    }
    Roles.getRegistration = function () {
        return new Roles();
    };

    return Roles;

});
