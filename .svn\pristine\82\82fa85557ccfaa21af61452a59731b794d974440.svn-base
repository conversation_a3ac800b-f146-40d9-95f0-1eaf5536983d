
<div class="container{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    <div>
        
        {{?it.header}}
        <h4 class="header {{?it.headerLabel}}with-label{{?}}">
            {{?it.headerLabel}}<span class="new badge header-label" data-badge-caption="{{=it.headerLabel}}"></span>{{?}}
            {{=it.header}}
        </h4>
        {{?}}
        {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        {{?it.sideImage}}
        <div class="row same-heigh-columns">
            <div class="col s1 side-image">
                {{?it.sideImage.topTitle1}}<span class="title">{{=it.sideImage.topTitle1}}</span>{{?}}
                {{?it.sideImage.topTitle2}}<span class="title">{{=it.sideImage.topTitle2}}</span>{{?}}
                <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.sideImage.src}}" alt="{{=it.sideImage.alt}}"
                        {{?it.sideImage.minHeight}}style="min-height: {{=it.sideImage.minHeight}};"{{?}}/>
                {{?it.sideImage.bottomTitle1}}<span class="title">{{=it.sideImage.bottomTitle1}}</span>{{?}}
                {{?it.sideImage.bottomTitle2}}<span class="title">{{=it.sideImage.bottomTitle2}}</span>{{?}}
            </div>
            <div class="col s11 side-items">
        {{??}}        
            <div class="col s12 side-items">
        {{?}}
                <ul id="sortable" class="length-{{=it.questions.length}} {{?it.class}}{{=it.class}}{{?}} {{?it.isResult}}isResult{{?}}">
                    {{~it.questions :question :idx}}
                    <li class="card-panel row hoverable sortable client-colors {{?question.color}}{{=question.color}}{{??}}primary{{?}} client-colors-text text-font2 scale-transition scaled-out"
                        data-bind="{{=question.bind}}" data-order="{{=idx+1}}" data-position="{{=idx}}">

                        {{?it.isResult}}    
                        <div class="col s1 badge"><span class="new badge left position" data-badge-caption="º"></span></div>
                        
                        {{?it.isCheck}}
                        <i class="check-icon material-icons small right scale-transition scaled-out"> </i>
                        {{?}}

                        {{?}}

                        <div class="col s11 text"><span class="text">{{=question.text}}</span></div>

                        {{? !it.isResult}}    
                        <div class="col s1"><i class="small material-icons right">swap_vert</i></div>
                        {{?}}
                    </li>
                    {{~}}
                </ul>
            </div>
        {{?it.sideImage}}
        </div>
        {{?}}


        {{?it.submitBtn && it.submitBtn.label}}    
        <div class="row submit">
            <a id="submitBtn" class="btn {{?it.DB[it.isFollower]==0}}pulse{{?}} client-colors button"
                {{?it.DB[it.isFollower]>0}}disabled{{?}}>
                <i class="medium material-icons right">send</i>{{=it.submitBtn.label}}
            </a>
        </div>
        {{?}}
        
        {{?it.isResult && it.answers}}    
        <div class="answers card-panel right client-colors button3 client-colors-text text-font2">
            <i class="small material-icons left">people</i>
            <div class="btn-text">
                <span class="text label">{{=it.answers}}</span>
                <span class="text votes"></span>
            </div>
        </div>
        <br class="clearfix">
        {{?}}

    </div>
</div>
    