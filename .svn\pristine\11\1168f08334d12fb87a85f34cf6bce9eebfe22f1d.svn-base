<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <Component type="Gauges" customJS="true">
    <![CDATA[{
			templateInEvent: "html/gauges.dot",
			css: "styles/gauges.css",
			animate: "zoomIn",

      stickyOnTop: false,
      resize: false,

			dynamic: "true",

      gauges:[
        {
          binding: "Score_SIM_Total_R2_LTUs_C1",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_LTU_C1}"  },
          initMarker: !{KPI_LTU_Init_C1},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_LTU_Min_C1},
            max: !{KPI_LTU_Max_C1},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R2_LTUs_C2",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_LTU_C2}"  },
          initMarker: !{KPI_LTU_Init_C2},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_LTU_Min_C2},
            max: !{KPI_LTU_Max_C2},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R2_LTUs_C3",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_LTU_C3}"  },
          initMarker: !{KPI_LTU_Init_C3},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_LTU_Min_C3},
            max: !{KPI_LTU_Max_C3},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R2_LTUs_C4",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_LTU_C4}"  },
          initMarker: !{KPI_LTU_Init_C4},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_LTU_Min_C4},
            max: !{KPI_LTU_Max_C4},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R2_LTUs_C5",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_LTU_C5}"  },
          initMarker: !{KPI_LTU_Init_C5},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_LTU_Min_C5},
            max: !{KPI_LTU_Max_C5},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R2_LTUs_C6",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_LTU_C6}"  },
          initMarker: !{KPI_LTU_Init_C6},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_LTU_Min_C6},
            max: !{KPI_LTU_Max_C6},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        }
      ],

      
			bindings: {
				"Score_SIM_Total_R2_LTUs_C1": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R2_LTUs_C1",
          trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R2_LTUs_C2": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R2_LTUs_C2",
          trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R2_LTUs_C3": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R2_LTUs_C3",
          trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R2_LTUs_C4": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R2_LTUs_C4",
          trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R2_LTUs_C5": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R2_LTUs_C5",
          trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R2_LTUs_C6": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R2_LTUs_C6",
          trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				}
			},
      
      trackTeam: "Team",
      isFollower: "Follower",

			scope: [
        "Follower",
        "Score_SIM_Total_R2_LTUs_C1",
        "Score_SIM_Total_R2_LTUs_C2",
        "Score_SIM_Total_R2_LTUs_C3",
        "Score_SIM_Total_R2_LTUs_C4",
        "Score_SIM_Total_R2_LTUs_C5",
        "Score_SIM_Total_R2_LTUs_C6"
      ]
		}]]>
  </Component>


</Action>