

<div class="{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.borders}} borders {{=it.borders}}{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}}{{?it.isHidden}} hidden{{?}}>  

    <div class="row">
        
        {{~it.nextSections:section:si}}
        <div id="next-section{{=si}}" 
            class="nextXML action-{{=si}} jscroll-added {{?section.isNav}}navigation {{?!section.isHiddenNav}}showable{{?}}{{?}}"
            {{?it.hiddenSections && (si > 0) && (!section.isNav || section.isNav && (si > 1))}}hidden{{?}}
            actionXML="{{=section.actionXML}}"></div>
        
            {{?it.hiddenSections && (si < (it.nextSections.length-1)) && !it.nextSections[si+1].isNav}}
            <div id="next-btn{{=si+1}}" class="next-btn-container container hidden {{?!section.isHidden}}showable{{?}}"
                {{?section.isHidden}}hidden{{?}}
                {{?si>0}}hidden{{?}} >

                {{?section.instructions}}<h6 class="instructions flow-text">{{=section.instructions}}</h6>{{?}}
                
                <div class="row center">
                    <a data-index="{{=si+1}}" {{?section.onclick}}onclick="{{=section.onclick}}"{{?}}
                        class="next-btn {{?section.button}}btn{{??}}btn-floating btn-large{{?}} pulse client-colors button">
                        
                        <i class="medium material-icons left">{{?section.icon}}{{=section.icon}}{{??}}arrow_downward{{?}}</i>{{?section.button}}{{=section.button}}{{?}}
                    </a>
                </div>
                
            </div>
            {{?}}
        {{~}}

    </div>

</div>