@charset "UTF-8";

/****      rangeSlider CSS      ****/
@import url("rangeSlider/rangeSlider.css");
@import url("rangeSlider/rangeSlider.skinHTML5.css");

@import "../materialize-src/sass/components/color-variables";

@import "mixins";


// .tab-component .wizlet.wizletRange > .container {
//     margin: 0;
//     width: initial;
// }


.wizlet.wizletRange,
.wizlet.wizletRange_Result {

    @include header-badge;
    
    @include header-help;

    .card .card-content {            
        @include card-title; 
        .card-title {
            margin-bottom: 15px;
            &:not(:first-child) {
                margin-top: 2rem;
            }
        }
        
        .sliderTitle {
            margin-bottom: 15px;
            align-items: center;
            display: flex;
            img {
                height: 48px;
                width: 48px;
                object-fit: contain;
                margin-right: 10px;
            }
        }

        .row {
            margin: 0;
        }
        @include header-badge;
        .header {
            margin-top: 0;
            &.with-label {
                margin-bottom: 2rem;
            }
            &:not(:first-child) {
                margin-top: 2rem;
            }
        }
        

        i.check-icon {
            position: absolute;
            z-index: 1;
            transform: translate(50%,-50%);

            &.left {
                left: 0;
            }
            &.right {
                right: 15px;
            }            
        }
    }

    .row.sideTitle {
        margin-bottom: 20px !important;
        .sideTitle {
            font-size: 90%;
            margin-top: 20px;
            &.left {
                text-align: left;
            }
            &.right {
                text-align: right;
            }
            &.with-sub {
                margin-top: 10px;
            }
        }
    }

    
    @mixin isDisabled () {
        &.disabled {
            opacity: .8;
            filter: grayscale(100%);
        }
    }

    .range {

        .irs {
            .irs-single, .irs-from, .irs-to {
                background: color("client-colors", "secondary");
            }
            .irs-min, .irs-max {
                top: 10px;
            }
            .irs-single {
                padding: 5px 10px;
                transform: translateY(-10px);
            }

        }

        &.nominmax .irs {
            
            .irs-min, .irs-max {
                display: none;
            }
        }
    
        &.solution,
        &.disabled,
        &.blocked {
            pointer-events: none;
        }

        @include isDisabled;
        
        &.color {

            @for $i from 1 through 6 {
                &.aux#{$i} {
                    .irs-bar, .irs-bar-edge {
                        border-color: color("client-colors", "aux#{$i}");
                        @include linear-gradient( 
                            rgba(color("client-colors", "aux#{$i}"),0.5), 
                            color("client-colors", "aux#{$i}") );
                    }                
                }
            }  
            @for $i from 1 through 12 {
                &.chart#{$i} {
                    .irs-bar, .irs-bar-edge {
                        border-color: color("client-colors", "chart#{$i}");
                        @include linear-gradient( 
                            rgba(color("client-colors", "chart#{$i}"),0.5), 
                            color("client-colors", "chart#{$i}") );
                    }                
                }
            }  
            
            @for $i from 1 through 4 {
                &.categoria#{$i} {
                    .irs-bar, .irs-bar-edge {
                        border-color: color("client-colors", "categoria#{$i}");
                        @include linear-gradient( 
                            rgba(color("client-colors", "categoria#{$i}"),0.5), 
                            color("client-colors", "categoria#{$i}") );
                    }                
                }
            }  

            &.foreBack {
                .irs-bar, .irs-bar-edge {
                    border-color: color("client-colors", "sliderFore");
                    background: color("client-colors", "sliderFore");
                }         
                .irs-line-mid, .irs-line-right {
                    border-color: color("client-colors", "sliderBack");
                    background: color("client-colors", "sliderBack");
                }                
            }

        }
        &.custom.image {
            margin-top: 20px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position-y: -15px;

            .irs-slider {
                background-size: 100% 100%;
                background-repeat: no-repeat;
                width: 8.3vw;
                    min-width: 53px;
                    max-width: 100px;
                height: 7vw;
                    min-height: 63px;
                    max-height: 84px;
                border: none;
                border-radius: 0px;
                box-shadow: none;
                transform: translateY(-20%);

                &.state_hover, &:hover {
                    background-color: transparent !important;
                }
            }

            .irs-single, .irs-from, .irs-to {
                visibility: hidden;
            }

            .irs-bar, .irs-bar-edge, .irs-line {    
                border: none;
                background: none;
            }
            
    
            .irs-grid > .irs-grid-text{
                visibility: hidden;
            }

        }
        
        &.adjustIcon {
            .irs.with-icon > .irs-bar {
                transform: translateX(-6px);
            }
        }
    }

    .gauge.total.card-panel {

        &.position-up {
            position: absolute;
            right: 5px;
        }
        
        padding: 8px 12px;
        display: inline-flex;
        margin-left: 12px;

        .gauge-label {
            margin-right: 5px;
        }
        .gauge-suffix {
            margin-left: 5px;
        }

        @include isDisabled;
    }
    
    @include row-submit;
}