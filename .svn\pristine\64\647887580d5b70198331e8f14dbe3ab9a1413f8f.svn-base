<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
      <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- ###### -->
    <!-- SCORES -->
    <!-- ###### -->

    <!-- KPI1 -->
    <Score result="Score_SIM_R1_Scenario2_KPI1_Opt1" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt1">
        <Choice value="1" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI1_Opt2" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt2">
        <Choice value="2" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI1_Opt3" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt3">
        <Choice value="3" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI1_Opt4" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt4">
        <Choice value="4" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI1_Opt5" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt5">
        <Choice value="5" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI1_Opt6" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt6">
        <Choice value="6" Response="0"></Choice>
      </Question>
    </Score>
    <Total result="Score_SIM_R1_Scenario2_KPI1" method="sum">
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1_Opt1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1_Opt2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1_Opt3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1_Opt4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1_Opt5</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1_Opt6</Question>
    </Total>

    <!-- KPI2 -->
    <Score result="Score_SIM_R1_Scenario2_KPI2_Opt1" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt1">
        <Choice value="1" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI2_Opt2" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt2">
        <Choice value="2" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI2_Opt3" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt3">
        <Choice value="3" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI2_Opt4" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt4">
        <Choice value="4" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI2_Opt5" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt5">
        <Choice value="5" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI2_Opt6" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt6">
        <Choice value="6" Response="0"></Choice>
      </Question>
    </Score>
    <Total result="Score_SIM_R1_Scenario2_KPI2" method="sum">
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2_Opt1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2_Opt2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2_Opt3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2_Opt4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2_Opt5</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2_Opt6</Question>
    </Total>

    <!-- KPI3 -->
    <Score result="Score_SIM_R1_Scenario2_KPI3_Opt1" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt1">
        <Choice value="1" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI3_Opt2" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt2">
        <Choice value="2" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI3_Opt3" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt3">
        <Choice value="3" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI3_Opt4" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt4">
        <Choice value="4" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI3_Opt5" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt5">
        <Choice value="5" Response="0"></Choice>
      </Question>
    </Score>
    <Score result="Score_SIM_R1_Scenario2_KPI3_Opt6" type="Choice">
      <Question name="Q_SIM_R1_Scenario2_Opt6">
        <Choice value="6" Response="0"></Choice>
      </Question>
    </Score>
    <Total result="Score_SIM_R1_Scenario2_KPI3" method="sum">
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3_Opt1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3_Opt2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3_Opt3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3_Opt4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3_Opt5</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3_Opt6</Question>
    </Total>

    <!-- ###### -->
    <!-- TOTALS -->
    <!-- ###### -->

    <!-- LTUs -->
    <Total result="Score_SIM_Total_R1_LTUs_C1" method="sum">
      <Question validate="false">Score_SIM_Init_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_LTUs_C1</Question>
    </Total>

    <!-- Penalty -->
    <Score type="Range" result="Score_SIM_R1_Penalty_Multiplier">
      <Question>Score_SIM_Total_R1_LTUs_C1</Question>
      <Boundary value="1">0</Boundary>
      <Boundary value="0">99</Boundary>
    </Score>
    <Total result="Score_SIM_R1_Penalty_KPI2_C1" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Penalty_Multiplier</Question>
    </Total>
    <Total result="Score_SIM_R1_Penalty_KPI3_C1" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R1_Penalty_Multiplier</Question>
    </Total>

    <!-- KPI1 -->
    <Total result="Score_SIM_Total_R1_KPI1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI1</Question>
    </Total>

    <!-- KPI2 -->
    <Total result="Score_SIM_Total_R1_KPI2" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Penalty_KPI2_C1</Question>
    </Total>

    <!-- KPI3 -->
    <Total result="Score_SIM_Total_R1_KPI3" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Penalty_KPI3_C1</Question>
    </Total>

    <!-- ROUND TOTAL -->
    <Total result="Score_SIM_Total_R1" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total_R1</Question>
    </Total>

  </Aggregator>


</Action>