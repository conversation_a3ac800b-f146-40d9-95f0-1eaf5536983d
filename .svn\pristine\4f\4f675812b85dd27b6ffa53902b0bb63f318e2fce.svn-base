<?xml version="1.0" encoding="utf-8" ?>
<Action>
  
  <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeInRight",
      transparentBox: false,
      content: {
        position: "up",
        _animate: "fadeInUp animate__delay-2s",
        title: "!{SIM_R1_CaseStudy2_Title}",
        body: "!{SIM_R1_CaseStudy2_Text}"
      }
    }]]>  
  </Component>

</Action>