<?xml version="1.0" encoding="utf-8" ?>
<Action>


  <!-- My Metrics -->
  <Component type="TableInputs" customJS="true">
    <![CDATA[{
        templateInEvent: "html/tableInputs.dot",
        css: "styles/tableInputs.css",
        instructions: "!{}",
        header: "!{}",
        animate: "fadeIn",
        class: "_responsive-table bigHeaders _firstColumnBold lastColumnBold centered",
        table: {
            title: "!{SIM_R3_Summary_Title}",
            body: "!{}",
            headers: [ "!{}", "!{SIM_R3_Summary_Table_Head1}", "!{SIM_R3_Summary_Table_Head2}" ],
            rows: [
                [ 
                    { type:"text", class:"", text:"<bh>!{KPI_Metric1}</bh>" },  
                    { type:"tloutput", class:"", tloutput:"Score_SIM_Total_R2_KPI1" },
                    { type:"tloutput", class:"", tloutput:"Score_SIM_Total_R3_KPI1" }
                ],
                [ 
                    { type:"text", class:"", text:"<bh>!{KPI_Metric2}</bh>" },  
                    { type:"tloutput", class:"", tloutput:"Score_SIM_Total_R2_KPI2" },
                    { type:"tloutput", class:"", tloutput:"Score_SIM_Total_R3_KPI2" }
                ],
                [ 
                    { type:"text", class:"", text:"<bh>!{KPI_Metric3}</bh>" },  
                    { type:"tloutput", class:"", tloutput:"Score_SIM_Total_R2_KPI3" },
                    { type:"tloutput", class:"", tloutput:"Score_SIM_Total_R3_KPI3" }
                ],
                [ 
                    { type:"text", class:"bold big", text:"!{KPI_TOTAL}", _img:"!{SIM_Dashboard_Table_Row8_Img}" },
                    { type:"text", class:"", text:"!{}" },
                    { type:"tloutput", class:"bold big", tloutput:"Score_SIM_Total_R3" }
                ]
            ],
            note: ""  
        },

        saveInModel: false,
        bindings: {
          "Score_SIM_Total_R3_KPI1": {
              dynamic:true,
              bind: "db:Score_SIM_Total_R3_KPI1",
              trackQuestion: "Team",    
					    render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
              renderOptions: {
                  numberFormat: {
                      value: "##0", _suffix: "%"
                  }
              }
          },
          "Score_SIM_Total_R3_KPI2": {
              dynamic:true,
              bind: "db:Score_SIM_Total_R3_KPI2",
              trackQuestion: "Team",    
					    render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
              renderOptions: {
                  numberFormat: {
                      value: "##0", _suffix: "%"
                  }
              }
          },
          "Score_SIM_Total_R3_KPI3": {
              dynamic:true,
              bind: "db:Score_SIM_Total_R3_KPI3",
              trackQuestion: "Team",    
					    render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
              renderOptions: {
                  numberFormat: {
                      value: "##0", _suffix: "%"
                  }
              }
          },
          "Score_SIM_Total_R2_KPI1": {
              dynamic:true,
              bind: "db:Score_SIM_Total_R2_KPI1",
              trackQuestion: "Team",    
					    render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
              renderOptions: {
                  numberFormat: {
                      value: "##0", _suffix: "%"
                  }
              }
          },
          "Score_SIM_Total_R2_KPI2": {
              dynamic:true,
              bind: "db:Score_SIM_Total_R2_KPI2",
              trackQuestion: "Team",    
					    render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
              renderOptions: {
                  numberFormat: {
                      value: "##0", _suffix: "%"
                  }
              }
          },
          "Score_SIM_Total_R2_KPI3": {
              dynamic:true,
              bind: "db:Score_SIM_Total_R2_KPI3",
              trackQuestion: "Team",    
					    render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
              renderOptions: {
                  numberFormat: {
                      value: "##0", _suffix: "%"
                  }
              }
          },
          "Score_SIM_Total_R3": {
              dynamic:true,
              bind: "db:Score_SIM_Total_R3",
              trackQuestion: "Team",    			
					    render: "renderer/spanBox",
              renderOptions: {
                  numberFormat: {
                      value: "##0.0"
                  }
              }
          }
        }
    }]]>
  </Component>


  <Component type="HCWebChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCWebChart.dot",
    css: "styles/HCWebChart.css",
    header: "!{}",
    instructions: "!{}",    
    questions: [
      {
        binding: "Score_SIM_Total_R2_KPI1"
      },
      {
        binding: "Score_SIM_Total_R2_KPI2"
      },
      {
        binding: "Score_SIM_Total_R2_KPI3"
      },
      {
        binding: "Score_SIM_Total_R3_KPI1"
      },
      {
        binding: "Score_SIM_Total_R3_KPI2"
      },
      {
        binding: "Score_SIM_Total_R3_KPI3"
      }
    ],
    trackQuestion: "Team",
    chartConfig: {
      chart: {
        type: "line",
        height: '60%'
      },
      title: {
        text: ""
      },
      subtitle: {
        text: ""
      },
      xAxis: [
        {
          categories: [
            "!{KPI_Metric1}",
            "!{KPI_Metric2}",
            "!{KPI_Metric3}"
          ]
        }
      ],
      yAxis: {
        min: 50,
        labels: {
          enabled: false
        }
      },
      tooltip: {
        headerFormat: "<b>{point.x}</b><br/> <table>",
        pointFormat: '<tr><td>{series.name}:  <b>{point.y}</b></td></tr>',
        footerFormat: '</table>',
        crosshairs: true,
        shared: true,
        useHTML: true
      },
      legend: {
        enabled: true,
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      "plotOptions": {
        "series": {
          "dataLabels": {
            enabled: true,
            allowOverlap: true
          }
        }
      },
      series: [
        {          
          question: "Q_My_Name",
          isName: true
        }
      ]
    },
    split: {
      groups: [
        "!{SIM_R3_Summary_Table_Head1}",
        "!{SIM_R3_Summary_Table_Head2}"
      ]
    },

    scope: [ ]
    
  }]]></Component>


</Action>