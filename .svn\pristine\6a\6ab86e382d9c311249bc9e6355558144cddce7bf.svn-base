@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";


.wizlet.wizletImageTooltip {

    .card {
        
        .card-content {
            @include card-title; 
        }

        .card-image {

            padding: 12px;

            a.btn-tooltip {
                cursor: pointer;
                position: absolute;
                // background-color: red;

                i {

                    font-size: 1rem;
                    @media #{$medium-and-up} {
                        font-size: 2rem;
                    }
                    @media #{$large-and-up} {
                        font-size: 3rem;
                    }
                    @media #{$extra-large-and-up} {
                        font-size: 3.5rem;
                    }
                    //color: color("client-colors", "red");

                    &.bounce {
                        @include animation-name(bounceInfinite);
                        @include animation-duration(10s);
                        @include animation-iteration-count(infinite);
                        @include animation-timing-function(ease);
                        @include animation-fill-mode(forwards);
                    }

                }
            
                @for $i from 1 through 10 {
                    &:nth-child(#{$i - 1}) {
                        i.bounce {
                            @include animation-delay(#{$i}s);
                        }
                    }
                } 

                .underline {
                    position: absolute;
                    height: 3px;
                    background-color: color("client-colors", "white");

                    &.pulse::before {
                        z-index: 1;
                    }
                }
            }

            
        }

        &.fit {

            .card-image {
                max-height: 70vh;
                height: 100%;

                img {
                    object-fit: contain;
                    max-height: 70vh;
                }
            }

        }

    }

    .fullscreen {
        .card-image {
            padding: 0;
        }
    }
    
}

@include tooltip-booble;

