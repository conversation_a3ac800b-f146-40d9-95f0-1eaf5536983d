

<div class="container{{?it.valign}} valign-container{{?}}{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.borders}} borders {{=it.borders}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"
    {{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    <div>
        
        {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        <div class="card">            
            <div class="card-content">
                

                <div id="diceBoard">
    
                    <div id="wrapper">
                        <div id="platform">
                            <div id="dice">
                                <div class="side front">
                                    <div class="dot center"></div>
                                </div>
                                <div class="side front inner"></div>
                                <div class="side top">
                                    <div class="dot dtop dleft"></div>
                                    <div class="dot dbottom dright"></div>
                                </div>
                                <div class="side top inner"></div>
                                <div class="side right">
                                    <div class="dot dtop dleft"></div>
                                    <div class="dot center"></div>
                                    <div class="dot dbottom dright"></div>
                                </div>
                                <div class="side right inner"></div>
                                <div class="side left">
                                    <div class="dot dtop dleft"></div>
                                    <div class="dot dtop dright"></div>
                                    <div class="dot dbottom dleft"></div>
                                    <div class="dot dbottom dright"></div>
                                </div>
                                <div class="side left inner"></div>
                                <div class="side bottom">
                                    <div class="dot center"></div>
                                    <div class="dot dtop dleft"></div>
                                    <div class="dot dtop dright"></div>
                                    <div class="dot dbottom dleft"></div>
                                    <div class="dot dbottom dright"></div>
                                </div>
                                <div class="side bottom inner"></div>
                                <div class="side back">
                                    <div class="dot dtop dleft"></div>
                                    <div class="dot dtop dright"></div>
                                    <div class="dot dbottom dleft"></div>
                                    <div class="dot dbottom dright"></div>
                                    <div class="dot center dleft"></div>
                                    <div class="dot center dright"></div>
                                </div>
                                <div class="side back inner"></div>
                                <div class="side cover x"></div>
                                <div class="side cover y"></div>
                                <div class="side cover z"></div>
                            </div>
                        </div>
    
                          
                        <div class="row center">
                            <a id="playBtn" hidden class="btn-large client-colors button">
                                <i class="large material-icons right">pan_tool</i>{{?it.btnPlay}}{{=it.btnPlay}}{{??}}Roll it!{{?}}
                            </a>
                            
                            <div id="scoreBox" hidden 
                                class="card-panel {{?it.scoreBox.animate}}animated {{=it.scoreBox.animate}}{{?}} client-colors secondary client-colors-text text-font2">
                                <span class="pre">{{=it.scoreBox.label_pre}}</span>
                                <span class="points strong"></span>
                                <span class="post">{{=it.scoreBox.label_post}}</span>
                            </div>
                        </div>

                    </div>
    
    
                </div>                
                


            </div>
        </div>  
                

    </div>
</div>
    