﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var Carousel = function () {
        this.type = 'Carousel';
        this.level = 1;
        this.timer = 5000;
        this.autoplay_id;
    };

    Carousel.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.votesBeforeUpdate = [];

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Carousel.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };

    Carousel.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
                 
            $carousel = options.context.find('.carousel');
            $carousel.carousel({
                fullWidth: false,
                indicators: (options.wizletInfo.indicators == null) ? true : options.wizletInfo.indicators,
                onCycleTo : function($current_item, dragged) {
                    if (options.wizletInfo.autoplay) {
                        stopAutoplay();
                        startAutoplay();
                    }
                }
            });
            
            options.context.find('.modal').modal();
            
            function startAutoplay() {
               self.autoplay_id = setInterval(function() {
                  $carousel.carousel('next');
                }, self.timer);
            }
            
            function stopAutoplay() {
              if(self.autoplay_id) {
                clearInterval(self.autoplay_id);
              }
            }

            //Load a new page(an action XML) into a modal
            options.context.find("[data-modal]").off("click").on("click", function (e) {
                self.loadPage(
                    $(this).data('modal'), 
                    options.context, 
                    self.unsedContext, 
                    $( $( $(this).attr('href')).find('.modal-content') ) 
                );
            });


            if (options.wizletInfo.ifAllvisited) {

                options.context.find(".carousel-item").off("click").on("click", function (e) {
                    
                    if ($(this).hasClass('active')) {
                        options.wizletInfo.items[$(this).data('index')].visited = true;

                        var allVisited = true;
                        $.each(options.wizletInfo.items, function (idx, item) {
                            allVisited = item.visited && allVisited;
                        });
                        if (allVisited) $(options.wizletInfo.ifAllvisited).removeAttr('hidden').find('a').removeAttr('disabled');
                    }
                });

            }

            return true;
        })
        .fail(this.wizerApi.showError);
    };

    

    /**
     * Load an action screen inside a modal window
     */
    Carousel.prototype.loadPage = function (actionXMLName, context, unusedContext, modalContainer) {
        var self = this;

        //unload previous 
        if (self.currentWizletModule && self.currentWizletModule.length > 0) {
            $.each(self.currentWizletModule, function (index, module) {
                if (module.wizletInstance.unloadHandler) {
                    module.wizletInstance.unloadHandler();
                }
            });
        }
        
        $('.material-tooltip:not(.timage)').remove();;
        
        var loading = self.wizerApi.loadActionInContainer(actionXMLName, context, unusedContext, modalContainer);
        loading.then(function (loads) {
           
            self.currentWizletModule = loads;
            
            var page = "wizer:action:init.mainArea";
                        
            $(document).trigger(page, actionXMLName);
        });
        
    };

    
    Carousel.getRegistration = function () {
        return new Carousel();
    };

    return Carousel;

});