<Action>

  <Component type="HCPieChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCPieChart.dot",
    css: "styles/HCPieChart.css",
    id: "pieChartScenario4",
    class: "",
    header: "!{}",
    _instructions: "!{RadioInstructionsAnswers}",    
    isAnswersDistribution: true,
    isCountingIfChecked: false,
    isCountingAllCombined: false,
    listenModel: false,
    questions: [
      {
        binding: "Q_SIM_R3_Scenario4"
      }
    ],
    trackQuestion: "Show_charts",
    chartConfig: {
      chart: {    
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        _height: '60%',
        type: "pie"
      },
      title: {
        text: "!{SIM_R3_Scenario4_Question}"
      },
      subtitle: {
        text: ""
      },
      tooltip: {
        headerFormat: "",
        pointFormat: "{point.name}: <b>{point.y}</b> ({point.percentage:.1f}%)"
      },
      legend: {
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        pie: {
          size: "100%", 
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            useHTML: false,
            format: '<b>{point.percentage:.1f}</b> %',
            distance: -50,
            filter: {
                property: 'percentage',
                operator: '>',
                value: 1
            }
          },
          showInLegend: true
        }
      },
      series: [ 
        { 
          name: "",
          colorByPoint: true,
          data: [
            {
              name: "!{Choice_Opt1} - !{SIM_R3_Scenario4_Opt1}"
            },
            {
              name: "!{Choice_Opt2} - !{SIM_R3_Scenario4_Opt2}"
            }
          ]
        }
      ],

      responsive: {
        rules: [{
          condition: { maxWidth: 700 },
          chartOptions: {
            chart: { height: 'auto' },
            plotOptions: {
              pie: {  dataLabels: {  enabled: false  } }
            }
          }
        }]
      }
    },
    scope: [ ],    
    _answers: "!{Results_VotesLabel}"
  }]]></Component>

</Action>