﻿<div class="container table{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} 

    {{?it.isHidden}}
    {{?it.condition}}
        {{? (it.condition_Val && (it.DB[it.condition] == it.condition_Val)) ||
            (it.condition_Diff && (!it.DB[it.condition] || (it.DB[it.condition] != it.condition_Diff))) ||
            (it.condition_Greater && (!it.DB[it.condition] || (it.DB[it.condition] >= it.condition_Greater))) ||
            (it.condition_Less && (!it.DB[it.condition] || (it.DB[it.condition] < it.condition_Less)))  }}
            hidden
        {{?}}
    {{??}}
        hidden
    {{?}}
{{?}}
>  


    <div>
        
        {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}
        {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        <div class="card">
            
            <div class="card-content {{?it.dataTablesHideControls}}hideControls{{?}} {{?it.table.image}}row{{?}}">
                {{?it.table.title}}<span class="card-title">{{=it.table.title}}</span>{{?}}
                {{?it.table.body}}<span class="flow-text">{{=it.table.body}}<br></span>{{?}}
   
                {{?it.table.image}}
                <div class="card-image col s12 m{{=it.table.image.width}}">
                    <img class="responsive-img" 
                        src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.table.image.src}}" alt="{{=it.table.image.alt}}"/>
                            
                </div>
                {{?}}

                <table class="striped highlight {{?it.class}}{{=it.class}}{{?}} {{?it.isDataTables}}dataTables{{?}} {{?it.table.image}}col s12 m{{=12-it.table.image.width}}{{?}}">
                    <thead {{?it.table.headerColor}}class="headerColor {{=it.table.headerColor}}"{{?}}>
                        <tr>
                            {{~it.table.headers :header}}
                            <th>{{=header}}</th>
                            {{~}}               
                        </tr>
                    </thead>
            
                    <tbody>
                        {{~it.table.rows :row:r}}
                        <tr>                            
                            {{~row :cell:c}}
                                <td {{?it.table.cellClass && it.table.cellClass[r][c]}}class="{{=it.table.cellClass[r][c]}}"{{?}}>{{=cell}}</td>
                            {{~}}
                        </tr>
                        {{~}}
                    </tbody>
                    {{?it.table.note}}<caption>{{=it.table.note}}</caption>{{?}}
                </table>

                {{?it.table.body2}}<p class="flow-text">{{=it.table.body2}}<br><br></p>{{?}}

            </div>

        </div>  
        
        
    </div>
</div>
    