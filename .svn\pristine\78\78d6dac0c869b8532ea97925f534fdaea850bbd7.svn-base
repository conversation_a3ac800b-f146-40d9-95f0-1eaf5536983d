@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
//@import "../materialize-src/sass/components/variables";
@import "mixins";


.wizlet.wizletActionsLoader {

    &>.borders {
        @include card-border(10px,double); 
    }
    

    .next-btn-container {
        &.hidden { visibility: hidden; opacity: 0;}
        transition: visibility 0.5s, opacity 1s linear;
        
        .instructions {
            color: color("client-colors","font3");
        }
        .next-btn {
            font-size: inherit;
            margin-top: 10px;
        }
    }

}

