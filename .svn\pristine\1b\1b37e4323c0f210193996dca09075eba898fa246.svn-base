﻿
<div class="container{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} 

{{?it.isHidden}}
{{?it.condition}}
    {{?!it.DB[it.condition] || 
        (it.condition_Val && (it.DB[it.condition] == it.condition_Val)) ||
        (it.condition_Greater && (it.DB[it.condition] > it.condition_Greater))}}
        hidden
    {{?}}
{{??}}
    hidden
{{?}}
{{?}}
>  

<div>

{{?it.help}}
<div class="header-container">   
{{?}}

{{?it.header}}
<h4 class="header {{?it.headerLabel}}with-label{{?}}{{?it.help}} help{{?}}">
    {{?it.headerLabel}}<span class="new badge header-label" data-badge-caption="{{=it.headerLabel}}"></span>{{?}}
    {{=it.header}}
</h4>
{{?}}
{{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
{{?it.help}}
    <i class="material-icons help client-colors-text text-primary {{?it.help.modalID}}modal-trigger" href="#{{=it.help.modalID}}{{?}}">{{?it.help.icon}}{{=it.help.icon}}{{??}}help{{?}}</i>
 </div>   
{{?}}

{{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

<div class="card">
    
    {{?it.total && it.total.position=="up"}} 
    <div class="gauge position-up total card-panel right client-colors primary client-colors-text text-font2">
            <span class="gauge-label">{{=it.total.label}} </span>
            <span data-binding="{{=it.total.binding}}"></span>
            {{?it.total.suffix}}<span class="gauge-suffix">{{=it.total.suffix}}</span>{{?}}
            {{?it.total.maxValue.restrictionBind}} 
                <span class="gauge-label">&nbsp/ </span> <span data-binding="{{=it.total.maxValue.restrictionBind}}"></span>
            {{?}}
            
            {{?it.total.maxValue.val}} 
                <span class="gauge-label">&nbsp/ {{=it.total.maxValue.val}}</span>
            {{?}}
    </div>
    {{?}}
    
    <div class="card-content">
        
        {{?it.title}}
        <h5 class="header title {{?it.titleLabel}}with-label{{?}}">
            {{?it.titleLabel}}<span class="new badge header-label" data-badge-caption="{{=it.titleLabel}}"></span>{{?}}
            {{=it.title}}
        </h5>
        {{?}}
        
        {{?it.isInline}}<div class="inline row">{{?}}

        {{~it.sliders :slider :idx}}
            {{?slider.cardTitle}}<span class="card-title flow-text">{{=slider.cardTitle}}</span>{{?}}

            {{?slider.sliderTitle}}<h5 class="header sliderTitle">{{=slider.sliderTitle}}</h5>{{?}}
            <div class="{{?it.isInline}}col {{=it.optionWidth}} {{?}}row {{?slider.sideTitles}}sideTitle{{?}}">
                {{?slider.sideTitles && slider.sideTitles.left}}
                    <div class="sideTitle left col s{{=slider.sideTitles.width}}{{?slider.sideTitles.leftSubTxt}} with-sub{{?}}">{{=slider.sideTitles.left}}
                        {{?slider.sideTitles.leftSubTxt && it.DB[slider.sideTitles.leftSubVal]}}
                        <br><small>{{=slider.sideTitles.leftSubTxt}}&nbsp{{=it.DB[slider.sideTitles.leftSubVal]}}</small>
                        {{?}}
                    </div>
                {{?}}
                    <div class="col {{?slider.isSolution}}solution{{?}} 
                                {{?slider.sideTitles && slider.sideTitles.left && slider.sideTitles.right}}s{{=12-2*slider.sideTitles.width}}{{?}} 
                                {{?slider.sideTitles && slider.sideTitles.left && !slider.sideTitles.right}}s{{=12-slider.sideTitles.width}}{{?}} 
                                {{?!slider.sideTitles}}s12{{?}} 
                                range slider-{{=idx+1}} {{?slider.class}}{{=slider.class}}{{?}} {{?it.isFollower && it.DB[it.isFollower]==1}}disabled{{?}}"
                {{?slider.background}}
                    style="background-image: url('Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=slider.background}}')"
                {{?}}
                >   
                <input type="text" id="{{=slider.id}}" data-idx="{{=idx}}" name="" 
                    {{?it.DB[it.isFollower]!=1 && it.DB[it.isFollower]!=2}} 
                            {{?slider.saveValues && 
                                slider.options.values && slider.options.values[slider.options.from]}}
                                data-value="{{=slider.options.values[slider.options.from]}}" 
                            {{??}}
                                {{?!slider.saveValues && 
                                    slider.options.values && slider.options.values[slider.options.from]}}
                                    data-value="{{=slider.options.from + slider.options.min}}" 
                                {{??}}
                                    data-value="{{=slider.options.from}}" 
                                {{?}}
                            {{?}}
                                {{?slider.bind}}data-bind="{{=slider.bind}}"{{?}} 
                                {{?slider.calcDifference}}data-binddif="{{=slider.calcDifference}}"{{?}} 
                                {{?slider.binding}}data-binding="{{=slider.binding}}"{{?}} 
                    {{?}}/>  
                </div>
                {{?slider.sideTitles && slider.sideTitles.right}}
                    <div class="sideTitle right col s{{=slider.sideTitles.width}}">{{=slider.sideTitles.right}}</div>
                {{?}}

                {{?slider.isCheck}}
                    <i hidden class="check-icon material-icons small right"> </i>
                {{?}}

                {{?slider.moreInfo}}
                        <ul class="collapsible">   
                            <li data-index="{{=idx+1}}">
                                <div class="collapsible-header flow-text">
                                    <i class="expand material-icons large bottom">expand_more</i>
                                    <span class="strong">{{=it.moreInfo}}</span>
                                </div>
                                <div class="collapsible-body flow-text"><span>{{=slider.moreInfo}}</span></div>
                            </li>
                        </ul>
                {{?}}
            </div>
        {{~}}
        {{?it.isInline}}</div>{{?}}

    </div>

    {{?it.total && it.total.position!="up"}} 
    <div class="gauge total card-panel right client-colors primary client-colors-text text-font2">
            <span class="gauge-label">{{=it.total.label}} </span>
            <span data-binding="{{=it.total.binding}}"></span>
            {{?it.total.suffix}}<span class="gauge-suffix">{{=it.total.suffix}}</span>{{?}}
            {{?it.total.maxValue.restrictionBind}} 
                <span class="gauge-label">&nbsp/ </span> <span data-binding="{{=it.total.maxValue.restrictionBind}}"></span>
            {{?}}
            
            {{?it.total.maxValue.val}} 
                <span class="gauge-label">&nbsp/ {{=it.total.maxValue.val}}</span>
            {{?}}
    </div>
    {{?}}
</div>


    
{{?it.submitBtn}}
<div class="row submit">
    <a id="submitBtn" {{?it.submitBtn.hidden}}hidden{{?}} class="btn pulse client-colors button"
        {{?it.DB[it.isFollower]>0}}disabled{{?}}>
        <i class="medium material-icons right">send</i>{{=it.submitBtn.label}}
    </a>
</div>
{{?}}

</div>

</div>
