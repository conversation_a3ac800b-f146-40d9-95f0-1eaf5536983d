﻿{{? !it.enabled || (it.enabled && (typeof it.DB[it.enabled]!=="undefined"))}}
<div class="container {{?it.animate}}animate__animated animate__{{=it.animate}}{{?}}" {{?it.id}}id="{{=it.id}}" {{?}} 

    {{?it.isFollower && it.DB[it.isFollower]==1}}disabled{{?}}
    {{?it.isFollower && it.DB[it.isFollower]==2}}hidden{{?}}
    
    {{?it.isHidden}}
    {{?it.condition}}
        {{? (it.condition_Val && (it.DB[it.condition] == it.condition_Val)) ||
            (it.condition_Diff && (!it.DB[it.condition] || (it.DB[it.condition] != it.condition_Diff))) ||
            (it.condition_Greater && (!it.DB[it.condition] || (it.DB[it.condition] > it.condition_Greater))) ||
            (it.condition_Less && (!it.DB[it.condition] || (it.DB[it.condition] < it.condition_Less)))  }}
            hidden
        {{?}}
    {{??}}
        {{?it.showDelay}}
            invisible
        {{??}}
            hidden
        {{?}}
    {{?}}
    {{?}}
    >
    
    {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

    <div class="row {{?it.aligned}}{{=it.aligned}}{{??}}center{{?}}">
        {{?it.buttons}} {{~it.buttons :button:idx}}
        <span {{?button.id}}id="{{=button.id}}" {{?}} {{?button.isHidden}}hidden{{?}} {{?button.disabled}}disabled{{?}}>
           {{?button.isFloat}}
           <!-- Float button at any screen size -->
           <a class="btn-floating {{?button.isLarge}}btn-large{{??}}btn{{?}} {{?button.class}}{{=button.class}}{{?}} {{?button.pulse}}pulse{{?}} client-colors button {{?button.tooltip}}tooltipped tnavigation{{?}}
                    {{?it.DB[it.isFollower]==1}}follower{{?}} {{?button.type==='modal'}}modal-trigger button2{{?}}" 
               data-position="bottom" {{?button.tooltip}}data-tooltip="{{=button.tooltip}}"{{?}} 
               {{?button.type==="previous"}}data-previousAction{{?}}
               {{?button.type==="next"}}data-nextAction{{?}}
               {{?button.popupID}}href="#{{=button.popupID}}" data-modal="{{=button.popup}}"{{?}}
               {{?button.gdActionCommand}}data-gdcommand="{{=button.gdActionCommand}}" data-gdcommandxml="{{=button.gdActionCommandXML}}"{{?}}
               {{?button.gdActionEmbed}}data-gdembed="{{=button.gdActionEmbed}}" data-index="{{=idx}}"{{?}}
               {{?button.gdActionSection}}data-gdaction="{{=button.gdActionSection}}"{{?}}
               {{?button.gdActionTrack}}data-gdtrack="{{=button.gdActionTrack}}"{{?}}
               {{?button.targetSection}}data-action="{{=button.targetSection}}"{{?}}
               {{?button.conditionTarget && it.DB[button.conditionTarget] && button.conditionalTarget}}
                   data-action="{{=button.conditionalTarget[it.DB[button.conditionTarget]]}}"
               {{?}}
               {{?button.innerTargetSection}}data-inner-action="{{=button.innerTargetSection}}"{{?}}
               {{?button.innerTargetSection_check}}
                   data-inner-action-check
                   data-inner-action-correct="{{=button.innerTargetSection_check.ifCorrect}}"
                   data-inner-action-incorrect="{{=button.innerTargetSection_check.ifIncorrect}}"
               {{?}}
               {{?it.DB[it.isFollower]==1}}disabled{{?}}>
               
               <i class="medium material-icons {{?button.type==='previous'}}left{{??}}right{{?}}">{{=button.icon}}</i>{{?button.label}}{{=button.label}}{{?}}
           </a>
           {{??}}
           <!-- Float when screen is small -->
           <a class="hide-on-med-and-up btn-floating btn-large {{?button.pulse}}pulse{{?}} client-colors button
                    {{?it.DB[it.isFollower]==1}}follower{{?}} {{?button.type==='modal'}}modal-trigger button2{{?}}" 
               data-position="bottom" 
               {{?button.type==="previous"}}data-previousAction{{?}}
               {{?button.type==="next"}}data-nextAction{{?}}
               {{?button.popupID}}href="#{{=button.popupID}}" data-modal="{{=button.popup}}"{{?}}
               {{?button.gdActionCommand}}data-gdcommand="{{=button.gdActionCommand}}" data-gdcommandxml="{{=button.gdActionCommandXML}}"{{?}}
               {{?button.gdActionEmbed}}data-gdembed="{{=button.gdActionEmbed}}" data-index="{{=idx}}"{{?}}
               {{?button.gdActionSection}}data-gdaction="{{=button.gdActionSection}}"{{?}}
               {{?button.gdActionTrack}}data-gdtrack="{{=button.gdActionTrack}}"{{?}}
               {{?button.targetSection}}data-action="{{=button.targetSection}}"{{?}}
               {{?button.conditionTarget && it.DB[button.conditionTarget] && button.conditionalTarget}}
                   data-action="{{=button.conditionalTarget[it.DB[button.conditionTarget]]}}"
               {{?}}
               {{?button.innerTargetSection}}data-inner-action="{{=button.innerTargetSection}}"{{?}}
               {{?button.innerTargetSection_check}}
                   data-inner-action-check
                   data-inner-action-correct="{{=button.innerTargetSection_check.ifCorrect}}"
                   data-inner-action-incorrect="{{=button.innerTargetSection_check.ifIncorrect}}"
               {{?}}
               {{?it.DB[it.isFollower]==1}}disabled{{?}}>
               
               <i class="medium material-icons {{?button.type==='previous'}}left{{??}}right{{?}}">{{=button.icon}}</i>
           </a>
           <!--  Square button when screen is medium/large (btn-navigation class makes small when medium-screen and large when big-screen)-->
           <a class="hide-on-small-only {{?button.isLarge}}btn-large{{??}}btn{{?}} 
                     {{?button.pulse}}pulse{{?}} client-colors button {{?button.tooltip}}tooltipped tnavigation{{?}}
                     {{?it.DB[it.isFollower]==1}}follower{{?}} {{?button.type==='modal'}}modal-trigger button2{{?}}" 
               data-position="bottom" {{?button.tooltip}}data-tooltip="{{=button.tooltip}}"{{?}} 
               {{?button.type==="previous"}}data-previousAction{{?}}
               {{?button.type==="next"}}data-nextAction{{?}}
               {{?button.popupID}}href="#{{=button.popupID}}" data-modal="{{=button.popup}}"{{?}}
               {{?button.gdActionCommand}}data-gdcommand="{{=button.gdActionCommand}}" data-gdcommandxml="{{=button.gdActionCommandXML}}"{{?}}
               {{?button.gdActionEmbed}}data-gdembed="{{=button.gdActionEmbed}}" data-index="{{=idx}}"{{?}}
               {{?button.gdActionSection}}data-gdaction="{{=button.gdActionSection}}"{{?}}
               {{?button.gdActionTrack}}data-gdtrack="{{=button.gdActionTrack}}"{{?}}
               {{?button.targetSection}}data-action="{{=button.targetSection}}"{{?}}
               {{?button.conditionTarget && it.DB[button.conditionTarget] && button.conditionalTarget}}
                   data-action="{{=button.conditionalTarget[it.DB[button.conditionTarget]]}}"
               {{?}}
               {{?button.innerTargetSection}}data-inner-action="{{=button.innerTargetSection}}"{{?}}
               {{?button.innerTargetSection_check}}
                   data-inner-action-check
                   data-inner-action-correct="{{=button.innerTargetSection_check.ifCorrect}}"
                   data-inner-action-incorrect="{{=button.innerTargetSection_check.ifIncorrect}}"
               {{?}}
               {{?it.DB[it.isFollower]==1}}disabled{{?}}>
               
               <i class="medium material-icons {{?button.type==='previous'}}left{{??}}right{{?}}">{{=button.icon}}</i>{{?button.label}}{{=button.label}}{{?}}
           </a>
           {{?}}
       </span> 
        {{~}} {{?}}
    </div>
    
    {{?it.buttons}} {{~it.buttons :button:idx}}
            {{?button.gdActionEmbed}}
            <div class="embeddingWrapper" id="embedding-{{=idx}}" data-embed></div>
            {{?}} 
        {{~}} {{?}}
</div>




<!-- **************************** -->
<!-- *****  MODALS screens  ***** -->
<!-- **************************** -->
{{?it.hasModals}} {{~it.buttons :button}} {{?button.popupID}}
<div id="{{=button.popupID}}" class="modal large">
    <div class="modal-content"> </div>
    <div class="modal-footer">
        <a class="btn modal-close client-colors button2">
            <i class="small material-icons right">close</i>{{=button.close}}
        </a>
    </div>
</div>
{{?}} {{~}} {{?}} {{?}}