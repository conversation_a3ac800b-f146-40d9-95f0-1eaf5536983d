
<!-- Progress Bar -->
{{=it.components[0]}}

<!-- Tabs header -->
{{=it.components[1]}}
    
<!-- Tabs panels (x4 components per each tab) -->
{{var cont=0; for (var idx = 2; idx < it.components.length; idx += 4) { }}
{{?it.components[idx+3]}}
    <div id="tabPanel{{=cont}}" class="col s12 tab-component">
        {{=it.components[idx]}}
        {{=it.components[idx+1]}}
        {{=it.components[idx+2]}}
        {{=it.components[idx+3]}}
    </div>
{{??}}
    {{?it.components[idx]}} {{=it.components[idx]}} {{?}}
    {{?it.components[idx+1]}} {{=it.components[idx+1]}} {{?}}
    {{?it.components[idx+2]}} {{=it.components[idx+2]}} {{?}}
{{?}}
{{ cont++}; }}  

