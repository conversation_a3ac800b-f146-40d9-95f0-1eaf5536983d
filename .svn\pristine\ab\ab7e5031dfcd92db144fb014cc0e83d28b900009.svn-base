@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";


.wizlet.wizletImagePopUp {


    .card {
        
        .card-content {
            @include card-title; 
        }

        .card-image {

            padding: 12px;

            img.background {
                object-fit: contain;
            }

            .image-holder {

                //padding: 20px;
                
                &:hover {
                    filter: saturate(200%);
                    img {
                        transform: scale(1.1);
                    }
                }

                img {    
                    cursor: pointer;
                    max-height: 30vh;
                    object-fit: contain;
                    filter: drop-shadow(2px 4px 6px black);
                }

                &.background {
                    position: absolute;
                }

                &.bounce {
                    @include animation-name(shakeInfinite);
                    @include animation-duration(10s);
                    @include animation-iteration-count(infinite);
                    @include animation-timing-function(ease);
                    @include animation-fill-mode(forwards);
                    @for $i from 1 through 10 {
                        &:nth-child(#{$i - 1}) {
                            @include animation-delay(#{$i}s);
                        }
                    } 
                }
            }

        }

    }

    
    .container.fullscreen {
        @mixin max-height {
            $footerHeight: 140px;
            max-height: calc(100vh - #{$footerHeight});
        }
        @mixin fit-content {
            width: fit-content;
            margin: auto;
        }
        
        @include max-height;
        width: 100%;
        max-width: 2560px;
        padding: 0;
        margin: auto;

        .card {
            @include fit-content;

            .card-image {
                @include max-height;
                @include fit-content;
                >img {
                    max-height: inherit;
                    width: auto;
                    margin: auto;
                }
            }
        }
    }
    
}

