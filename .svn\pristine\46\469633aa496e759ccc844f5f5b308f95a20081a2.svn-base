<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
      <Question>Filter_Agg</Question>
    </ParticipantFilter>


    <!-- ###### -->
    <!-- SCORES -->
    <!-- ###### -->

    <Score type="Vote" result="Score_SIM_Init_LTUs_C1" Response="!{KPI_LTU_Init_C1}"/>
    <Score type="Vote" result="Score_SIM_Init_KPI1" Response="!{KPI_Metric1_Init}"/>
    <Score type="Vote" result="Score_SIM_Init_KPI2" Response="!{KPI_Metric2_Init}"/>
    <Score type="Vote" result="Score_SIM_Init_KPI3" Response="!{KPI_Metric3_Init}"/>

    <!-- LTUs -->
    <Score result="Score_SIM_R1_Scenario1_LTUs" type="Choice">
      <Question name="Q_SIM_R1_Scenario1">
        <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_LTUs}"></Choice>
        <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_LTUs}"></Choice>
        <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_LTUs}"></Choice>
        <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_LTUs}"></Choice>
      </Question>
    </Score>

    <!-- KPI1: Engagement -->
    <Score result="Score_SIM_R1_Scenario1_KPI1" type="Choice">
      <Question name="Q_SIM_R1_Scenario1">
        <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_KPI1}"></Choice>
        <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_KPI1}"></Choice>
        <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_KPI1}"></Choice>
        <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_KPI1}"></Choice>
      </Question>
    </Score>

    <!-- KPI2: Safety-->
    <Score result="Score_SIM_R1_Scenario1_KPI2" type="Choice">
      <Question name="Q_SIM_R1_Scenario1">
        <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_KPI2}"></Choice>
        <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_KPI2}"></Choice>
        <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_KPI2}"></Choice>
        <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_KPI2}"></Choice>
      </Question>
    </Score>

    <!-- KPI3: Driving Results-->
    <Score result="Score_SIM_R1_Scenario1_KPI3" type="Choice">
      <Question name="Q_SIM_R1_Scenario1">
        <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_KPI3}"></Choice>
        <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_KPI3}"></Choice>
        <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_KPI3}"></Choice>
        <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_KPI3}"></Choice>
      </Question>
    </Score>


    <!-- ###### -->
    <!-- TOTALS -->
    <!-- ###### -->

    <!-- LTUs -->
    <Total result="Score_SIM_Total_LTUs" method="sum">
      <Question validate="false">Score_SIM_Init_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_LTUs</Question>
    </Total>

    <!-- KPI1: Engagement -->
    <Total result="Score_SIM_Total_KPI1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI1</Question>
    </Total>

    <!-- KPI2: Safety-->
    <Total result="Score_SIM_Total_KPI2" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI2</Question>
    </Total>

    <!-- KPI3: Driving Results-->

    <!-- Penalty:
        - If MTUs are between -1 to -2 ==> Driving Results is impacted by: -1
        - If MTUs are between -3 to -4 ==> Driving Results is impacted by: -2
        - If MTUs are < -4 ==> Driving Results is impacted by: -3
      -->
    <Score type="Range" result="Score_SIM_R1_KPI3penalty">
      <Question>Score_SIM_Total_LTUs</Question>
      <Boundary value="-3">-4</Boundary>
      <Boundary value="-2">-2</Boundary>
      <Boundary value="-1">0</Boundary>
      <Boundary value="0">99</Boundary>
    </Score>

    <Total result="Score_SIM_Total_KPI3" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI3</Question>
      <Question validate="false">Score_SIM_R1_KPI3penalty</Question>
    </Total>

    <!-- KPI4: Preparing for the Future -->
    <Total result="Score_SIM_Total_KPI4" method="sum">
      <Question validate="false">Score_SIM_Init_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI4</Question>
    </Total>

    <!-- KPI: TOTAL -->
    <Total result="Score_SIM_Total" method="avg">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
      <Question validate="false">Score_SIM_Total_KPI2</Question>
      <Question validate="false">Score_SIM_Total_KPI3</Question>
      <Question validate="false">Score_SIM_Total_KPI4</Question>
    </Total>
    <Total result="Score_SIM_Total_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total</Question>
    </Total>

    <!-- ROUND1 - KPI: TOTAL -->
    <Total result="Score_SIM_Total_R1_LTUs" method="sum">
      <Question validate="false">Score_SIM_Total_LTUs</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI4" method="sum">
      <Question validate="false">Score_SIM_Total_KPI4</Question>
    </Total>
    <Total result="Score_SIM_Total_R1" method="sum">
      <Question validate="false">Score_SIM_Total</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total_R1</Question>
    </Total>

  </Aggregator>


</Action>