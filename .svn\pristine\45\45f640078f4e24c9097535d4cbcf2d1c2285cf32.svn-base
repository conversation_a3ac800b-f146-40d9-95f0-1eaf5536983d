@charset "UTF-8";
@import "../materialize-src/sass/components/color-variables";
@import "mixins";

.mainContent{
    text-align: center;
}
.pc{
    margin: 1em;
    width: 85%;
    display: inline-block;
    &-header{
        background: transparent;
        color: color("client-colors", "font");
        text-align: left;
        padding: 0;
        &__description{
            text-align: left;
            padding: 0;
            max-width: none;
        }
        h1{
            color: color("client-colors", "secondary");
            text-align: left;
        }
    }
    &-container{
        max-width: none;
        .cl-row{
            padding: 0;
        }
    }
    &-listright{
        width: calc( 100% - ( ( 1.65rem * 2.25 ) + 4px + 10px ) );
    }
    &-listleft{
        width: auto;
        display: flex;
        flex-flow: column;
        height: auto;
        align-items: end;
    }
    &-orderlistleftpanel{
        margin-right: 10px;
        text-align: center;
        font-size: 1.65rem !important;
        width: calc( 2.25em + 4px ) !important;
        height: calc( 2.25em + 4px ) !important;
        line-height: calc( 2.25em + 4px - 9px ) !important;
        border-radius: 10px;
        background-color: color("client-colors", "primary");
        //box-shadow: 2px 2px 3px rgba(0,0,0,0.15), 3px 3px 3px rgba(255,255,255,0.15) inset;
        //box-shadow: 2px 2px 3px rgba(0,0,0,0.15);
        border: solid 2px color("client-colors", "secondary");
    }
    &-rightpanel{
        text-align: left;
        cursor: grab;
        border: solid 2px color("client-colors", "secondary");
        border-radius: 10px;
        background-color: color("client-colors", "secondary");
        color: color("client-colors", "white");
        //box-shadow: 2px 2px 3px rgba(0,0,0,0.15), 3px 3px 3px rgba(255,255,255,0.15) inset;
        box-shadow: 2px 2px 3px rgba(0,0,0,0.15);
        &:active{
            cursor: grabbing;
            background-color: color("client-colors", "primary");
            border-color: color("client-colors", "primary");
        }
    }
    &-draggablecontainer{
        font-size: 1.65rem !important;
        width: 2.25em !important;
        height: 2.25em !important;
        background: none;
        background-color: color("client-colors", "white");
        border-bottom-right-radius: 100%;
    }
    &-draggablesign1,
    &-draggablesign2,
    &-draggablesign3{
        width: 15px;
        height: 3px;
        transform: rotate(-45deg);
        background: none;
        background-color: color("client-colors", "light-grey");
        border-radius: 1px;
    }
    &-draggablesign1{
        top: 12px;
        left: 6px;
    }
    &-draggablesign2{
        top: 13px;
        left: 10px;
    }
    &-draggablesign3{
        top: 14px;
        left: 14px;
    }
    &-clearbuttontext{
        display: none !important;
    }
    &-submitbutton{
        margin-top: 22px;
        color: color("client-colors", "font2");
        padding: 2px 6px;
        background-image: none;
        border: 2px solid;
        border-color: initial;
        border-radius: 5px;
        background-color: color("client-colors", "button2");
        text-align: center;
        letter-spacing: .5px;
        transition: background-color .2s ease-out;
        cursor: pointer;
        font-size: 14px;
        outline: 0;
        display: inline-block;
        height: 36px;
        line-height: 36px;
        text-transform: uppercase;
        vertical-align: middle;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 3px 1px -2px rgba(0, 0, 0, .12), 0 1px 5px 0 rgba(0, 0, 0, .2);
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        &:hover{
            color: color("client-colors", "secondary") !important;
            background-color: color("client-colors", "white") !important;
            span{
                color: color("client-colors", "secondary") !important;
            }
        }
        &:has(.pc-savedmessage){
            display: none !important;
        }
        span{
            background: transparent !important;
            padding: 0;
            margin: 0;
            height: 100%;
            &:before{
                display: none !important;
            }
        }
    }
}