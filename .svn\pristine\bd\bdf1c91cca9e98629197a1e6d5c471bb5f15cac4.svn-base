﻿<div class="container" {{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    
    {{?it.header}}
        {{?it.help && it.help.icon}}
            <div class="header-container">
                <h4 class="header help {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.header}}</h4>       
                <i class="material-icons help client-colors-text text-font2 {{?it.help.modalID}}modal-trigger" href="#{{=it.help.modalID}}{{?}}">{{=it.help.icon}}</i>
            </div>
        {{??}}
            <h4 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.header}}</h4>       
        {{?}}
    {{?}}
    {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
    
    
    {{?it.intro}}<div class="intro flow-text"><span>{{=it.intro}}</span></div>{{?}}
    
    {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

    {{?it.intro_collapsible}}
    <div class="row center no-padding">
        <div class="col s12">

            <ul class="collapsible">   
                {{~it.intro_collapsible.items :item:idx}}
                    <li data-index="{{=idx+1}}"
                        {{?it.DB[it.intro_collapsible.bind]}} 
                            {{?it.DB[it.intro_collapsible.bind]==(idx+1)}}class="active"{{?}}>
                            
                            <div class="collapsible-header flow-text">
                                <i class="expand material-icons large bottom">{{?it.DB[it.intro_collapsible.bind]==(idx+1)}}{{=it.intro_collapsible.expand_less}}{{??}}{{=it.intro_collapsible.expand_more}}{{?}}</i>

                        {{??}}
                            {{?item.active}}class="active"{{?}}>
                            
                            <div class="collapsible-header flow-text">
                                <i class="expand material-icons large bottom">{{?item.active}}{{=it.intro_collapsible.expand_less}}{{??}}{{=it.intro_collapsible.expand_more}}{{?}}</i>
                        {{?}}

                            <span class="strong">{{=item.title}}</span>
                        </div>
                        <div class="collapsible-body flow-text">                            
                            {{?item.image}}
                            <div class="card-image">
                                <img class="{{?item.image.src_vert}}hide-on-med-and-down{{?}} responsive-img" 
                                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=item.image.src}}" alt="{{=item.image.alt}}"/>

                                {{?item.image.src_vert}}
                                <img class="hide-on-large-only responsive-img" 
                                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=item.image.src_vert}}" alt="{{=item.image.alt}}"/>
                                {{?}}
                                
                            </div>
                            {{?}}
                            <span>{{=item.text}}</span>
                        </div>
                    </li>
                {{~}}
            </ul>

        </div>
    </div>
    {{?}}


    <div class="row center no-padding {{?it.isFlex}}is-flex{{?}}">
        
        {{~it.cards :card:idx}}
        <div class="col {{=it.grid}}" 
            {{?card.hidden}}
                {{?card.condition}}
                    {{?!it.DB[card.condition] || (it.DB[card.condition]!=card.condition_Val)}}
                        hidden
                    {{?}}
                {{??}}
                    hidden
                {{?}}
            {{?}}> 
                
            <div class="card maincard hoverable {{?card.reveal}}sticky-action{{??}}no-reveal{{?}} {{?it.size}}{{=it.size}}{{?}} 
                        
                {{?!card.enabled}}
                    {{?card.condition}}
                        {{?!it.DB[card.condition] || (it.DB[card.condition]!=card.condition_Val)}}
                            disabled
                        {{?}}
                    {{??}}
                        disabled
                    {{?}}
                {{?}}"> 
            
                {{?card.newBadge}}<span class="new badge header-label" data-badge-caption="{{=card.newBadge}}"></span>{{?}}

                {{?card.href && !card.reveal}}
                    {{?card.href_condition && it.DB[card.href_condition] && card.hrefs[it.DB[card.href_condition]]}}
                    <a href="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=card.hrefs[it.DB[card.href_condition]]}}"{{?card.download}}download{{??}}target="_blank"{{?}}>
                    {{??}}
                    <a href="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=card.href}}"{{?card.download}}download{{??}}target="_blank"{{?}}>
                    {{?}}
                {{?}}

                <div class="card-image {{?!card.reveal && card.modal && card.enabled}}modal-trigger{{?}} {{?card.onHover}}onHover{{?}}"
                    {{?!card.reveal}} 
                        {{?card.jump && (card.enabled || (card.condition && it.DB[card.condition] && it.DB[card.condition]==card.condition_Val))}}
                            data-action="{{=card.jump}}"
                        {{?}}
                        {{?card.modal && card.enabled}}
                            href="#{{=card.modal.popupID}}" data-modal="{{=card.modal.popup}}"
                        {{?}}
                        {{?card.autoNavigation}}data-auto-bind="{{=card.autoNavigation.bind}}" data-auto-target="{{=card.autoNavigation.targetName}}"{{?}}
                    {{?}}>

                    {{?card.img}}
                    <img class="activator" {{?it.maxHeight}}style="max-height:{{=it.maxHeight}}"{{?}}
                        {{?card.img.conditional && card.img.conditional.bind && it.DB[card.img.conditional.bind]}}
                            src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=card.img.conditional.srcs[it.DB[card.img.conditional.bind]-1]}}" 
                        {{??}}
                            src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=card.img.src}}" 
                        {{?}}
                        alt="{{=card.img.alt}}"/>
                    {{?}}
                </div>

                {{?card.title}}
                <div class="card-content {{?!card.reveal && card.modal && card.enabled}}modal-trigger{{?}}" {{?it.contentMinHeight}}style="min-height: {{=it.contentMinHeight}};"{{?}}
                     {{?!card.reveal}}
                        {{?card.jump && (card.enabled || (card.condition && it.DB[card.condition] && it.DB[card.condition]==card.condition_Val))}}
                            data-action="{{=card.jump}}"
                        {{?}}
                        {{?card.modal && card.enabled}}
                            href="#{{=card.modal.popupID}}" data-modal="{{=card.modal.popup}}"
                        {{?}}
                        {{?card.autoNavigation}}data-auto-bind="{{=card.autoNavigation.bind}}" data-auto-target="{{=card.autoNavigation.targetName}}"{{?}}
                    {{?}}>
                    <span class="card-title {{?card.reveal}}activator{{?}} {{?it.titleSize}}size {{=it.titleSize}}{{?}}">{{=card.title}}
                        {{?card.reveal && (card.enabled || (card.condition && it.DB[card.condition] && it.DB[card.condition]==card.condition_Val))}}
                            <i class="material-icons right">unfold_more</i>
                        {{?}}
                    </span>
                </div> 
                {{?}}           
                
                
                {{?card.href && !card.reveal}}
                </a>
                {{?}}

                {{?card.link}}
                <div class="card-action {{?card.modal && card.enabled}}modal-trigger{{?}}"
                    {{?card.jump && (card.enabled || (card.condition && it.DB[card.condition] && it.DB[card.condition]==card.condition_Val))}}
                        data-action="{{=card.jump}}"
                    {{?}}
                    {{?card.modal && card.enabled}}
                        href="#{{=card.modal.popupID}}" data-modal="{{=card.modal.popup}}"
                    {{?}}
                    {{?card.autoNavigation}}data-auto-bind="{{=card.autoNavigation.bind}}" data-auto-target="{{=card.autoNavigation.targetName}}"{{?}}>

                    <a class="{{?card.tooltip}}tooltipped tcard{{?}}" {{?card.tooltip}}data-tooltip="{{=card.tooltip}}"{{?}}

                        {{?card.href}}
                            {{?card.href_condition && it.DB[card.href_condition] && card.hrefs[it.DB[card.href_condition]]}}
                            href="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=card.hrefs[it.DB[card.href_condition]]}}"{{?card.download}}download{{??}}target="_blank"{{?}}
                            {{??}}
                            href="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=card.href}}"{{?card.download}}download{{??}}target="_blank"{{?}}
                            {{?}}
                        {{?}}>

                        
                        {{?card.enabled || (card.condition && it.DB[card.condition] && it.DB[card.condition]==card.condition_Val)}}
                            
                            {{?card.link_condition && it.DB[card.link_condition] && card.links[it.DB[card.link_condition]]}}
                                {{=card.links[it.DB[card.link_condition]]}}
                            {{??}}
                                {{=card.link}}
                            {{?}}
                            <i class="material-icons small bottom">{{?card.icon}}{{=card.icon}}{{??}}forward{{?}}</i>
                        {{??}}
                            {{=it.link_disabled}}
                            <i class="material-icons small bottom">av_timer</i>
                        {{?}}
                    </a>
                </div>
                {{?}}
                
            
                {{?card.reveal}}
                <div class="card-reveal">
                    <span class="card-title reveal">{{=card.title}}<i class="material-icons right">close</i></span>
                    <span>{{=card.body}}</span>
                </div>
                {{?}}

            </div>
        </div>
        {{~}}

    </div>


    

</div>





<!-- ********************************** -->
<!-- *********  MODAL-POPUPS  ********* -->
<!-- ********************************** -->
{{~it.cards :card:idx}}
    {{?card.modal && card.enabled}}
    <div id="{{=card.modal.popupID}}" class="modal large">
        <div class="modal-content"> </div>
        <div class="modal-footer">
            <a class="btn modal-close client-colors button2">
                <i class="small material-icons right">close</i>{{=card.modal.close}}
            </a>
        </div>
    </div>
    {{?}}
{{~}}
