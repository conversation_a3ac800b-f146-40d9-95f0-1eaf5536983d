<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <!-- Need to use Card.js to apply animation animateLater to the image which already has an animated_animate one -->
  <!-- <Component type="Card" customJS="true"> -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{SIM_R1_CaseStudy_Header}",
      valign: false,
      animate: "fadeInLeft",
      transparentBox: false,
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_CaseStudy1_Image}",  alt: "!{SIM_R1_CaseStudy_Header}" ,
          position: "left large",
          src_vert: "!{}",
          animate: "fadeInLeft animate__delay-1s", _animateLater: "bounce"
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{SIM_R1_CaseStudy1_Title}",
        body: "!{SIM_R1_CaseStudy1_Text}"
      }
    }]]>  
  </Component>

</Action>