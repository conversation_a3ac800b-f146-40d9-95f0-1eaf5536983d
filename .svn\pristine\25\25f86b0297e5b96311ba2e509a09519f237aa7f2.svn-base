@charset "UTF-8";


@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    
    src: url("fonts/MaterialIcons-Regular.eot"); /* For IE6-8 */
    src: local('Material Icons'),
         local('MaterialIcons-Regular'),
         url("fonts/MaterialIcons-Regular.woff2") format('woff2'),
         url("fonts/MaterialIcons-Regular.woff") format('woff'),
         url("fonts/MaterialIcons-Regular.ttf") format('truetype');
}

    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        @include font-feature-settings ('liga');
        @include font-smoothing ();

        &.bottom {
            vertical-align: bottom;
        }
    }

@font-face {
    font-family: clientRegular;
    // src: url("fonts/ClientRegular.ttf") format('truetype'),
    src: url("fonts/ClientRegular.woff") format('woff');
}
@font-face {
    font-family: clientLight;
    // src: url("fonts/ClientLight.ttf") format('truetype'),
    src: url("fonts/ClientLight.woff") format('woff');
}
@font-face {
    font-family: clientBold;
    // src: url("fonts/ClientBold.ttf") format('truetype'),
    src: url("fonts/ClientBold.woff") format('woff');
}
@font-face {
    font-family: clientBolder;
    // src: url("fonts/ClientBolder.ttf") format('truetype'),
    src: url("fonts/ClientBolder.woff") format('woff');
}

@font-face {
    font-family: 'fa-regular-400';
    src: url("fonts/fa-regular-400.eot");
    src: url("fonts/fa-regular-400.eot?#iefix") format("embedded-opentype"), url("fonts/fa-regular-400.woff2") format("woff2"), url("fonts/fa-regular-400.woff") format("woff"), url("fonts/fa-regular-400.ttf") format("truetype"), url("fonts/fa-regular-400.svg#fontawesome") format("svg");
  }
  @font-face {
    font-family: 'fa-solid-900';
    src: url("fonts/fa-solid-900.eot");
    src: url("fonts/fa-solid-900.eot?#iefix") format("embedded-opentype"), url("fonts/fa-solid-900.woff2") format("woff2"), url("fonts/fa-solid-900.woff") format("woff"), url("fonts/fa-solid-900.ttf") format("truetype"), url("fonts/fa-solid-900.svg#fontawesome") format("svg");
  }

h4.strong, h5.strong, h6.strong, span.strong {
    font-family: clientBold, Arial;
}

h1.light, h2.light, h3.light, h4.light, h5.light, span.light,
h6.instructions, span.light {
    font-family: clientLight, Arial;
}

// h4.header, h5.header {
//     color: color("client-colors", "font2");    
// }
h6.instructions {
    //color: color("client-colors", "font3");
    //color: color("client-colors", "secondary");
    color: color("client-colors", "tertiary");
}

span.tiny { font-size: 50%; }
span.small { font-size: 75%; }
span.big { font-size: 125%; }

span.word-wrap { word-wrap: break-word; }

span.tiny, span.small, span.big { line-height: 100%; }

span.highlight, bh { 
    font-weight: bold;
    //color: color("client-colors", "font3");
    color: color("client-colors", "secondary");
}
u {
    text-decoration-color: color("client-colors", "font3");
}

.header > span.highlight { 
    filter: drop-shadow(1px 1px 1px color("client-colors", "font2"));
}


div.centered {
    text-align: center;
}
div.lefted {
    text-align: left;
}
div.righted {
    text-align: right;
}