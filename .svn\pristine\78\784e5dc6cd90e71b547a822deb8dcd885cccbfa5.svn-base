<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro_SIM">
  <Include name="Header_Intro"></Include>

  
  <!-- Vanilla component (no JS needed)
       Basic card component to show info -->
  <Component type="Vanilla" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{SIM_R2_Summary_Header}",
      instructions: "!{}",    
      valign: false,
      animate: "fadeIn",
      content: {
        img: { 
          materialboxed: false, 
          src: "!{SIM_R2_Summary_Image}",  alt: "!{SIM_R2_Summary_Title}" 
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{SIM_R2_Summary_Text1}",
        body2: "!{}"
      } 
    }]]>
  </Component>
  

  <!-- Metrics Table and WebChart -->
  <Include name="SIM_R2_Summary_included"></Include>


  <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "zoomIn animate__delay-2s",
      content: {
        title: "!{}",
        body: "!{SIM_R2_Summary_Text2}"
      }
    }]]>  
  </Component>

  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>

  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <!-- <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      _isHidden: true, _showDelay: "3000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_finish}",
          tooltip: "!{Navigation_finish_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component> -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, _showDelay: "3000",
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: true,
          popupID: "Modal_menu",
          popup: "SIM_R2_Summary_modal",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_summary}",
          icon: "zoom_in"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R2_Summary}"/>     
    <Score type="TimeStamp" result="Q_FINISH_DATE_R2" keepFirst="false"/> 
  </Voting>


</Action>