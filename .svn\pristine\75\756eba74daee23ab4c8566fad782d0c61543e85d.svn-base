<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayout">
  <Include name="Header"></Include>
      
      
 <Component type="RadioButtons" customJS="true">
    <![CDATA[{
      templateInEvent: "html/radioButtons.dot",
      css: "styles/radioButtons.css",
      id: "radio1",
      isHidden: false,
      animate: "fadeInLeft",
      headerLabel: "!{}",
      header: "!{Extra_YesNoQuestion_Radio_Header}",
      instructions: "!{Extra_YesNoQuestion_Radio_Instruction}",
      titleLabel: "!{}",
      title: "!{}",
      subtitle: "",
      _class: "no-separator reduced",
      inputclass: "with-gap",
      groupName: "radioGroup",
      preloadSaved: true,
      bind: "Q_Extra_YesNoQuestion1_Radio",
      moreInfo: "!{Choice_MoreInfo}",
      isCheck: false,
      
      noBlockAnswer: false,
      turnoffAnswer: false,
      
      _optionsFromVote: "Q_Title_Input",
      _moreInfoFromVote: "Q_Desc_Input",
      _includeMyVote: false,

      isInline: true,
      optionWidth: "s6 big",
      options: [
        {
          value: "1",
          label: "!{}",
          icon: "thumb_up",
          title: "!{Extra_YesNoQuestion_Radio_Opt1}",
          titleClass:"title-align-items-center client-colors-text text-green",
          moreInfo: "!{}",
          isCorrect: true
        },
        {
          value: "2",
          label: "!{}",
          icon: "thumb_down",
          title: "!{Extra_YesNoQuestion_Radio_Opt2}",
          titleClass:"title-align-items-center client-colors-text text-red",
          moreInfo: "!{}",
          isCorrect: false
        }
      ],
      
      autocheck: false,
      checkBtn: {
        label: "!{Choice_btnCheck}",
        animate: "slideInUp",
        _toast: { 
          ifCorrect: "!{Choice_toastCorrect}",
          ifIncorrect: "!{Choice_toastIncorrect}",
          points: "!{}"
        },
        toastMsg: "!{InputSubmited}",
        idToShow: "",
        scrollToDown: false
      },
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower"]     
    }]]>
  </Component>


</Action>