﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var ActionMenu = function () {
        this.type = 'ActionMenu';
        this.level = 1;
    };

    ActionMenu.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.votesBeforeUpdate = [];

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    ActionMenu.prototype.unloadHandler = function () {
        $('.material-tooltip:not(.timage)').remove();
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };


    ActionMenu.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
                        
            options.context.find("[data-action]").off("click").on("click", function (e) {
                wizerApi.jump($(this).data("action"), false);
            });

            //Groupd Director Action: jump participant to a section
            options.context.find("[data-gdaction]").on("click", function (e) {
                
                var question = $(this).data("gdtrack") ? $(this).data("gdtrack") : 'GD';

                var waitForActionId = self.wizerApi.lookingUpActionIdIfNotNumeric( $(this).data("gdaction") );
                waitForActionId.then(function (actionId) {
                    AjaxGetJson('Vote', 'SetGroupCurrentActionId', 'trackQuestionId=' + self.wizerApi.getQuestionIdByName(question) + '&actionId=' + actionId, function (result) {
                        if (!result.actionId) {
                            console.log('Fail in SetGroupCurrentActionId');
                        }
                    });
                });
                
            });
            
            //Groupd Director Action: Embed action (as aggregation)
            options.context.find("[data-gdembed]").on("click", function (e) {
                
                var embedFile = $(this).data("gdembed");
                var embedIdx = $(this).data("index");
                
                if (embedFile) {
                    
                    var optionsObj = {actionXML: embedFile};
                    var embedding = require('wizletEmbedding');
                    embedding.unembedWizletExcept('MenuComponent');
                    optionsObj.selector = '#embeded-'+embedIdx;
                    optionsObj.noscroll = true;
                    embedding.embedWizlet({
                        context: $(document),
                        actionScriptName: embedFile,
                        options: optionsObj
                    });
                }
                else {
                    self.wizerApi.showError('actionXML attribute missing from options');
                }
                
            });


            //Groupd Director Action: Command action (as EmbedWizlet)
            options.context.find("[data-gdcommand]").on("click", function (e) {
                
                var command = $(this).data("gdcommand");
                var embedFile = $(this).data("gdcommandxml");
                                console.log(command)
                                console.log(embedFile)
                if (embedFile) {
                    var question = $(this).data("gdtrack") ? $(this).data("gdtrack") : 'GD';
                    var optionsObj = {actionName: embedFile};
                    AjaxGetJson('Vote', 'SendClientCommandToGroup', 'trackQuestionId=' + self.wizerApi.getQuestionIdByName(question) + '&cmd=' + command + '&options=' + JSON.stringify(optionsObj), function (result) {
                        if (!result.success) {
                            console.log('Fail in SendClientCommandToGroup');
                        }
                    });
                }
                else {
                    self.wizerApi.showError('actionName attribute missing from options');
                }
                              
            });


            if (options.wizletInfo.highlightActiveScreen) {

                var screen = $('body').attr('class');
                screen = screen.indexOf(" ") > -1 ? 
                            screen.substr(screen.indexOf("body")+4, screen.indexOf(" ")) :
                            screen.substr(screen.indexOf("body")+4, screen.length)

                options.context.find('.collection-item a[data-action='+screen+'').parent().addClass('active')
                    
            }



            
            options.context.find('.tooltipped.tmenu').tooltip( {
                enterDelay: 1000
            });

            
            //Enable zoom images
            options.context.find('img.materialboxed').materialbox({
                //Hide-show floating buttons over the zoomed image
                onOpenStart : function(current_item) {
                    if (options.wizletInfo.image.classToHide)
                        $(options.wizletInfo.image.classToHide).hide();
                },
                onCloseStart : function(current_item) {
                    if (options.wizletInfo.image.classToHide)
                        $(options.wizletInfo.image.classToHide).show();
                }
            });


            return true;
        })
        .fail(this.wizerApi.showError);
    };



    ActionMenu.prototype.addVote = function (questionName, val) {
        var self = this;
        var questionId = self.wizerApi.getQuestionIdByName(questionName)
        self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});
    };

    
    ActionMenu.getRegistration = function () {
        return new ActionMenu();
    };

    return ActionMenu;

});