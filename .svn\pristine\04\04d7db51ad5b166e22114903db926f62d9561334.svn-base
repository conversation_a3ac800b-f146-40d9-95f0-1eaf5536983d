<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC" layout="../../../layout/tabsLayout">
  <Include name="HeaderFAC"></Include>
      
  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{Extra_YesNoQuestion_Radio_Header}",
      swipeable: false,
      tabs: [
          "!{Header_LinkResults}",
          "!{Header_LinkChoices}"
      ],
      scope: null
  }]]></Component>
      
      
  <Component type="HCPieChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCPieChart.dot",
    css: "styles/HCPieChart.css",
    class: "yesno",
    header: "!{}",
    instructions: "!{RadioInstructionsAnswers}",    
    isAnswersDistribution: true,
    listenModel: true,
    questions: [
      {
        binding: "Q_Extra_YesNoQuestion4_Radio"
      }
    ],
    trackQuestion: "Show_charts",
    chartConfig: {
      chart: {    
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        _height: '65%',
        type: "pie"
      },
      title: {
        text: "!{GD_Extra_YesNoQuestion4_Radio}"
      },
      subtitle: {
        text: ""
      },
      tooltip: {
        headerFormat: "",
        pointFormat: "{point.name}: <b>{point.y}</b> ({point.percentage:.1f}%)"
      },
      legend: {
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        pie: {
          size: "100%", 
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            useHTML: false,
            format: '<b>{point.percentage:.1f}</b> %',
            distance: -50,
            filter: {
                property: 'percentage',
                operator: '>',
                value: 1
            }
          },
          showInLegend: true
        }
      },
      series: [ 
        { 
          name: "",
          colorByPoint: true,
          data: [
            {
              name: "!{Extra_YesNoQuestion_Radio_Opt1}"
            },
            {
              name: "!{Extra_YesNoQuestion_Radio_Opt2}"
            }
          ]
        }
      ]
    },
    scope: [ ],    
    answers: "!{Results_VotesLabel}"
  }]]></Component>

  


  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    instructions: "!{RadioInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "noBold",
    questions: [
      {
        show: true,
        title: "!{GD_Extra_YesNoQuestion4_Radio}",
        binding: "Q_Extra_YesNoQuestion4_Radio"
      }
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 0,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>


  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "Extra_YesNoQuestion3_Radio",
          targetSection: "Extra_YesNoQuestion3_Piechart_FAC",
          label: "!{GD_Extra_YesNoQuestion3_Radio}",
          icon: "thumbs_up_down"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "Extra_YesNoQuestion5_Radio",
          targetSection: "Extra_YesNoQuestion5_Piechart_FAC",
          label: "!{GD_Extra_YesNoQuestion5_Radio}",
          icon: "thumbs_up_down"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


</Action>