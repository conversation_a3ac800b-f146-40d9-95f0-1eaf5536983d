<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC" layout="../../../layout/tabsLayoutX2">
  <Include name="HeaderFAC"></Include>


  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{R1_DebriefPage_FAC_Header}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{R1_DebriefPage_FAC_tab1}",
          "!{R1_DebriefPage_FAC_tab1b}",
          "!{R1_DebriefPage_FAC_tab1c}",
          "!{R1_DebriefPage_FAC_tab2}",
          "!{R1_DebriefPage_FAC_tab3}",
          "!{R1_DebriefPage_FAC_tab4}",
          "!{R1_DebriefPage_FAC_ranking}"
      ],
      scope: null
  }]]></Component>

      

<!-- ******* -->
<!-- EVENT 1 -->
<!-- ******* -->    
  <Include name="R1_Debrief_Scenario1_chart"></Include>  
  <Include name="R1_Debrief_Scenario1_results"></Include>
  
  <Include name="R1_Debrief_Scenario1b_chart"></Include>  
  <Include name="R1_Debrief_Scenario1b_results"></Include>

  <Include name="R1_Debrief_Scenario1c_chart"></Include>  
  <Include name="R1_Debrief_Scenario1c_results"></Include>

<!-- ******* -->
<!-- EVENT 2 -->
<!-- ******* -->    
  <Include name="R1_Debrief_Scenario2_chart"></Include>  
  <Include name="R1_Debrief_Scenario2_results"></Include>




<!-- ******* -->
<!-- EVENT 3 -->
<!-- ******* -->    
  <Include name="R1_Debrief_Scenario3_chart"></Include>  
  <Include name="R1_Debrief_Scenario3_results"></Include>
  



<!-- ******* -->
<!-- EVENT 4 -->
<!-- ******* -->    
  <Include name="R1_Debrief_Scenario4_chart"></Include>  
  <Include name="R1_Debrief_Scenario4_results"></Include>




<!-- ******* -->
<!-- RANKING -->
<!-- ******* -->

  <Include name="SIM_Ranking_included"></Include>

  
  
  
  
  <!-- EXPORT CSV BUTTON (adding empty component to get the Export button out of the tabs layout which goes in pairs)-->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css"    
    }]]>
  </Component>
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "bounceInUp",
      id: "btn_export",
      isHidden: false, _showDelay: 3000,
      title: "!{GroupDirector_ExportBtn_R1}",
      icon: "cloud_download",
      onclick: "",
      pulse: true,
      color: "aux1",
      modal: {
        modalID: "modal-export",
        header: "!{GroupDirector_Export_Modal_Title}",
        text: "!{GroupDirector_Export_Modal_Text}",
        close: "!{GroupDirector_Export_Modal_Close}",
        action: "!{GroupDirector_Export_Modal_Action}",
        onclick: "",
        onclickFunction: "dataExport",
        onclickQuestion: "GD",
        onclickQuestion2: "Data Report SIM-R1"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



  <Include name="ScrollToButton"></Include>



  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - showDelay: show the hidden button after Xms (waiting the animation)
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "center",
      animate: "zoomIn animate__delay-2s",
      id: "btn_navigation_home",
      _isHidden: true, _showDelay: 1000,
      hasModals: true,
      _buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "R2_LandingPage",
          targetSection: "R2_LandingPage_FAC",
          label: "!{GD_R2_LandingPage}",
          icon: "screen_share"
        }
      ],
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>




</Action>