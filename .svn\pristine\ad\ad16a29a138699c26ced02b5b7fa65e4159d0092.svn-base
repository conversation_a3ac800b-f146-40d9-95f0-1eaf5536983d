.wizlet.wizletCheckBoxes .header-container{position:relative}.wizlet.wizletCheckBoxes .header-container .header.help{padding-right:36px}.wizlet.wizletCheckBoxes .header-container i.help{position:absolute;right:0;bottom:0;cursor:pointer}.wizlet.wizletCheckBoxes .header-container i.help:hover{-webkit-transform:scale(1.3);-ms-transform:scale(1.3);-o-transform:scale(1.3);transform:scale(1.3)}.wizlet.wizletCheckBoxes .container.borders{border-width:10px;border-color:#c7538f;border-radius:10px;float:none !important}.wizlet.wizletCheckBoxes .container.borders.top{border-top-style:double}.wizlet.wizletCheckBoxes .container.borders.right{border-right-style:double}.wizlet.wizletCheckBoxes .container.borders.left{border-left-style:double}.wizlet.wizletCheckBoxes .container.borders.bottom{border-bottom-style:double;margin-bottom:50px}.wizlet.wizletCheckBoxes .header.with-label{position:relative;padding-left:2.5rem;margin-bottom:1rem}.wizlet.wizletCheckBoxes .header.with-label .badge.header-label{position:absolute;left:0;top:0;background-color:#00aab4;min-width:2rem;min-height:2rem;font-size:1.25rem;font-weight:bold;border-radius:50%;line-height:2rem;margin:0;margin-top:-5px}.wizlet.wizletCheckBoxes .card .card-content{background-color:#fff}.wizlet.wizletCheckBoxes .card .card-content .row{margin:0}.wizlet.wizletCheckBoxes .card .card-content .header.with-label{position:relative;padding-left:2.5rem;margin-bottom:1rem}.wizlet.wizletCheckBoxes .card .card-content .header.with-label .badge.header-label{position:absolute;left:0;top:0;background-color:#00aab4;min-width:2rem;min-height:2rem;font-size:1.25rem;font-weight:bold;border-radius:50%;line-height:2rem;margin:0;margin-top:-5px}.wizlet.wizletCheckBoxes .card .card-content .header{margin-top:0}.wizlet.wizletCheckBoxes .card .card-content.card-content{background-color:#fff;border-radius:10px}.wizlet.wizletCheckBoxes .card .card-content .card-title{font-family:clientBolder,clientBold,Arial;font-size:150%;line-height:100%;text-align:left;padding-bottom:5px;border-bottom:3px solid #c7538f;margin-bottom:15px}.wizlet.wizletCheckBoxes .card .card-content .card-title.activator,.wizlet.wizletCheckBoxes .card .card-content .card-title.reveal{position:relative}.wizlet.wizletCheckBoxes .card .card-content .card-title.activator i.material-icons.right,.wizlet.wizletCheckBoxes .card .card-content .card-title.reveal i.material-icons.right{position:absolute;top:0;right:-5px}.wizlet.wizletCheckBoxes .card .card-content .embed{clear:both;width:40%}@media only screen and (max-width : 600px){.wizlet.wizletCheckBoxes .card .card-content .embed{width:100%}}.wizlet.wizletCheckBoxes .card .card-content .embed.left{float:left;margin-right:12px}.wizlet.wizletCheckBoxes .card .card-content .embed.right{float:right;margin-left:12px}.wizlet.wizletCheckBoxes .card .card-content .embed.xtiny{width:10%}.wizlet.wizletCheckBoxes .card .card-content .embed.tiny{width:20%}.wizlet.wizletCheckBoxes .card .card-content .embed.small{width:30%}.wizlet.wizletCheckBoxes .card .card-content .embed.large{width:50%}.wizlet.wizletCheckBoxes .card .card-content .embed.extralarge{width:70%}.wizlet.wizletCheckBoxes form>ul .subheader{color:#031794;width:100%;clear:both}.wizlet.wizletCheckBoxes form>ul>li{padding:10px 5px;margin:0;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer}.wizlet.wizletCheckBoxes form>ul>li:not(:last-child){border-bottom:1px solid #c7538f}.wizlet.wizletCheckBoxes form>ul>li.chosen .hoverable{-webkit-box-shadow:0 8px 17px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);box-shadow:0 8px 17px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.wizlet.wizletCheckBoxes form>ul>li.chosen label .question-label{background-color:#ff6e00}.wizlet.wizletCheckBoxes form>ul>li label span.question-label{color:#fff;position:absolute;left:0;top:0;-webkit-transform:translateX(-75%) translateY(-25%);-ms-transform:translateX(-75%) translateY(-25%);-o-transform:translateX(-75%) translateY(-25%);transform:translateX(-75%) translateY(-25%);padding:0 4px;min-width:2rem;background-color:#00aab4}@media only screen and (max-width : 600px){.wizlet.wizletCheckBoxes form>ul>li label span.question-label{-webkit-transform:translateX(-110%) translateY(-25%);-ms-transform:translateX(-110%) translateY(-25%);-o-transform:translateX(-110%) translateY(-25%);transform:translateX(-110%) translateY(-25%);min-width:1.5rem}}.wizlet.wizletCheckBoxes form>ul>li label span.title{color:#4d4d4d}.wizlet.wizletCheckBoxes form>ul>li label span.title.title-align-items-center{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.wizlet.wizletCheckBoxes form>ul>li label span.title.with-image .option.left{padding-left:0}.wizlet.wizletCheckBoxes form>ul>li label span.title.with-image .option.right{padding-right:0}.wizlet.wizletCheckBoxes form>ul>li label span.title.with-image .image img{margin-top:-10px}.wizlet.wizletCheckBoxes form>ul>li label span.title .col.image{padding:0}@media only screen and (max-width : 600px){.wizlet.wizletCheckBoxes form>ul>li label span.title .col.image>img{max-height:3rem}}.wizlet.wizletCheckBoxes form>ul>li:hover label{color:#ff6e00}.wizlet.wizletCheckBoxes form>ul>li:hover [type=checkbox]+span:after{border-color:#ff6e00}.wizlet.wizletCheckBoxes form>ul>li.no-separator{border-bottom:none}.wizlet.wizletCheckBoxes form>ul>li.reduced{padding-top:2px;padding-bottom:0px}.wizlet.wizletCheckBoxes form>ul>li.reduced .card-panel span.question-label{-webkit-transform:translateX(-85%) translateY(-25%);-ms-transform:translateX(-85%) translateY(-25%);-o-transform:translateX(-85%) translateY(-25%);transform:translateX(-85%) translateY(-25%);min-width:1.5rem}.wizlet.wizletCheckBoxes form>ul>li.reduced .card-panel span.title{font-weight:normal}.wizlet.wizletCheckBoxes form>ul>li .subheader{color:#031794;margin-bottom:1rem}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]+span{font-weight:bold;font-size:1.2rem;height:auto;padding-left:50px}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]+span:before{height:28px !important;width:14px !important;top:-7px !important;border-width:4px !important}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]+span:after{height:36px !important;width:36px !important;margin-top:-7px}@media only screen and (max-width : 600px){.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]+span{padding-left:35px}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]+span:before{height:20px !important;width:9px !important;top:-1px !important;border-width:3px !important}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]+span:after{height:24px !important;width:24px !important;margin-top:1px}}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]:checked+span{color:#ff6e00}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]:checked+span:before{border-right-color:#fff;border-bottom-color:#fff}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]:checked+span:after{border-color:#ff6e00;background-color:#ff6e00}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]:disabled+span{color:#222}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]:disabled+span:after{border-color:#ccc !important;background-color:#ccc !important}.wizlet.wizletCheckBoxes form>ul>li [type=checkbox]:disabled+span.title:not(.isSolution) .option-image{-webkit-filter:grayscale(1);filter:grayscale(1)}.wizlet.wizletCheckBoxes form>ul>li .card-panel{position:relative}@media only screen and (max-width : 992px){.wizlet.wizletCheckBoxes form>ul>li .card-panel.hide-label{padding:12px 12px 0 12px}}@media only screen and (max-width : 600px){.wizlet.wizletCheckBoxes form>ul>li .card-panel{margin:0;padding:12px 12px 6px 12px}}.wizlet.wizletCheckBoxes form>ul>li .card-panel i.check-icon{position:absolute;top:0;z-index:1}.wizlet.wizletCheckBoxes form>ul>li .card-panel i.check-icon.left{left:0}.wizlet.wizletCheckBoxes form>ul>li .card-panel i.check-icon.right{right:0}.wizlet.wizletCheckBoxes form>ul>li .card-panel i.check-icon.scaled-out{-webkit-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0)}.wizlet.wizletCheckBoxes form>ul>li ul.collapsible>li{background-color:#fff}.wizlet.wizletCheckBoxes form>ul>li ul.collapsible>li .collapsible-header{font-size:90%;border:none;padding:.5rem}.wizlet.wizletCheckBoxes form>ul>li ul.collapsible>li .collapsible-body{border:none;padding:0 1rem 1rem 1rem}.wizlet.wizletCheckBoxes form>ul>li.follower{pointer-events:none}.wizlet.wizletCheckBoxes form>ul>li.disabled,.wizlet.wizletCheckBoxes form>ul>li[disabled]{pointer-events:none}.wizlet.wizletCheckBoxes form>ul>li.disabled ul.collapsible,.wizlet.wizletCheckBoxes form>ul>li[disabled] ul.collapsible{pointer-events:initial}.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen),.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen){opacity:.8}.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen) label,.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen) label span.title:not(.isSolution),.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen) label,.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen) label span.title:not(.isSolution){color:#555}.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen) label span.title:not(.isSolution)::after,.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen) label span.title:not(.isSolution)::after{border-color:#555}.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen)[data-correct=true] label,.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen)[data-correct=true] label{color:#222}.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen) .question-label,.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen) .question-label{background-color:#222}.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen) ul.collapsible i,.wizlet.wizletCheckBoxes form>ul>li.disabled:not(.chosen) ul.collapsible span,.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen) ul.collapsible i,.wizlet.wizletCheckBoxes form>ul>li[disabled]:not(.chosen) ul.collapsible span{color:#222}.wizlet.wizletCheckBoxes form>ul>li.disabled.chosen [type=checkbox]:disabled+span,.wizlet.wizletCheckBoxes form>ul>li[disabled].chosen [type=checkbox]:disabled+span{color:#ff6e00}.wizlet.wizletCheckBoxes form>ul>li.disabled.chosen [type=checkbox]:disabled+span:after,.wizlet.wizletCheckBoxes form>ul>li[disabled].chosen [type=checkbox]:disabled+span:after{border-color:#ff6e00 !important;background-color:#ff6e00 !important}.wizlet.wizletCheckBoxes form>ul.inline{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap}.wizlet.wizletCheckBoxes form>ul.inline>li.col{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:vertical;-moz-flex-direction:vertical;-ms-flex-direction:column;-webkit-flex-direction:column;flex-direction:column;display:grid;margin:0}.wizlet.wizletCheckBoxes form>ul.inline>li.col:last-child{border-bottom:1px solid #c7538f}.wizlet.wizletCheckBoxes form>ul.inline.centered{text-align:center;display:table}.wizlet.wizletCheckBoxes form>ul.inline.centered>li.col{display:inline-block;float:none;text-align:left;border:none}.wizlet.wizletCheckBoxes form>ul.inline.small .card-panel{padding-right:12px;padding-bottom:0}.wizlet.wizletCheckBoxes .row.submit{padding-top:5px;margin-right:0;text-align:right}.wizlet.wizletCheckBoxes .row.submit #checkBtn[hidden]{display:inline-block !important;visibility:hidden}@media only screen and (max-width : 600px){.wizlet.wizletCheckBoxes .row.submit .btn{font-size:0;width:40px;height:40px;line-height:40px}.wizlet.wizletCheckBoxes .row.submit .btn i{width:inherit;display:inline-block;text-align:center;color:#fff;font-size:1.6rem;line-height:40px}}.wizlet.wizletCheckBoxes #checkBtn{margin-left:15px}