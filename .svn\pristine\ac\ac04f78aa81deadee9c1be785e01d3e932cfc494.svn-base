﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var FormInputs = function () {
        this.type = 'FormInputs';
        this.level = 1;
    };

    FormInputs.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    FormInputs.prototype.unloadHandler = function () {
        $('#toast-container').hide();
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        $(document).off("wizer:model:change", this.nameChanged);
        WizletBase.unloadHandler({ wizlet: this });
    };

    FormInputs.prototype.render = function (options) {
        var self = this;

        if (self.rendering) {
            return self.rendering.promise;
        }
        self.rendering = new Q.defer();

        var loadSelects = self.loadSelectsOptions(options.wizletInfo.inputs);
        
        loadSelects.then(function () {
            
            self.templateDefer.promise.then(function (template) {

                var fragment = template(options.wizletInfo);
                options.context.html(fragment);

                /****** Inputs ******/
                
                var inputs = options.context.find('input:not([disabled])');
                var selects = options.context.find('select');
                
                inputs.filter('[remaining]').characterCounter();

                //Set remaining text
                $.each(options.context.find('.character-counter'), function (idx,remaining) {
                    $(remaining).before('<span class="remaining">&nbsp'+options.wizletInfo.remaining+'</span>');
                });
                

                //Set validation function to all the inputs
                var inputsRequired = inputs.filter('[required]');
                var selectsRequired = selects.filter('[required]');
                
                
                $(inputsRequired).on("keyup", function(event) {
                    M.validate_field ( $(this) );
                    self.validateForm( inputsRequired, selectsRequired, options.context.find('#submitBtn') );
                });
                $(inputsRequired).on("blur", function(event) {
                    self.validateForm( inputsRequired, selectsRequired, options.context.find('#submitBtn') );
                });


                //Init inputs with bound information stored previously
                self.initInputs (inputs, selects, inputsRequired, selectsRequired, options.context.find('#submitBtn'));


                /****** Selects ******/
                selects.formSelect();
                
                
                selects.on('change', function(e) {
                    let $select = $(this);
                    let selectVal = $select.val();

                    
                    $select.removeClass('invalid').addClass('valid');
                    $select.parent('.select-wrapper').removeClass('invalid').addClass('valid');

                    // Saved when submit
                    // self.addVote($select.data('bind'), selectVal);
                    // self.addVote($select.data('bindval'), $select.find('option:selected').text());

                    //Update avatar icon
                    if ($select.hasClass('with-icons')) {
                        $select.siblings('img.avatar').remove();
                        $select.parent().prepend('<img class="avatar" src="/Wizer/Pages/Events/' + wizerApi.eventName() + '/images/avatar/'+selectVal+'">');
                    }

                    //Auto populate the linked select when change value
                    self.addDynamicOptions ($select, options.wizletInfo.dynamic_options, selectVal);    
                    //$(options.context.find('#submitBtn')).attr('disabled',true);                
                    self.validateForm( inputsRequired, selectsRequired, options.context.find('#submitBtn') );
                    
                });

                //Bind touch event for iPads and trigger the click event
                var coordYstart = 0, coordYend = 0;
                var item;
                selects.parent('.select-wrapper').find('ul.select-dropdown li').off('touchstart').on('touchstart', function(event){
                    item = this;
                    coordYstart = event.originalEvent.touches[0].pageY; 
                    event.stopPropagation();
                });
                selects.parent('.select-wrapper').find('ul.select-dropdown li').on('touchend', function(event){
                    coordYend = event.originalEvent.changedTouches[0].pageY;   
                    if (Math.abs(coordYend-coordYstart) < 10) {
                        $(item).trigger('click'); 
                        event.preventDefault(); 
                    }
                });
                


                /****** Clear Button ******/
                options.context.find('#clearBtn').on('click', function (event) {
                    $.each(inputs, function (idx,FormInputs) {
                        $(FormInputs).val('');
                        if (idx===0) $(FormInputs).focus();
                    });
                    if (options.wizletInfo.clearBtn.toast)
                        M.toast({
                            html: options.wizletInfo.clearBtn.toast,
                            classes: 'rounded'
                        });
                });


                /****** Close Button ******/
                options.context.find('#closeBtn').one('click', function (event) {

                    var btn = this;
                    var closeBtn = options.wizletInfo.closeBtn;
                    
                    if (options.wizletInfo.closeBtn.showId)
                        $('#'+closeBtn.showId).removeAttr('hidden');
                    
                    if (options.wizletInfo.closeBtn.showId2)
                        $('#'+closeBtn.showId2).removeAttr('hidden');

                    if (options.wizletInfo.closeBtn.bindDate) {
                        var today = new Date();
                        var date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
                        self.addVote(options.wizletInfo.closeBtn.bindDate, date);
                    }
                    if (options.wizletInfo.closeBtn.bindTime) {
                        var today = new Date();
                        var time = today.getHours() + ":" + today.getMinutes() + ":" + today.getSeconds();
                        self.addVote(options.wizletInfo.closeBtn.bindDate, time);
                    }
                    if (options.wizletInfo.closeBtn.hideAfter)
                        options.context.attr('hidden',true);

                    if (closeBtn.targetSection) wizerApi.jump($(btn).data("action"), false);

                });


                /****** Submit Button ******/
                options.context.find('#submitBtn').one('click', function (event) {

                    var btn = this;
                    var submitBtn = options.wizletInfo.submitBtn;

                    self.saveInputs (options.context, inputs, selects, options.wizletInfo.submitBtn, options.wizletInfo.myName);
                    
                    
                    if (options.wizletInfo.submitBtn.hideIds)
                        $(submitBtn.hideIds).attr('hidden', true);

                    if (options.wizletInfo.submitBtn.showIds)
                        $(submitBtn.showIds).removeAttr('hidden');
                        
                    if (options.wizletInfo.submitBtn.showId)
                        $('#'+submitBtn.showId).removeAttr('hidden');
                    
                    if (options.wizletInfo.submitBtn.showId2)
                        $('#'+submitBtn.showId2).removeAttr('hidden');

                    if (options.wizletInfo.submitBtn.bindDate) {
                        var today = new Date();
                        var date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
                        self.addVote(options.wizletInfo.submitBtn.bindDate, date);
                    }
                    if (options.wizletInfo.submitBtn.bindTime) {
                        var today = new Date();
                        var time = today.getHours() + ":" + today.getMinutes() + ":" + today.getSeconds();
                        self.addVote(options.wizletInfo.submitBtn.bindDate, time);
                    }
                    if (options.wizletInfo.submitBtn.hideAfter)
                        options.context.attr('hidden',true);

                    if (submitBtn.targetSection) wizerApi.jump($(btn).data("action"), false);

                });

                    
                //The follower listens the model to load the leader's name
                if (self.iAmFollower()) {
                           
                    //Keep listening the model to change to a new leader's name
                    $(document).on("wizer:model:change", { 
                                        self: self, 
                                        context: options.context, 
                                        info: options.wizletInfo
                                    }, self.nameChanged);
                        
                };


                self.rendering.resolve();    
            });

        });

        return self.rendering.promise;


    };


    FormInputs.prototype.addDynamicOptions = function ($select, dynamic_options, selectVal) {
        
        var self = this;
        if ($select.is('[data-linkedselect]')) {
            let linkedName = $select.data('linkedselect');
            let linkedSelect = self.wizletContext.find('select[name="'+linkedName+'"]');
            
            //load the new options
            if (selectVal && linkedSelect && dynamic_options && dynamic_options[linkedName] && dynamic_options[linkedName][selectVal]) {
            
                let selectOptions = dynamic_options[linkedName][selectVal];

                let html='';
                $.each (selectOptions, function(idx, option) {
                    if (option.group) html += '<optgroup label="'+option.group+'">';
                    html += '<option value="'+option.val+'"'+  (option.code ? ('data-code="'+option.code+'"') : '')  + ((idx===0)?'disabled selected':'')+'>'+
                                    option.name+
                            '</option>';
                    if (option.endgroup) html += '</optgroup>';
                });
                linkedSelect.html(html);                            
                linkedSelect.removeAttr('disabled').addClass('validate').removeClass('valid');
                linkedSelect.formSelect();
            }    
        }
    };




    FormInputs.prototype.loadSelectsOptions = function (inputs) {
        
        var self = this;

        var loading = new Q.defer();
        var promiseArr = [];

        $.each (inputs, function(idx, input) {

            if (input.type==="select" && input.json) {
                
                var loadJSON = self.loadJSON(input.json);

                promiseArr.push(loadJSON);
                loadJSON.then(function (areas) {
                    input.options = areas;
                });
            }
        });

        Q.all(promiseArr).then(function (response) {
            loading.resolve();
        });

        return loading.promise;

    };



    FormInputs.prototype.validateForm = function (inputs, selects, submitBtn) {
        
        var allValidated = true;
        $.each(inputs, function (idx,input) {
            allValidated = allValidated && $(input).hasClass('valid');
        });
        $.each(selects, function (idx,input) {
            allValidated = allValidated && $(input).hasClass('valid');
        });

        if (allValidated) {
            $(submitBtn).removeAttr('disabled');
        } else {
            $(submitBtn).attr('disabled',true);
        }

    };


    FormInputs.prototype.initInputs = function (inputs, selects, inputsRequired, selectsRequired, submitBtn) {

        var self = this;

        //Get all inputs and selects stored values
        var questionIds = [];
        $.each(inputs, function (idx, input) {
            questionIds.push( self.wizerApi.getQuestionIdByName( $(input).data('bind') ) );
        });
        
        $.each(selects, function (idx, select) {
            questionIds.push( self.wizerApi.getQuestionIdByName( $(select).data('bind') ) );
            if ($(select).is('[data-bindval]'))
                questionIds.push( self.wizerApi.getQuestionIdByName( $(select).data('bindval') ) );
        });
        self.wizerApi.getMyVotes(questionIds).then(function(response) {
            
            var count;
            //set values in the inputs
            $.each(inputs, function (idx, input) {
                count = idx;
                var val = response.votes[questionIds[idx]];
                if (val && val[0]) {
                    $(input).val( val[0] ).focus();
                    //focus and validate input
                    M.validate_field ( $(input) );
                    //remove focus in the last input
                    if (idx===inputs.length-1) $(input).blur();
                }
            });
            //update materialize look&feel of inputs
            M.updateTextFields();
            
            //set values in the selects
            $.each(selects, function (idx, select) {
                let val = response.votes[questionIds[count+idx+1]];
                
                let $select = $(select);

                if (val && val[0]) {
                    $select.val( val[0] );
                    //update materialize and add validation class
                    $select.formSelect();
                    $select.addClass('valid');
                    $select.parent('.select-wrapper').addClass('valid');
                    if ($select.hasClass('with-icons')) {
                        $select.siblings('img.avatar').remove();
                        $select.parent().prepend('<img class="avatar" src="/Wizer/Pages/Events/' + wizerApi.eventName() + '/images/avatar/'+$(select).val()+'">');
                    }
                    //Auto populate the linked select when change value
                    self.addDynamicOptions ($select, self.wizletInfo.dynamic_options, val[0]); 
                }
            });
            
            //validate form to enable submit button
            self.validateForm( inputsRequired, selectsRequired, submitBtn );
        });


    };

    FormInputs.prototype.saveInputs = function (context, inputs, selects, submitBtn, myName) {

        var self = this;

        var names=[], values=[];
        $.each(inputs, function (idx,input) {
            // self.addVote($(input).data('bind'), $(input).val());
            names.push( $(input).data('bind') );
            values.push( $(input).val() );
            if (submitBtn.clearAfter) $(input).val('').focus();
        });
        
        $.each(selects, function (idx,select) {
            names.push( $(select).data('bind') );
            values.push( $(select).val() );
            if (submitBtn.clearAfter) $(select).val('');
            if ($(select).is('[data-bindval]')) {
                names.push( $(select).data('bindval') );
                values.push( $(select).find('option:selected').text() );
            }
        });

        
        self.addVotes(names, values);


        if (submitBtn.toast)
            M.toast({
                html: submitBtn.toast,
                classes: 'rounded'
            });

        context.find('#submitBtn').prop('hidden',true);
        
        context.find('input').attr('disabled',true);

        //Set my participant name
        if (myName) {
            var newName = inputs.filter('[name=name]').val();
            //self.addVote(myName, newName);
            $('header .participantName').html( newName );
        }

    };

    
    FormInputs.prototype.loadJSON = function (url) {
        
        var self = this;

        var loading = new Q.defer();

        var xobj = new XMLHttpRequest();
        xobj.overrideMimeType("application/json");
        xobj.open('GET', 
                '/Wizer/Pages/Events/'+self.wizerApi.eventName()+url+'?nocache=' + (new Date()).getTime(), 
                true); 
        xobj.onreadystatechange = function () {
            if (xobj.readyState == 4 && xobj.status == "200") {
                loading.resolve( JSON.parse( xobj.responseText ) );
            }
        };
        xobj.send(null);

        return loading.promise;
    };


    
    
    FormInputs.prototype.iAmFollower = function () {
        var self = this;
        return (self.wizletInfo.isFollower && 
                self.wizletInfo.DB[self.wizletInfo.isFollower] == 1);
    };



    FormInputs.prototype.nameChanged = function (event) {
        var self = event.data.self;
        var info = event.data.info;
        
        //check the leader's name
        var teamId = self.wizerApi.getQuestionIdByName(info.trackTeam);
        var questionId = self.wizerApi.getQuestionIdByName(info.follower_bind);
        self.wizerApi.getForemanVotes(teamId,[questionId]).then(function (response) { 
            var leaderVal =  response.votes[questionId][0]; 
            if (leaderVal) {
                $('header .participantName').html( leaderVal + info.follower_suffix );                 
            }
        });  

    };


    
    FormInputs.prototype.removeVotes = function (questionName) {
        var self = this;
        var questionId = self.wizerApi.getQuestionIdByName(questionName);
        self.wizerApi.removeVotes({ votes: [{ questionId: questionId }] });
    };
    
    FormInputs.prototype.addVote = function (questionName, val) {
        var self = this;
        var questionId = self.wizerApi.getQuestionIdByName(questionName)
        self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});
    };

    
    FormInputs.prototype.addVotes = function (questionNames, values) {
        var self = this; 

        var myVotes = [];
        questionNames.forEach(function(q,pos) { 
            myVotes.push( { 
                questionId: self.wizerApi.getQuestionIdByName(q), 
                responseText: values[pos] } ); 
        });
        self.wizerApi.addVotes({ votes: myVotes });
    };


    
    FormInputs.getRegistration = function () {
        return new FormInputs();
    };

    return FormInputs;

});