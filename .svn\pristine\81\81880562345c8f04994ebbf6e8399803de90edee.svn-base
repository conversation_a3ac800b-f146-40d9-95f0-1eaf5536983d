﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var Password = function () {
        this.type = 'Password';
        this.level = 1;
    };

    Password.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Password.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        $(document).off("wizer:model:change", this.nameChanged);
        WizletBase.unloadHandler({ wizlet: this });
    };

    Password.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);

            var inputField ='input';

            var inputs = options.context.find('input:not([disabled])');
            var inputsRequired = inputs.filter('[required]');
            
            $(inputsRequired).on('keydown', function(e) {
                var key = e.which || e.keyCode;                
                if (key === 13) {
                    e.preventDefault();                        
                }
            });


            $(inputsRequired).on("keyup", function(event) {
                var valid = self.validatePassword( this, options.wizletInfo.password.code);
                if (valid) {
                    options.context.find('#submitBtn').removeAttr('hidden');
                } else {
                    options.context.find('#submitBtn').attr('hidden', true);
                }

            });
            // $(inputsRequired).on("blur", function(event) {
            //     var valid = self.validatePassword( this, options.wizletInfo.password.code, options.context.find('#submitBtn') );
            // });


            //Save text from inputs
            options.context.find('#submitBtn').on('click', function (event) {

                if (options.wizletInfo.submitBtn.action)
                    self[options.wizletInfo.submitBtn.action] ();

                if (options.wizletInfo.submitBtn.toast)
                    M.toast({
                        html: options.wizletInfo.submitBtn.toast,
                        classes: 'rounded'
                    });

                
                if (options.wizletInfo.submitBtn.jump)
                    wizerApi.jump(options.wizletInfo.submitBtn.jump, false);

                    
                if (options.wizletInfo.submitBtn.load)
                    self.loadPage(
                        options.wizletInfo.submitBtn.load, 
                        options.context, 
                        self.unsedContext, 
                        $(this).find('.modal-content')
                    );

            });
            


            return true;
        })
        .fail(this.wizerApi.showError);
    };


      
    Password.prototype.validatePassword = function (input, code) {
        
        var valid = false;

        if ($(input).val() == code) {
            $(input).removeClass('invalid').addClass('valid');
            valid = true;
            M.validate_field ( $(input) );
        } else {
            $(input).removeClass('valid').addClass('invalid')
        }
        //update materialize look&feel of inputs
        M.updateTextFields();

        return valid;

    };


    Password.prototype.loadPage = function (actionXMLName, context, unusedContext, modalContainer) {
        var self = this;
       

        //unload previous 
        if (self.currentWizletModule && self.currentWizletModule.length > 0) {
            $.each(self.currentWizletModule, function (index, module) {
                if (module.wizletInstance.unloadHandler) {
                    module.wizletInstance.unloadHandler();
                }
            });
        }
        
        $('.material-tooltip').remove();
        
        var loading = self.wizerApi.loadActionInContainer(actionXMLName, context, unusedContext, modalContainer);
        loading.then(function (loads) {
           
            self.currentWizletModule = loads;
            
            var page = "wizer:action:init.mainArea";
                        
            $(document).trigger(page, actionXMLName);
        });
        
    };


    
    Password.prototype.removeVotes = function (questionName) {
        
        this.wizerApi.resetVotes()
    };
    
    


    
    Password.getRegistration = function () {
        return new Password();
    };

    return Password;

});