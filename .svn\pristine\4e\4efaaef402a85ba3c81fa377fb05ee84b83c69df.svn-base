@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";



// .tab-component .wizlet.wizletCarousel > .container {
//     margin: 0;
//     width: initial;
// }

.wizlet.wizletCarousel {

    .carousel {
             
        @media #{$small-and-down} { 
            height: 150px;
        }    
        @media #{$medium-and-down} { 
            height: 250px;
        }
        @media #{$medium-and-up} { 
            height: 350px;
        }
        @media #{$large-and-up} { 
            height: 450px;
        }
        @media #{$extra-large-and-up} { 
            height: 550px;
        }

        margin: 20px 0;
        .carousel-item { 
            width: 50%;
            height: 100%;

            
            .card {

                @include no-select-highlight;

                background-color: color("client-colors", "background2") ;
                
                .card-image {
                    min-width: 30%;
                    max-height: 75%;

                    img {
                        //min-height: 200px;
                        height: 300px;
                        object-fit: contain;
                    }
                }

                .card-action a {
                    font-family: clientBold, Arial;
                    cursor: pointer;
                    color: color("client-colors", "button") !important; 
                    &:hover {
                        color: color("client-colors", "aux1") !important; 
                        text-decoration: underline;
                    }
                    font-size: 20px; 
                    @media #{$small-and-down} { 
                        font-size: 16px; 
                    }
                }

                .card-content, 
                .card-reveal {
                    @include card-title(100%);
                    .card-title.size {
                        &.big {font-size: 120%; }
                        &.small {font-size: 90%; }
                    }
                    width: 90%;
                    height: auto;
                }
                .card-reveal {
                    padding-top: 0px;
                    .reveal-text {
                        font-size: 90%;
                    }
                }


                .card-content {
                    padding: 5px 12px;            
                    // height: 70px;
                    overflow: auto;
                    .card-title {         
                        border-bottom: none;    
                        line-height: 2rem;
                        word-break: break-word;                
                        @media #{$small-and-down} { 
                            font-size: 20px;  
                        }
                    }            
                }

                .card-action {
                    padding: 8px 12px;
                    cursor: pointer;
                }

                &.sticky-action {

                    .card-reveal {
                        @include opacity(0.95);
                        p, ul {
                            font-size: inherit;
                        }
                    }
                    &.with-overflow {
                        overflow-y: auto;
                        .card-reveal {
                            height: 150px;
                            .reveal-text {
                                position: absolute;
                                padding-bottom: 10px;
                            } 
                        }
                    }
                }

                
                &.disabled {
                    pointer-events: none;
                    filter: grayscale(100%);
                }


                &.no-reveal {
                    .card-content .card-title {
                        text-align: center;
                    }
                    .card-image, .card-content {
                        cursor: pointer;
                    }
                }
                
            }


        }

        .indicators {
            bottom: -28px;

            .indicator-item {
                background-color: color("client-colors", "primary");

                &.active {
                    background-color: color("client-colors", "secondary");
                }
            }
        }
    }

    .modal.open {
        width: fit-content;
        max-width: 90vw;
        height: auto;
        background-color: rgba(0,0,0,0);

        .modal-content {
            width: fit-content;
            text-align: center;

            img {
                width: 100%;
                // height: 100%;
                height: -webkit-fill-available;
                max-width: 90vw;
                object-fit: contain;
            }
        }

        &.fullModal {
            overflow: hidden;
            padding: 0;
            .modal-content {
                background-color: color("client-colors", "white");
                padding: 5px;
                height: 90vh;
            }
            .modal-footer {
                position: absolute;
                top: 0;
                right: 0;
                background: none;
                .modal-close {
                    margin: 0;
                    padding: 0;
                    i {
                        margin: 0 5px;
                    }
                }
            }
        }
    }

}