<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro2">
  <Include name="Header_Intro"></Include>
  

  <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      _header: "!{R2_Welcome_Header}",
      valign: false,
      animate: "zoomIn",
      transparentBox: false,
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{R2_Welcome_Image}",  alt: "!{R2_Welcome_Header}" ,
          position: "left large",
          src_vert: "!{}",
          animate: "fadeInLeft animate__delay-1s", _animateLater: "bounce"
        },
        img: { 
          materialboxed: false, _borders: "top right left bottom", frame: "", nopadding: false,
          src: "!{R2_Welcome_Image}",  alt: "!{R2_Welcome_Header}",
          isHiddenWhenSmall: true, 
          src_vert: "!{}",
          animate: "fadeInUp animate__delay-1s"
        },
        position: "up down",
        animate: "fadeInUp animate__delay-1s",
        title: "!{R2_Welcome_Title}",
        body: "!{R2_Welcome_Text}"
      }
    }]]>  
  </Component>


  <Include name="Init_Score"></Include> 
  

</Action>