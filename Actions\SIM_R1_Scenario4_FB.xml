<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayout_SIM_R1" layout="../../../layout/tabsLayout2">
  <Include name="Header_SIM_R1"></Include>
  <Include name="KPIgauges_R1"></Include>


  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "bounceInLeft",
      progress: "5",
      steps: [ "!{SIM_BreadCrumbs_0}",
               "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}" ]
    }]]>
  </Component>



  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R1_Scenario4_FB_Header}",
      swipeable: true,
      tabs: [
          "!{Feedback_Opt} !{Choice_Opt1}",
          "!{Feedback_Opt} !{Choice_Opt2}"
      ],
      correctOption: 0,
      myText: "!{Feedback_MyOpt}",
      isCheck: false,
      bind: "Q_SIM_R1_Scenario4",
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower","Q_SIM_R1_Scenario4"]
  }]]></Component>


  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      _isHidden: true,
        _condition: "Q_SIM_R1_Scenario4",
        _condition_Diff: "1",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario4_FB_Opt1_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario4_Opt1}",
        body: "!{SIM_R1_Scenario4_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario4_Opt1_KPI1}","!{SIM_R1_Scenario4_Opt1_KPI2}","!{SIM_R1_Scenario4_Opt1_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario4",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario4"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      _isHidden: true,
        _condition: "Q_SIM_R1_Scenario4",
        _condition_Diff: "2",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario4_FB_Opt2_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario4_Opt2}",
        body: "!{SIM_R1_Scenario4_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario4_Opt2_KPI1}","!{SIM_R1_Scenario4_Opt2_KPI2}","!{SIM_R1_Scenario4_Opt2_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario4",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario4"]
    }]]>
  </Component>






  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>





  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      _isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "SIM_R1_Summary",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R1_Scenario4_FB_Header}"/>
  </Voting>





</Action>