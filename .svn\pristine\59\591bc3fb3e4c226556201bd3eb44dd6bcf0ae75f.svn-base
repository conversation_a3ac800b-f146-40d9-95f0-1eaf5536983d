/* wizer API utility to simplify most used API calls
*/

define(['Q', 'wizer-api', './newCalcBinderCache'], function (Q, WizerApi, newCalcBinderCache) {
    var instance;

    var WizerApiExtended = function (wizerApi) {
        this.type = 'WizerApiExtended';
        this.level = 1;
        this.wizerApi = wizerApi;

        this.newCalcBinderCache = new newCalcBinderCache(wizerApi);
    };

    // get my vote on track by track question name
    WizerApiExtended.prototype.getMyTrackVote = function (trackQuestion) {
        var self = this;
        fetching = Q.defer();
        var trackQuestionId = self.wizerApi.getQuestionIdByName(trackQuestion);
        var gettingMyVoteOnTrack = self.wizerApi.getMyVotes([trackQuestionId]);
        gettingMyVoteOnTrack.then(function (votes) {
            var myVoteonTrack = votes.votes[trackQuestionId];
            if (myVoteonTrack) {
                fetching.resolve(myVoteonTrack);
            }
            else {
                trackDiffer.resolve(null);
                Wizer.Api.showError("No Votes of this user on trackQuestion: " + trackQuestion);
            }
        });

        return fetching.promise;
    }

    /**
     * Get votes for given questions
     * @param {Array} questions array of question names
     * @param {String} trackQuestion Track question name
     * @param {Object} sorting sorting object {question:"sortingQuestionName", "isNumeric":true/false}
     * returns object in following format
       {votes: Array(20), participants: Array(5)}
       participants : Array(5)
            0:
                email:"Team2"
                id:2758
                name:"Team 2"
                questionMap:
                    Q1:{qid: 7362, qName: "Q1", value: "20"}
                    Q2:{qid: 7363, qName: "Q2", value: "40"}
                    Q3:{qid: 7364, qName: "Q3", value: "50"}
                    Team_No:{qName: "Team_No", qid: 7369, value: "1"}
                    tlInputTeamName:{qid: 7370, qName: "tlInputTeamName", value: "Name 5"}
            1:{id: 2759, name: "Team 1", email: "Team1", questionMap: {…}}
            2:{id: 2760, name: "Team 3", email: "Team3", questionMap: {…}}
            3:{id: 2761, name: "Team 4", email: "Team4", questionMap: {…}}
            4:{id: 2762, name: "Team 5", email: "Team5", questionMap: {…}}
        votes:Array(20)
            0:
            {questionId: 7370, participantName: "Team 2", responseText: "Name 5", participantIsAdmin: false, sequence: 0, …}
     */
    WizerApiExtended.prototype.getVotesByQuestionName = function (questions, trackQuestion, sorting, maxLength) {
        var self = this;
        var waiting = new Q.defer;
        var participants = [];
        var participantMap = {};
        var questionMap = {};
        var questioIdnMap = {};

        var qid, qObj, qIds = [];
        questions.forEach(function(question) {
            qid = self.wizerApi.getQuestionIdByName(question);
            qObj = {qName:question, qid:qid};
            questioIdnMap[qid] = questionMap[question] = qObj;
            qIds.push(qid);
        });

        
        var trackQuestionId, myTrackVote;
        var trackDiffer = new Q.defer();
        
   
        if(trackQuestion){
            trackQuestionId = self.wizerApi.getQuestionIdByName(trackQuestion);
            self.getMyTrackVote(trackQuestion).then(function (val) {
                myTrackVote = val;
                trackDiffer.resolve(true);
            });
        }else{
            trackDiffer.resolve(true);
        }

        var participant;
        trackDiffer.promise.then(function () {            
            var param = {questionIds: qIds, filterQuestionId:trackQuestionId, filterText:myTrackVote }
            self.wizerApi.getVotes(param).then(function (response) {
                response.votes.forEach(function(vote){
                    if(!participantMap[vote.participantId]){
                        participantMap[vote.participantId] = {
                            id:vote.participantId, 
                            name: vote.participantName, 
                            email: vote.participantEmail,
                            questionMap: {}
                        };
                        participants.push(participantMap[vote.participantId]);
                    }
                    participant = participantMap[vote.participantId];
                    var qName = questioIdnMap[vote.questionId].qName;

                    if(!participant.questionMap[qName]){
                        participant.questionMap[qName] = {qid:vote.questionId, qName:qName};
                    }
                    qObj = participant.questionMap[qName];
                    qObj.value = vote.responseText;

                });

                response.participants = participants;

                if(sorting){
                    sortQid = self.wizerApi.getQuestionIdByName(sorting.question);                    
                    questioIdnMap[qid] = questionMap[sorting.question] = qObj;   
                    var param1 = {questionIds: [sortQid], filterQuestionId:trackQuestionId, filterText:myTrackVote };
                    self.wizerApi.getVotes(param1).then(function (res) {
                        res.votes.forEach(function(vote){
                            participant = participantMap[vote.participantId];
                            var qName = sorting.question;
                            qObj = {qName:sorting.question, qid:sortQid};
                            if(participant){
                                qObj.value = vote.responseText;
                                if(!participant.questionMap[qName]){
                                    participant.questionMap[qName] = qObj;
                                }
                            }
                        });
                        response.participants = self.sortParticipansByVote(participants, sorting);
                        if (maxLength) response.participants = (response.participants).slice(0, maxLength);
                        waiting.resolve(response);
                    });
                }else{
                    waiting.resolve(response);

                }
            });
        });
        return waiting.promise;

    }

    /**
     * sort participants by given sorting object
     * @param {Array} participants Array of participant objects
     * @param {Object} sorting sorting object {question:"sortingQuestionName", "isNumeric":true/false}
     * Returns sorted array
     */
    WizerApiExtended.prototype.sortParticipansByVote = function (participants, sorting) {
        
        var q = sorting.question;
        if(sorting.isNumeric){  
            if(sorting.sortOrder == "DESC"){
                participants.sort(function (a, b) {
                    return parseInt(b.questionMap[q].value) - parseInt(a.questionMap[q].value);
                });
            }else {
                participants.sort(function (a, b) {
                    return parseInt(a.questionMap[q].value) - parseInt(b.questionMap[q].value);
                });
            }
        }else{
            var num = sorting.sortOrder == "DESC" ? -1 : 1;
            participants.sort(function (a, b) {
                if(a.questionMap[q].value < b.questionMap[q].value) return -1 * num;
                if(a.questionMap[q].value > b.questionMap[q].value) return 1 * num;
                return 0;
            });
        }

        return participants;
    }

    /**
     * Get my votes for given questions
     * @param {Array} questions Array of question names
     */
    WizerApiExtended.prototype.getMyVotesByQuestionName = function (questions) {
        var self = this;
        var waiting = new Q.defer;
        var questionMap = {};
        var questioIdnMap = {};

        var qid, qObj, qIds = [];
        questions.forEach(function(question) {
            qid = self.wizerApi.getQuestionIdByName(question);
            qObj = {qName:question, qid:qid};
            questioIdnMap[qid] = questionMap[question] = qObj;
            qIds.push(qid);
        });
        self.wizerApi.getMyVotes(qIds).then(function (response) {
            questions.forEach(function(question) {
                qObj = questionMap[question];
                if(response.votes && response.votes[qObj.qid]){
                    qObj.value = response.votes[qObj.qid][0];
                }
            });
            response.questionMap = questionMap;
            response.questioIdnMap = questioIdnMap;
            waiting.resolve(response);
        });

        return waiting.promise;
    }



    WizerApiExtended.prototype.getCalcValue = function (modelName, range) {
        var self = this;

        var getting = Q.defer();
        var ensuring = self.wizerApi.calcBinderCache.ensureCalcBinder(modelName);
        ensuring.then(function (calcBinder) {

            if (calcBinder) {
                calcBinder.getValue(range, undefined /*period*/, function (value) {
                    // checking for booleans which are often returned from calc (Excel) models
                    if (value == true)
                        result = 1;
                    else if (value == false)
                        result = 0;
                    else if (typeof value == 'string') {
                        if (isNaN(value) && isNaN(parseFloat(value))) {
                            result = value;
                        } else {
                            result = numeral().unformat(value)
                        }
                        //console.log(range,':',value,'=',result);
                    } else if (value.errorContext) {
                        if (calcCell.uninitialized) {
                            console.log(true, "Uninitialized cells: " + JSON.stringify(calcCell.uninitialized));
                        }
                        console.log(true, "Error in js calc model. Cell(" + value.errorContext.address() + ") has data '" + value.data + "', text '" + value.text + "' and formula '" + value.errorContext.formula + "'");
                        result = value.data
                    } else {
                        try {
                            result = JSON.stringify(value);
                        } catch (ex) {
                            getting.reject(new Error("Getting value that I cannot even stringify"));
                            return;
                        }
                    }
                    getting.resolve(result);
                });
            } else {
                getting.reject(new Error("no calcBinder"));
            }
        }, function (err) {
            getting.reject(err);
        });
        return getting.promise;
    }

    WizerApiExtended.prototype.getCalcValues = function (modelName, questions) {
        var self = this;

        var getting = Q.defer();
        var promisearr = [];

        var results = [];

        var ensuring = self.wizerApi.calcBinderCache.ensureCalcBinder(modelName);
        ensuring.then(function (calcBinder) {

            if (calcBinder) {

                questions.forEach(function(range, idx) {
                    
                    var promise = calcBinder.getValue(range, undefined /*period*/, function (value) {
                        
                        // checking for booleans which are often returned from calc (Excel) models
                        if (value == true)
                            result = 1;
                        else if (value == false)
                            result = 0;
                        else if (typeof value == 'string') {
                            if (isNaN(value) && isNaN(parseFloat(value))) {
                                result = value;
                            } else {
                                result = numeral().unformat(value)
                            }
                            //console.log(range,':',value,'=',result);
                        } else if (value.errorContext) {
                            if (calcCell.uninitialized) {
                                console.log(true, "Uninitialized cells: " + JSON.stringify(calcCell.uninitialized));
                            }
                            console.log(true, "Error in js calc model. Cell(" + value.errorContext.address() + ") has data '" + value.data + "', text '" + value.text + "' and formula '" + value.errorContext.formula + "'");
                            result = value.data
                        } else {
                            try {
                                result = JSON.stringify(value);
                            } catch (ex) {
                                getting.reject(new Error("Getting value that I cannot even stringify"));
                                return;
                            }
                        }
                        results[idx] = result;
                    });
                    promisearr.push(promise);
                });

                Q.all(promisearr).done(function(values) {
                    getting.resolve(results);
                });

            } else {
                getting.reject(new Error("no calcBinder"));
            }
        }, function (err) {
            getting.reject(err);
        });
        return getting.promise;
    }



    WizerApiExtended.getRegistration = function (wizerApi) {
        
        return instance || new WizerApiExtended(wizerApi);
    };

    return WizerApiExtended;

});
