.wizlet.wizletRollingDice_old .card .card-content .row{margin:0}.wizlet.wizletRollingDice_old #diceBoard{height:55vh;border:10px outset #00aab4;background-color:rgba(255,110,0,.3)}.wizlet.wizletRollingDice_old #diceBoard #wrapper{position:relative;width:100%;height:70%;-webkit-perspective:1200px;perspective:1200px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform{position:relative;width:70%;height:90%;margin-top:8%;margin-left:30%}@media only screen and (max-width : 992px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform{height:85%;margin-top:13%}}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform.playing{-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-animation:roll 1s infinite linear;-o-animation:roll 1s infinite linear;animation:roll 1s infinite linear}@media only screen and (max-width : 600px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform.playing{-webkit-animation:roll-mid 1s infinite linear;-o-animation:roll-mid 1s infinite linear;animation:roll-mid 1s infinite linear}}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform.playing #dice{-webkit-animation-duration:1s;-o-animation-duration:1s;animation-duration:1s;-webkit-animation:spin 2s infinite linear;-o-animation:spin 2s infinite linear;animation:spin 2s infinite linear}@media only screen and (max-width : 600px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform.playing #dice{-webkit-animation:spin-mid 2s infinite linear;-o-animation:spin-mid 2s infinite linear;animation:spin-mid 2s infinite linear}}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform.stop,.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform.stop #dice{-webkit-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice{position:absolute;width:200px;height:200px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transform:rotateX(0deg) rotateY(20deg) rotateZ(-20deg);transform:rotateX(0deg) rotateY(20deg) rotateZ(-20deg)}@media only screen and (max-width : 600px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice{width:100px;height:100px}}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side{position:absolute;width:200px;height:200px;background:#fff;-webkit-box-shadow:inset 0 0 40px #ccc;box-shadow:inset 0 0 40px #ccc;border-radius:40px}@media only screen and (max-width : 600px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side{width:100px;height:100px}}@media only screen and (max-width : 600px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side{-webkit-box-shadow:inset 0 0 20px #ccc;box-shadow:inset 0 0 20px #ccc;border-radius:20px}}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.cover,.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.inner{background:#555;-webkit-box-shadow:none;box-shadow:none}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.cover{border-radius:0}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.cover.x{-webkit-transform:rotateY(90deg);transform:rotateY(90deg)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.cover.z{-webkit-transform:rotateX(90deg);transform:rotateX(90deg)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.front{-webkit-transform:translateZ(100px);transform:translateZ(100px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.front.inner{-webkit-transform:translateZ(99px);transform:translateZ(99px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.back{-webkit-transform:rotateX(-180deg) translateZ(100px);transform:rotateX(-180deg) translateZ(100px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.back.inner{-webkit-transform:rotateX(-180deg) translateZ(99px);transform:rotateX(-180deg) translateZ(99px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.right{-webkit-transform:rotateY(90deg) translateZ(100px);transform:rotateY(90deg) translateZ(100px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.right.inner{-webkit-transform:rotateY(90deg) translateZ(99px);transform:rotateY(90deg) translateZ(99px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.left{-webkit-transform:rotateY(-90deg) translateZ(100px);transform:rotateY(-90deg) translateZ(100px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.left.inner{-webkit-transform:rotateY(-90deg) translateZ(99px);transform:rotateY(-90deg) translateZ(99px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.top{-webkit-transform:rotateX(90deg) translateZ(100px);transform:rotateX(90deg) translateZ(100px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.top.inner{-webkit-transform:rotateX(90deg) translateZ(99px);transform:rotateX(90deg) translateZ(99px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.bottom{-webkit-transform:rotateX(-90deg) translateZ(100px);transform:rotateX(-90deg) translateZ(100px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.bottom.inner{-webkit-transform:rotateX(-90deg) translateZ(99px);transform:rotateX(-90deg) translateZ(99px)}@media only screen and (max-width : 600px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.front{-webkit-transform:translateZ(50px);transform:translateZ(50px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.front.inner{-webkit-transform:translateZ(49px);transform:translateZ(49px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.back{-webkit-transform:rotateX(-180deg) translateZ(50px);transform:rotateX(-180deg) translateZ(50px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.back.inner{-webkit-transform:rotateX(-180deg) translateZ(49px);transform:rotateX(-180deg) translateZ(49px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.right{-webkit-transform:rotateY(90deg) translateZ(50px);transform:rotateY(90deg) translateZ(50px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.right.inner{-webkit-transform:rotateY(90deg) translateZ(49px);transform:rotateY(90deg) translateZ(49px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.left{-webkit-transform:rotateY(-90deg) translateZ(50px);transform:rotateY(-90deg) translateZ(50px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.left.inner{-webkit-transform:rotateY(-90deg) translateZ(49px);transform:rotateY(-90deg) translateZ(49px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.top{-webkit-transform:rotateX(90deg) translateZ(50px);transform:rotateX(90deg) translateZ(50px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.top.inner{-webkit-transform:rotateX(90deg) translateZ(49px);transform:rotateX(90deg) translateZ(49px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.bottom{-webkit-transform:rotateX(-90deg) translateZ(50px);transform:rotateX(-90deg) translateZ(50px)}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side.bottom.inner{-webkit-transform:rotateX(-90deg) translateZ(49px);transform:rotateX(-90deg) translateZ(49px)}}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot{position:absolute;width:46px;height:46px;border-radius:23px;background:#222;-webkit-box-shadow:inset 5px 0 10px #000;box-shadow:inset 5px 0 10px #000}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.center{margin:77px 0 0 77px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dtop{margin-top:20px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dleft{margin-left:134px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dright{margin-left:20px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dbottom{margin-top:134px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.center.dleft{margin:77px 0 0 20px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.center.dright{margin:77px 0 0 134px}@media only screen and (max-width : 600px){.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot{width:24px;height:24px;border-radius:12px;-webkit-box-shadow:inset 3px 0 5px #000;box-shadow:inset 3px 0 5px #000}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.center{margin:38px 0 0 38px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dtop{margin-top:10px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dleft{margin-left:67px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dright{margin-left:10px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.dbottom{margin-top:67px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.center.dleft{margin:38px 0 0 10px}.wizlet.wizletRollingDice_old #diceBoard #wrapper #platform #dice .side .dot.center.dright{margin:38px 0 0 67px}}.wizlet.wizletRollingDice_old #diceBoard #wrapper #playBtn{-o-animation-name:shakeInfinite;-webkit-animation-name:shakeInfinite;animation-name:shakeInfinite;-o-animation-duration:5s;-webkit-animation-duration:5s;animation-duration:5s;-o-animation-iteration-count:infinite;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-o-animation-timing-function:ease;-webkit-animation-timing-function:ease;animation-timing-function:ease;-o-animation-delay:2s;-webkit-animation-delay:2s;animation-delay:2s;-o-animation-fill-mode:forwards;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}.wizlet.wizletRollingDice_old #diceBoard #wrapper #scoreBox{width:80%;margin:auto;padding:12px}@-webkit-keyframes spin{0%{-webkit-transform:translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);transform:translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)}16%{-webkit-transform:translateZ(-100px) rotateX(180deg) rotateY(180deg) rotateZ(0deg);transform:translateZ(-100px) rotateX(180deg) rotateY(180deg) rotateZ(0deg)}33%{-webkit-transform:translateZ(-100px) rotateX(360deg) rotateY(90deg) rotateZ(180deg);transform:translateZ(-100px) rotateX(360deg) rotateY(90deg) rotateZ(180deg)}50%{-webkit-transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}66%{-webkit-transform:translateZ(-100px) rotateX(180deg) rotateY(360deg) rotateZ(270deg);transform:translateZ(-100px) rotateX(180deg) rotateY(360deg) rotateZ(270deg)}83%{-webkit-transform:translateZ(-100px) rotateX(270deg) rotateY(180deg) rotateZ(180deg);transform:translateZ(-100px) rotateX(270deg) rotateY(180deg) rotateZ(180deg)}100%{-webkit-transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}}@-o-keyframes spin{0%{transform:translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)}16%{transform:translateZ(-100px) rotateX(180deg) rotateY(180deg) rotateZ(0deg)}33%{transform:translateZ(-100px) rotateX(360deg) rotateY(90deg) rotateZ(180deg)}50%{transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}66%{transform:translateZ(-100px) rotateX(180deg) rotateY(360deg) rotateZ(270deg)}83%{transform:translateZ(-100px) rotateX(270deg) rotateY(180deg) rotateZ(180deg)}100%{transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}}@keyframes spin{0%{-webkit-transform:translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);transform:translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)}16%{-webkit-transform:translateZ(-100px) rotateX(180deg) rotateY(180deg) rotateZ(0deg);transform:translateZ(-100px) rotateX(180deg) rotateY(180deg) rotateZ(0deg)}33%{-webkit-transform:translateZ(-100px) rotateX(360deg) rotateY(90deg) rotateZ(180deg);transform:translateZ(-100px) rotateX(360deg) rotateY(90deg) rotateZ(180deg)}50%{-webkit-transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}66%{-webkit-transform:translateZ(-100px) rotateX(180deg) rotateY(360deg) rotateZ(270deg);transform:translateZ(-100px) rotateX(180deg) rotateY(360deg) rotateZ(270deg)}83%{-webkit-transform:translateZ(-100px) rotateX(270deg) rotateY(180deg) rotateZ(180deg);transform:translateZ(-100px) rotateX(270deg) rotateY(180deg) rotateZ(180deg)}100%{-webkit-transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}}@-webkit-keyframes spin-mid{0%{-webkit-transform:translateZ(-50px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);transform:translateZ(-50px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)}16%{-webkit-transform:translateZ(-50px) rotateX(180deg) rotateY(180deg) rotateZ(0deg);transform:translateZ(-50px) rotateX(180deg) rotateY(180deg) rotateZ(0deg)}33%{-webkit-transform:translateZ(-50px) rotateX(360deg) rotateY(90deg) rotateZ(180deg);transform:translateZ(-50px) rotateX(360deg) rotateY(90deg) rotateZ(180deg)}50%{-webkit-transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}66%{-webkit-transform:translateZ(-50px) rotateX(180deg) rotateY(360deg) rotateZ(270deg);transform:translateZ(-50px) rotateX(180deg) rotateY(360deg) rotateZ(270deg)}83%{-webkit-transform:translateZ(-50px) rotateX(270deg) rotateY(180deg) rotateZ(180deg);transform:translateZ(-50px) rotateX(270deg) rotateY(180deg) rotateZ(180deg)}100%{-webkit-transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}}@-o-keyframes spin-mid{0%{transform:translateZ(-50px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)}16%{transform:translateZ(-50px) rotateX(180deg) rotateY(180deg) rotateZ(0deg)}33%{transform:translateZ(-50px) rotateX(360deg) rotateY(90deg) rotateZ(180deg)}50%{transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}66%{transform:translateZ(-50px) rotateX(180deg) rotateY(360deg) rotateZ(270deg)}83%{transform:translateZ(-50px) rotateX(270deg) rotateY(180deg) rotateZ(180deg)}100%{transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}}@keyframes spin-mid{0%{-webkit-transform:translateZ(-50px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);transform:translateZ(-50px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)}16%{-webkit-transform:translateZ(-50px) rotateX(180deg) rotateY(180deg) rotateZ(0deg);transform:translateZ(-50px) rotateX(180deg) rotateY(180deg) rotateZ(0deg)}33%{-webkit-transform:translateZ(-50px) rotateX(360deg) rotateY(90deg) rotateZ(180deg);transform:translateZ(-50px) rotateX(360deg) rotateY(90deg) rotateZ(180deg)}50%{-webkit-transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}66%{-webkit-transform:translateZ(-50px) rotateX(180deg) rotateY(360deg) rotateZ(270deg);transform:translateZ(-50px) rotateX(180deg) rotateY(360deg) rotateZ(270deg)}83%{-webkit-transform:translateZ(-50px) rotateX(270deg) rotateY(180deg) rotateZ(180deg);transform:translateZ(-50px) rotateX(270deg) rotateY(180deg) rotateZ(180deg)}100%{-webkit-transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg);transform:translateZ(-50px) rotateX(360deg) rotateY(360deg) rotateZ(360deg)}}@-webkit-keyframes roll{0%{-webkit-transform:translate3d(-200px, -50px, -400px);transform:translate3d(-200px, -50px, -400px)}12%{-webkit-transform:translate3d(0px, 0, -100px);transform:translate3d(0px, 0, -100px)}25%{-webkit-transform:translate3d(200px, -50px, -400px);transform:translate3d(200px, -50px, -400px)}37%{-webkit-transform:translate3d(0px, -100px, -800px);transform:translate3d(0px, -100px, -800px)}50%{-webkit-transform:translate3d(-200px, -50px, -400px);transform:translate3d(-200px, -50px, -400px)}62%{-webkit-transform:translate3d(0px, 0, -100px);transform:translate3d(0px, 0, -100px)}75%{-webkit-transform:translate3d(200px, -50px, -400px);transform:translate3d(200px, -50px, -400px)}87%{-webkit-transform:translate3d(0px, -100px, -800px);transform:translate3d(0px, -100px, -800px)}100%{-webkit-transform:translate3d(-200px, -50px, -400px);transform:translate3d(-200px, -50px, -400px)}}@-o-keyframes roll{0%{transform:translate3d(-200px, -50px, -400px)}12%{transform:translate3d(0px, 0, -100px)}25%{transform:translate3d(200px, -50px, -400px)}37%{transform:translate3d(0px, -100px, -800px)}50%{transform:translate3d(-200px, -50px, -400px)}62%{transform:translate3d(0px, 0, -100px)}75%{transform:translate3d(200px, -50px, -400px)}87%{transform:translate3d(0px, -100px, -800px)}100%{transform:translate3d(-200px, -50px, -400px)}}@keyframes roll{0%{-webkit-transform:translate3d(-200px, -50px, -400px);transform:translate3d(-200px, -50px, -400px)}12%{-webkit-transform:translate3d(0px, 0, -100px);transform:translate3d(0px, 0, -100px)}25%{-webkit-transform:translate3d(200px, -50px, -400px);transform:translate3d(200px, -50px, -400px)}37%{-webkit-transform:translate3d(0px, -100px, -800px);transform:translate3d(0px, -100px, -800px)}50%{-webkit-transform:translate3d(-200px, -50px, -400px);transform:translate3d(-200px, -50px, -400px)}62%{-webkit-transform:translate3d(0px, 0, -100px);transform:translate3d(0px, 0, -100px)}75%{-webkit-transform:translate3d(200px, -50px, -400px);transform:translate3d(200px, -50px, -400px)}87%{-webkit-transform:translate3d(0px, -100px, -800px);transform:translate3d(0px, -100px, -800px)}100%{-webkit-transform:translate3d(-200px, -50px, -400px);transform:translate3d(-200px, -50px, -400px)}}@-webkit-keyframes roll-mid{0%{-webkit-transform:translate3d(-100px, -25px, -200px);transform:translate3d(-100px, -25px, -200px)}12%{-webkit-transform:translate3d(0px, 0, -50px);transform:translate3d(0px, 0, -50px)}25%{-webkit-transform:translate3d(100px, -25px, -200px);transform:translate3d(100px, -25px, -200px)}37%{-webkit-transform:translate3d(0px, -50px, -400px);transform:translate3d(0px, -50px, -400px)}50%{-webkit-transform:translate3d(-100px, -25px, -200px);transform:translate3d(-100px, -25px, -200px)}62%{-webkit-transform:translate3d(0px, 0, -50px);transform:translate3d(0px, 0, -50px)}75%{-webkit-transform:translate3d(100px, -25px, -200px);transform:translate3d(100px, -25px, -200px)}87%{-webkit-transform:translate3d(0px, -50px, -400px);transform:translate3d(0px, -50px, -400px)}100%{-webkit-transform:translate3d(-100px, -25px, -200px);transform:translate3d(-100px, -25px, -200px)}}@-o-keyframes roll-mid{0%{transform:translate3d(-100px, -25px, -200px)}12%{transform:translate3d(0px, 0, -50px)}25%{transform:translate3d(100px, -25px, -200px)}37%{transform:translate3d(0px, -50px, -400px)}50%{transform:translate3d(-100px, -25px, -200px)}62%{transform:translate3d(0px, 0, -50px)}75%{transform:translate3d(100px, -25px, -200px)}87%{transform:translate3d(0px, -50px, -400px)}100%{transform:translate3d(-100px, -25px, -200px)}}@keyframes roll-mid{0%{-webkit-transform:translate3d(-100px, -25px, -200px);transform:translate3d(-100px, -25px, -200px)}12%{-webkit-transform:translate3d(0px, 0, -50px);transform:translate3d(0px, 0, -50px)}25%{-webkit-transform:translate3d(100px, -25px, -200px);transform:translate3d(100px, -25px, -200px)}37%{-webkit-transform:translate3d(0px, -50px, -400px);transform:translate3d(0px, -50px, -400px)}50%{-webkit-transform:translate3d(-100px, -25px, -200px);transform:translate3d(-100px, -25px, -200px)}62%{-webkit-transform:translate3d(0px, 0, -50px);transform:translate3d(0px, 0, -50px)}75%{-webkit-transform:translate3d(100px, -25px, -200px);transform:translate3d(100px, -25px, -200px)}87%{-webkit-transform:translate3d(0px, -50px, -400px);transform:translate3d(0px, -50px, -400px)}100%{-webkit-transform:translate3d(-100px, -25px, -200px);transform:translate3d(-100px, -25px, -200px)}}