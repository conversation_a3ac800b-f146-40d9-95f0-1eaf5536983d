@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

.wizlet.wizletFormInputs {
   
    .card {

        .card-image {
            min-width: 30%;
            video { 
                display: block;
                position: relative;
                // position: absolute;
                left: 0; right: 0; top: 0; bottom: 0;
                width: 100%;
                height: 100%;
                background-color: black;
            }
        }
        
        .card-content {
            @include card-title; 
            
            .row {
                margin: 0;
            }
            

            form {

                .input-field {
                    min-height: 72px;
                }

                //Clicking the label does not activate the input without this
                input {
                    cursor: pointer;
                    font-size: inherit;

                    + label {
                        pointer-events: none;    
                        * { pointer-events: all;  }
                    }

                    &:disabled {
                        pointer-events: none;
                    }
                }   
                
                //Disable number spinners
                input[type="number"] {
                    -moz-appearance: textfield;
                    &::-webkit-outer-spin-button, 
                    &::-webkit-inner-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }
                }
                 

                .remaining {
                    min-height: 18px;
                    float: right;
                    font-size: 12px;
                }

                label:not(.active) {
                    ~ .remaining, ~ .character-counter {
                        visibility: hidden;
                    } 
                }



                $color_right: color("client-colors","turquoise");
                $color_wrong: color("client-colors","red");

                input {

                    @mixin input-color ($color) {
                        border-color: $color;
                        box-shadow: 0 1px 0 0 $color;
                        ~ .helper-text:after,
                        ~ label {
                            color: $color;
                        }
                    }

                    &.valid {
                        @include input-color($color_right);
                    }
                    &.invalid {
                        @include input-color($color_wrong);
                    }
                }

                .select-wrapper {

                    @mixin input-color ($color) {
                        border-color: $color;
                        box-shadow: 0 1px 0 0 $color;
                    }
                    @mixin help-color ($color) {
                        ~ .helper-text:after,
                        ~ label {
                            color: $color;
                        }
                    }

                    &.valid {
                        @include help-color($color_right);
                        > input {
                            @include input-color($color_right);
                        }
                    }

                    &.invalid {
                        @include help-color($color_wrong);
                        > input {
                            @include input-color($color_wrong);
                        }
                    }

                    input {
                        padding-right: 15px;
                        box-sizing: border-box;
                        text-overflow: ellipsis;
                    }
                    
                    img.avatar {
                        position: absolute;
                        bottom: 5px;
                        right: 25px;
                        width: 36px;
                    }

                    ul.select-dropdown li span {                        
                            color: color("client-colors", "font") ;
                        }

                }
                
    
            }

            .more_info {
                font-size: 1rem;
            }

        }
        
        .card-image, .card-content {
            background-color: color("client-colors", "white");
        }

    }

    @include row-submit;
    
}