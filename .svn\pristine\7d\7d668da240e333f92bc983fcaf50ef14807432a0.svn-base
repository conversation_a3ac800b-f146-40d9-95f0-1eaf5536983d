<Action>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: false,
      header: "!{}",
      animate: "zoomIn",
      class: "responsive-table _bigHeaders_ _smallHeaders_ firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Debrief_Scenario_Table}",
        body: "!{}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "", "!{KPI_LTU}", "!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}" ],
        rows: [
          [ "!{Choice_Opt1}", "!{SIM_R2_Scenario2_Opt1_LTUs}", "!{SIM_R2_Scenario2_Opt1_KPI1}", "!{SIM_R2_Scenario2_Opt1_KPI2}", "!{SIM_R2_Scenario2_Opt1_KPI3}","!{SIM_R2_Scenario2_Opt1_KPI4}" ],
          [ "!{Choice_Opt2}", "!{SIM_R2_Scenario2_Opt2_LTUs}", "!{SIM_R2_Scenario2_Opt2_KPI1}", "!{SIM_R2_Scenario2_Opt2_KPI2}", "!{SIM_R2_Scenario2_Opt2_KPI3}","!{SIM_R2_Scenario2_Opt2_KPI4}" ],
          [ "!{Choice_Opt3}", "!{SIM_R2_Scenario2_Opt4_LTUs}", "!{SIM_R2_Scenario2_Opt4_KPI1}", "!{SIM_R2_Scenario2_Opt4_KPI2}", "!{SIM_R2_Scenario2_Opt4_KPI3}","!{SIM_R2_Scenario2_Opt4_KPI4}" ]
        ],
        note: "!{}"
      },
      scope: ["Follower"]
    }]]>
  </Component>

</Action>