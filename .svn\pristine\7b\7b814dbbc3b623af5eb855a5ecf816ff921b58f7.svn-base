﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var AssessForm = function () {
        this.type = 'AssessForm';
        this.level = 1;     
        
    };

    AssessForm.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.votesBeforeUpdate = [];

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    AssessForm.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };


    AssessForm.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            
            options.context.find("#submitBtn").on('click', function (event) {
                var $btn = $(this);
                var $input = $btn.closest('.row').find('input');
                
                AjaxGetJson('Vote', 'Assess' + '?question=' + $btn.data("vote") + '&vote=' + $input.val() + '&email=' + $btn.data("user")).then(function (response) {
                    console.log(response);
                    
                    if (response.success) {
                        M.toast({
                            html: response.successMessage,
                            classes: 'rounded',
                            displayLength: options.wizletInfo.submitBtn.toastDuration || 2e3
                        });

                    } else {
                        M.toast({
                            html: response.message,
                            classes: 'rounded',
                            displayLength: options.wizletInfo.submitBtn.toastDuration || 2e3
                        });
                        
                    }
                });

            });

            if (options.wizletInfo.onclose)
            M.Modal.getInstance( options.context.closest('.modal')).options.onCloseEnd = function(current_item) {
                
                console.log('Recalculating Aggregator...')
                     
                var embedFile = options.wizletInfo.onclose.gdActionEmbed;
                var embedIdx = "embeddingAssess";
                                    
                var optionsObj = {actionXML: embedFile};
                var embedding = require('wizletEmbedding');
                embedding.unembedWizletExcept('MenuComponent');
                optionsObj.selector = '#'+embedIdx;
                optionsObj.noscroll = true;
                embedding.embedWizlet({
                    context: $(document),
                    actionScriptName: embedFile,
                    options: optionsObj
                }).then(function() { 
                    console.log('Aggregated :)')
                });
            }

            return true;
        })
        .fail(this.wizerApi.showError);
    };


    
    AssessForm.getRegistration = function () {
        return new AssessForm();
    };

    return AssessForm;

});