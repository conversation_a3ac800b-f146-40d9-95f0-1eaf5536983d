﻿// define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'jsCalcLib/numberFormatting', './datatables.min', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT, numberFormatting) {
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'jsCalcLib/numberFormatting', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT, numberFormatting) {

        var IndividualResults = function () {
            this.type = 'IndividualResults';
            this.level = 1;
            this.langDefaults = {
                sProcessing:     '',
                sLengthMenu:     '',
                sZeroRecords:    '',
                sEmptyTable:     '',
                sInfo:           '',
                sInfoEmpty:      '',
                sInfoFiltered:   '',
                sInfoPostFix:    '',
                sSearch:         '',
                sUrl:            '',
                sInfoThousands:  '',
                sLoadingRecords: '',
                oPaginate: {
                    sFirst:    '',
                    sLast:     '',
                    sNext:     '<i class="material-icons">arrow_forward</i>',
                    sPrevious: '<i class="material-icons">arrow_back</i>'
                },
                oAria: {
                    sSortAscending:  '',
                    sSortDescending: ''
                }
            }
        };

        IndividualResults.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
            this.wizletInfo = wizletInfo;
            this.wizletContext = wizletContext;
            this.wizerApi = wizerApi;
            this.votesBeforeUpdate = [];

            this.templateDefer = Q.defer();
            var self = this;
            var requirements = [];
            requirements.push(WizletBase.loadTemplate(wizletInfo, 'IndividualResults.dot'));
           
            if (wizletInfo.css) {
                requirements.push(WizletBase.loadCss(wizletInfo));
            }

            require(requirements, function (doTTemplate, css) {
                self.templateDefer.resolve(doTTemplate);
            });

    
            if (self.wizletInfo.liveUpdate) {
                
                $(document).one('wizer:action:init', function(e, currentAction) { 
                    $(document).on("wizer:action:latestVoteTimeChanged", { context: self }, self.reRenderOnLiveUpdate);
                });
                //$(document).on("wizer:action:latestVoteTimeChanged", { context: self }, self.reRenderOnLiveUpdate);
                // $(document).on("wizer:model:change", { context: self }, self.reRenderOnLiveUpdate);
            }
            //VideoPlayer.onLoad(null, info, content, wizerApi);
            return WizletBase.loadHandler({ wizlet: this, render: this.render });
        };

        IndividualResults.prototype.unloadHandler = function () {
            //unload wizletbase
            $(document).off("wizer:action:latestVoteTimeChanged", this.reRenderOnLiveUpdate);
            // $(document).off("wizer:model:change", this.reRenderOnLiveUpdate);
            WizletBase.unloadHandler({ wizlet: this });
        };

        IndividualResults.prototype.validate = function () {
            var self = this;

            var validating = new Q.defer();
            var promiseArr = [];
            if (self.wizletInfo.trackQuestion !== "" && self.wizletInfo.trackQuestion !== undefined) {
                this.trackQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackQuestion);
                var trackQuestionVotes = self.wizerApi.getMyVotes([this.trackQuestionId]);

                promiseArr.push(trackQuestionVotes);
                trackQuestionVotes.then(function (response) {
                    self.trackValue = response.votes[self.trackQuestionId][0];
                });
            }
            if (self.wizletInfo.rankQuestion !== "" && self.wizletInfo.rankQuestion !== undefined) {
                this.rankQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.rankQuestion);

                var rankQuestionVotes = self.wizerApi.getMyVotes([this.rankQuestionId]);

                rankQuestionVotes.then(function (response) {
                    self.myrankValue = Number(response.votes[self.rankQuestionId][0]);
                });
                promiseArr.push(rankQuestionVotes);
            }
            this.showNear = self.wizletInfo.showNear;

            this.avatarId = self.wizletInfo.boundAvatar ? self.wizerApi.getQuestionIdByName(self.wizletInfo.boundAvatar) : null;
            
            this.customNameId = self.wizletInfo.boundName ? self.wizerApi.getQuestionIdByName(self.wizletInfo.boundName) : null;

            this.questionrankId = [];
            this.questionIds = [];
            var questionId;
            if(self.wizletInfo.rank && self.wizletInfo.rank.binding) {
                this.questionrankId = self.wizerApi.getQuestionIdByName(self.wizletInfo.rank.binding);
            }
            for (var i = 0; i < self.wizletInfo.questions.length; i++) {
                questionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.questions[i].binding);
                //if (questionId !== undefined) {
                    this.questionIds.push(questionId.toString());
                //}
            }

            //The filters will be used to show registers which filter value is the same than user is watching the ranking
            //Thus, the user only will see data from other participants with the same condition used as filter
            if (self.wizletInfo.filters) {
                var filtersId = [];
                $.each (self.wizletInfo.filters, function(idx,filter) {
                    filtersId.push (self.wizerApi.getQuestionIdByName(filter));
                });
                var filterQuestions = self.wizerApi.getMyVotes(filtersId);
                filterQuestions.then(function (response) {
                    self.myfilters={};           
                    $.each (filtersId, function(idx,id) {
                        self.myfilters[id] = (response.votes[id][0]);
                    });
                });
                promiseArr.push(filterQuestions);

                self.filteredQuestions = [];
                $.each (self.wizletInfo.filteredQuestions, function(idx,filteredQuestion) {
                    self.filteredQuestions.push ( self.wizerApi.getQuestionIdByName(filteredQuestion) );
                });
            }

            if (self.wizletInfo.sortByQuestion !== "" && self.wizletInfo.sortByQuestion !== undefined) {
                self.sortByQuestionId = (self.wizerApi.getQuestionIdByName(self.wizletInfo.sortByQuestion)).toString();
            }
            else {
                self.sortByQuestionId = -1;
            }
            
            Q.all(promiseArr).then(function (response) {
                validating.resolve();
            });

            return validating.promise;
        };

        IndividualResults.prototype.reRenderOnLiveUpdate = function (event) {
            var self = event.data.context;
            delete self.rendering;
            return self.render();
        };

        IndividualResults.prototype.render = function () {
            var self = this;
            self.isDelivery = this.wizletInfo.isDelivery || false;
            if (self.rendering) {
                return self.rendering.promise;
            }
            self.rendering = new Q.defer();
                
            var validating = self.validate();
            validating.then(function () {
                var getVotesParameters = { questionIds: self.questionIds, isDelivery: self.isDelivery };
                if (self.trackQuestionId) {
                    getVotesParameters.filterQuestionId = self.trackQuestionId;
                }

                if (self.trackValue) {
                    getVotesParameters.filterText = self.trackValue;
                }

                var skipInList = self.wizletInfo.listSkip;
                if (!skipInList) skipInList = 0;

                var listMaxLength = self.wizletInfo.listLength;
                if (!listMaxLength) listMaxLength = 1000000;
                var votesByQuestionsPromise = null;
                if (self.rankQuestionId && self.showNear) {
                    votesByQuestionsPromise = self.wizerApi.getVotesInRankRange(self.rankQuestionId, self.myrankValue, self.showNear,self.isDelivery);
                    listMaxLength = (self.showNear * 2) + 1;
                }
                else {
                    votesByQuestionsPromise = self.wizerApi.getVotesByQuestions(getVotesParameters);
                }

                var votesBySortQuestionPromise = null;
                if (self.sortByQuestionId !== -1 && self.questionIds.indexOf(self.sortByQuestionId) === -1) {
                    votesBySortQuestionPromise = self.wizerApi.getVotesByQuestions({ isDelivery: false, questionIds: [self.sortByQuestionId] });
                }

    
                var customNamesPromise = null;
                if (self.customNameId) {
                    customNamesPromise = self.wizerApi.getVotes({questionIds: [self.customNameId]}).then(function (response) {
                        self.nameObj = self.createNamesObject(response);
                    });
                }
                
                var avatarsPromise = null;
                if (self.avatarId) {
                    avatarsPromise = self.wizerApi.getVotes({questionIds: [self.avatarId]}).then(function (response) {
                        self.avatarObj = self.createNamesObject(response);
                    });
                }

                var questionrankPromise = null;
                if (self.questionrankId) {
                    questionrankPromise = self.wizerApi.getVotes({questionIds: [self.questionrankId]}).then(function (response) {
                        self.questionrankObj = self.createNamesObject(response);
                    });
                }
                    
                var thingsToWaitForBeforeRendering = [
                    //self.templateDefer.promise,
                    self.wizerApi.loadQuestions(),
                    votesByQuestionsPromise,
                    votesBySortQuestionPromise,
                    customNamesPromise,
                    avatarsPromise,
                    questionrankPromise
                ];

                Q.all(thingsToWaitForBeforeRendering)
                .then(function (response) {

                    var votesSeen = 0;
                    var votesAdded = 0;

                    self.questionVotes = {};
                    self.questionVotes.questions = [];
                    var votes = {};
                    var participants = {};
                    var votesByQuestionUnfiltered = votesByQuestionsPromise.inspect().value;
                    if (self.sortByQuestionId !== -1 && self.questionIds.indexOf(self.sortByQuestionId) === -1) {
                        var votesBySortQuestion = votesBySortQuestionPromise.inspect().value;
                    }


                    // FILTER votes excluding the values equals to each filter defined for each question
                    if (self.wizletInfo.filters)  {
                        var qId2;
                        $.each(self.myfilters, function (qId, val) {
                        
                            //Only keep the registers with this filter in their parcitipantNames (i.e. for only emails using @ as filter)
                            if (self.wizletInfo.filterParticipantName) {
                                var contains = self.wizletInfo.filterParticipantName;
                                votesByQuestionUnfiltered[qId] = votesByQuestionUnfiltered[qId].filter(function(vote,idx) {
                                                                    if (vote.participantName.indexOf(contains) > -1) {
                                                                        return (val == vote.responseText) ;
                                                                    } else {
                                                                        return false;
                                                                    }
                                                                });
                            } else {
                                votesByQuestionUnfiltered[qId] = votesByQuestionUnfiltered[qId].filter(function(vote,idx) {
                                                                    return (val == vote.responseText) ;
                                                                });
                                
                            }
                            qId2 = qId;

                        });

                        //Get users filtered in other filter columns, to excluded from the FilteredQuestion column
                        if (self.wizletInfo.filteredQuestions) {
                            

                            $.each (self.filteredQuestions, function(idx,filteredQuestion) {
                                
                                if (votesByQuestionUnfiltered[filteredQuestion])  {

                                    var participantsFiltered = votesByQuestionUnfiltered[qId2].map(function (vote,idx) {
                                                                    return vote.participantName;
                                                                });
                                                                
                                    votesByQuestionUnfiltered[filteredQuestion] = votesByQuestionUnfiltered[filteredQuestion].filter(function(question) {
                                                                                        return (participantsFiltered.indexOf(question.participantName) > -1) ;
                                                                                    });
                                 
                                }

                            });

                        }
                    }

                    votesByQuestion = {};
                    if (self.wizletInfo.includeAdminScore === true) {
                        if (votesByQuestionUnfiltered != null) {
                            votesByQuestion = votesByQuestionUnfiltered;
                        }
                    } else {
                        $.each(self.questionIds, function (index, qId) {
                            var votes = votesByQuestionUnfiltered[qId];
                            if (votes != null) {
                                votesByQuestion[qId] = $.grep(votes, function (vote) {
                                    return !vote.participantIsAdmin;
                                });
                            }
                        });
                    };


                    $.each(self.questionIds, function (index, qId) {
                        if (votesByQuestion[qId] != null) {
                            $.each(votesByQuestion[qId], function (index, vote) {
                                if (votes[vote.participantName] == null) {
                                    votes[vote.participantName] = {};
                                }
                                votes[vote.participantName][qId] = self.getDisplayResponseText(qId, vote);
                                participants[vote.participantName] = vote.participantName;
                            });
                        }
                        self.questionVotes.questions.push(self.lookupQuestionName(qId));
                    });

                    var sortQuestionVotes = {};
                    if (self.sortByQuestionId !== -1 && self.questionIds.indexOf(self.sortByQuestionId) === -1) {
                        if (votesBySortQuestion && votesBySortQuestion[self.sortByQuestionId])
                        $.each(votesBySortQuestion[self.sortByQuestionId], function (index, vote) {
                            sortQuestionVotes[vote.participantName] = self.getDisplayResponseText(self.sortByQuestionId, vote);
                        });
                    }

                    self.questionVotes.participationVotes = [];
                    var sortByParticipant = false;
                    if (self.sortByQuestionId && self.sortByQuestionId != -1) {
                        sortByParticipant = false;
                    } else {
                        self.sortByQuestionId = self.questionIds[0];
                        sortByParticipant = true;
                    }
                    var sortedVotes = [];
                    var emptyVotes = [];
                    var nonEmptyVotes = [];
                    var sortDescending = self.wizletInfo.sortOrder == "desc";
                    $.each(participants, function (index, participant) {
                        if (!(self.sortByQuestionId in (votes[participant]))) {
                            if (!votesByQuestion[self.sortByQuestionId]) {
                                votesByQuestion[self.sortByQuestionId] = [];
                            }

                            if (self.sortByQuestionId !== -1 && self.questionIds.indexOf(self.sortByQuestionId) === -1) {
                                var vote = sortQuestionVotes[participant];
                                votesByQuestion[self.sortByQuestionId].push({ participantName: participant, responseText: vote });
                            }
                            else {
                                votesByQuestion[self.sortByQuestionId].push({ participantName: participant, responseText: '-' });
                            } 
                        }
                    });

                    if (self.sortByQuestionId in votesByQuestion) {
                        $.each(votesByQuestion[self.sortByQuestionId], function (index, vote) {
                            var sortVote = {}
                            var sortKey = vote.participantName;
                            if (!sortByParticipant) {
                                sortKey = self.getDisplayResponseText(self.sortByQuestionId, vote);
                            }

                            if (numberFormatting.unformat(sortKey) != 0) {
                                sortKey = numberFormatting.unformat(sortKey);
                            }
                            
                            if ($.isNumeric(sortKey)) {
                                sortKey = parseFloat(sortKey);
                            }
                            sortedVotes.push({ sortKey: sortKey, vote: vote });
                        });
                    }

                    for (var i = 0; i < sortedVotes.length; i++) {
                        if(sortedVotes[i].sortKey == undefined) {
                            emptyVotes.push(sortedVotes[i]);
                        }
                        else {
                            nonEmptyVotes.push(sortedVotes[i]);
                        }
                    }
                    self.sortUndefinedLow = sortDescending;
                    nonEmptyVotes.sort(self.ascendingByVoteOrParticipation);
                    if (sortDescending) {
                        nonEmptyVotes.reverse();
                    }
                    for (var i = 0; i < emptyVotes.length; i++) {
                        nonEmptyVotes.push(emptyVotes[i]);
                    }
                    var i = 0;
                    
                    $.each(nonEmptyVotes, function (index, sortVote) {
                        votesSeen++;
                        if (skipInList >= votesSeen) return; // continue
                        if (votesAdded >= listMaxLength) return false; // break
                        votesAdded++;

                        var vote = sortVote.vote;
                        //if(!vote.participantId) return;
                        var onePaticipation = {};
                        onePaticipation.participationName = vote.participantName;

                        if (self.nameObj) {
                            var myName = self.nameObj[vote.participantName];
                            onePaticipation.customName = myName ? 
                                                            ((self.wizletInfo.showOriginalName ? (vote.participantName+' - ') : '') + myName) : 
                                                            vote.participantName;
                        } else {
                            onePaticipation.customName = vote.participantName;
                        }
                        if (self.avatarObj) {
                            onePaticipation.customAvatar = self.avatarObj[vote.participantName] ? 
                                                            self.avatarObj[vote.participantName] : 
                                                                (self.wizletInfo.defaultAvatar ? self.wizletInfo.defaultAvatar : '');
                        } 
                        
                        if (self.questionrankObj) {
                            onePaticipation.rank = self.questionrankObj[vote.participantName] ? 
                                                            self.questionrankObj[vote.participantName] : '';
                        } 
                        onePaticipation.index = i++;
                        var participationVotes = votes[onePaticipation.participationName];
                        onePaticipation.votes = [];
                        var existValues=false;
                        
                        $.each(self.questionIds, function (index, qId) {
                            var question = self.wizletInfo.questions[index];
                            if (question.show) {
                                if (participationVotes && participationVotes[qId]) {
                                    // var vote = (question.showdecimals) ?
                                    //                 (participationVotes[qId]*1).toFixed(2) : 
                                    //                 (participationVotes[qId]);
                                    var vote = participationVotes[qId];
                                    var format = question.format;
                                    var timestamp = question.timestamp;
                                    if (timestamp) {
                                        var date = new Date(vote);
                                        vote =  ("0"+date.getHours()).slice(-2) + ":" + 
                                                ("0"+date.getMinutes()).slice(-2) + ":" + 
                                                ("0"+date.getSeconds()).slice(-2);
                                    }
                                    if (format) {
                                        vote = numberFormatting.format(vote, format);
                                    }
                                    if (question.prefix)
                                        vote = question.prefix + " " + vote;
                                    if (question.suffix) {
                                        vote = vote + " " + question.suffix;
                                    }
                                    
                                    onePaticipation.votes.push(vote);
                                    existValues = true;
                                }
                                else {
                                    onePaticipation.votes.push('-');
                                    existValues = existValues || false;
                                }
                            } 
                        });
                        if (existValues || self.wizletInfo.showEmpties)
                            self.questionVotes.participationVotes.push(onePaticipation);
                    });
                    
                    if (self.wizletInfo.markUpdate) {
                        for (var i = 0; i < self.votesBeforeUpdate.length; i++) {
                            for (var j = 0; j < self.questionVotes.participationVotes.length; j++) {
                                if (self.questionVotes.participationVotes[j].participationName === self.votesBeforeUpdate[i].participationName) {
                                    if (self.questionVotes.participationVotes[j].index > self.votesBeforeUpdate[i].index) {
                                        self.questionVotes.participationVotes[j].class = 'demote';
                                    }
                                    else if (self.questionVotes.participationVotes[j].index < self.votesBeforeUpdate[i].index) {
                                        self.questionVotes.participationVotes[j].class = 'promote';
                                    }
                                    break;
                                }
                            }
                        }

                        self.votesBeforeUpdate = self.questionVotes.participationVotes;
                    }

                    self.templateDefer.promise.then(function (doTTemplate) {
                        //{ title: self.wizletInfo.title, text: self.wizletInfo.text, columnHeaders: self.wizletInfo.questions, questionVotes: self.questionVotes }
                        //self.wizletInfo.columnHeaders = self.wizletInfo.questions;
                        self.wizletInfo.columnHeaders = self.wizletInfo.questions.filter(function(question) {
                                                                return question.show
                                                            });
                        self.wizletInfo.questionVotes = self.questionVotes;
                        self.wizletContext.html('');
                        self.wizletContext.append(doTTemplate(self.wizletInfo));


                        // ********** DATA-TABLES ********************
                        if (self.wizletInfo.isDataTables) {
                            var dataTablesLang = self.wizletInfo.dataTablesLang ? self.wizletInfo.dataTablesLang : {};

                            var language = $.extend(true, {}, self.langDefaults, dataTablesLang);


                            var order = self.wizletInfo.dataTablesOrder ? self.wizletInfo.dataTablesOrder : [[0, "asc"]];
                            var DT = self.wizletContext.find('.dataTables').DataTable( {
                                order: order,
                                columnDefs: [
                                    {
                                        targets: [0,1,2],   //avatar & name
                                        orderable: false
                                    }
                                ],
                                                            
                                language: language,

                                _lengthMenu: [[5, 10, 25, 50, 100, -1], [5, 10, 25, 50, 100, dataTablesLang.lengthAll]],
                                
                                lengthMenu: [[-1], [dataTablesLang.lengthAll]]

                            } );
                            
                            //Keep first column (position) always the same order from 1 to..
                            if (self.wizletInfo.dataTablesFixOrder)
                            DT.on( 'order.dt search.dt', function () {
                                DT.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
                                    cell.innerHTML = i+1;
                                } );
                            } ).draw();
                            
                            
                            if (! self.wizletInfo.dataTablesHideControls) {
                            self.wizletContext.find('.dataTables_wrapper select').val('5');
                            //self.wizletContext.find('.dataTables_wrapper select').addClass("browser-default");
                            self.wizletContext.find('.dataTables_wrapper select').attr('single',true);
                            self.wizletContext.find('.dataTables_wrapper select').formSelect();
                            }

                        }

                        self.rendering.resolve();
                    });
                    
                }, function (error) {
                    //This will be called if $q.all finds any of the requests erroring.
                });
            });

            return self.rendering.promise;
            //})
            //.fail(this.wizerApi.showError)
        };

        IndividualResults.prototype.createNamesObject = function (response) {

            var nameObj = {};
            $.each(response.votes, function (idx, name) {
                nameObj[name.participantName] = name.responseText;
            });

            return nameObj;
        };


        IndividualResults.prototype.ascendingByVoteOrParticipation = function (a, b) {
            var x = a.sortKey;
            var y = b.sortKey;

            if (x == undefined) {
                if (y == undefined) {
                    return 0;
                } else {
                    return IndividualResults.sortUndefinedLow ? -1 : 1;
                }
            } else if (y == undefined) {
                return IndividualResults.sortUndefinedLow ? 1 : -1;
            }
            return ((x > y) ? 1 : ((x < y) ? -1 : 0));
        };

        IndividualResults.prototype.lookupQuestionName = function (questionId) {
            return this.wizerApi.getQuestion(questionId).QuestionText;
        };

        IndividualResults.prototype.getDisplayResponseText = function (questionId, vote) {
            var question = this.wizerApi.getQuestion(questionId);
            if (question.Options && question.Options.length > 0) {
                return this.getOptionText(question.Options, vote.responseText);
            }
            return vote.responseText;
        };

        IndividualResults.prototype.getOptionText = function (options, answer) {
            var result = answer;
            $.each(options, function (index, option) {
                if (option.KeypadNumber == answer) {
                    result = option.ResponseText;
                }
            });
            return result;
        };
        IndividualResults.prototype.setHeight = function(options) {
            $('.js-set-height').each(function(){
                var h = $(this).next().outerHeight();
                //$(this).height(h);
            });
        }

        IndividualResults.getRegistration = function () {
            return new IndividualResults();
        };

        return IndividualResults;

});