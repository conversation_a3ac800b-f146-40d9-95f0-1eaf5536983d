<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
      <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- ###### -->
    <!-- SCORES -->
    <!-- ###### -->

    <!-- Check if choice 1 selected in some previous scenario -->
    <Score result="Q_SIM_R1_Scenario1acum_1" type="Choice" method="sum">
      <Question name="Q_SIM_R1_Scenario1" value="1" Response="1"></Question>
      <Question name="Q_SIM_R1_Scenario1b" value="1" Response="1"></Question>
    </Score>
    <!-- Check if choice 2 selected in some previous scenario -->
    <Score result="Q_SIM_R1_Scenario1acum_2" type="Choice" method="sum">
      <Question name="Q_SIM_R1_Scenario1" value="2" Response="1"></Question>
      <Question name="Q_SIM_R1_Scenario1b" value="2" Response="1"></Question>
    </Score>
    <!-- Check if choice 3 selected in some previous scenario -->
    <Score result="Q_SIM_R1_Scenario1acum_3" type="Choice" method="sum">
      <Question name="Q_SIM_R1_Scenario1" value="3" Response="1"></Question>
      <Question name="Q_SIM_R1_Scenario1b" value="3" Response="1"></Question>
    </Score>

    <!-- LTUs -->
    <Score result="Score_SIM_R1_Scenario1b_LTUs" type="Choice">
      <Question name="Q_SIM_R1_Scenario1b">
        <Choice value="1" Response="!{SIM_R1_Scenario1b_Opt1_LTUs}"></Choice>
        <Choice value="2" Response="!{SIM_R1_Scenario1b_Opt2_LTUs}"></Choice>
        <Choice value="3" Response="!{SIM_R1_Scenario1b_Opt3_LTUs}"></Choice>
        <Choice value="4" Response="!{SIM_R1_Scenario1b_Opt4_LTUs}"></Choice>
      </Question>
    </Score>


    <!-- ###### -->
    <!-- TOTALS -->
    <!-- ###### -->

    <!-- LTUs -->
    <Total result="Score_SIM_Total_LTUs" method="sum">
      <Question validate="false">Score_SIM_Init_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario1b_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario1c_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_LTUs</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_LTUs</Question>
    </Total>

    <!-- KPI1: Engagement -->
    <Total result="Score_SIM_Total_KPI1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI1</Question>
    </Total>

    <!-- KPI2: Safety-->
    <Total result="Score_SIM_Total_KPI2" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI2</Question>
    </Total>

    <!-- KPI3: Driving Results-->

    <!-- Penalty:
        - If MTUs are between -1 to -2 ==> Driving Results is impacted by: -1
        - If MTUs are between -3 to -4 ==> Driving Results is impacted by: -2
        - If MTUs are < -4 ==> Driving Results is impacted by: -3
      -->
    <Score type="Range" result="Score_SIM_R1_KPI3penalty">
      <Question>Score_SIM_Total_LTUs</Question>
      <Boundary value="-3">-4</Boundary>
      <Boundary value="-2">-2</Boundary>
      <Boundary value="-1">0</Boundary>
      <Boundary value="0">99</Boundary>
    </Score>

    <Total result="Score_SIM_Total_KPI3" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI3</Question>
      <Question validate="false">Score_SIM_R1_KPI3penalty</Question>
    </Total>

    <!-- KPI4: Preparing for the Future -->
    <Total result="Score_SIM_Total_KPI4" method="sum">
      <Question validate="false">Score_SIM_Init_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI4</Question>
    </Total>

    <!-- KPI: TOTAL -->
    <Total result="Score_SIM_Total" method="avg">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
      <Question validate="false">Score_SIM_Total_KPI2</Question>
      <Question validate="false">Score_SIM_Total_KPI3</Question>
      <Question validate="false">Score_SIM_Total_KPI4</Question>
    </Total>
    <Total result="Score_SIM_Total_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total</Question>
    </Total>

    <!-- ROUND1 - KPI: TOTAL -->
    <Total result="Score_SIM_Total_R1_LTUs" method="sum">
      <Question validate="false">Score_SIM_Total_LTUs</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI4" method="sum">
      <Question validate="false">Score_SIM_Total_KPI4</Question>
    </Total>
    <Total result="Score_SIM_Total_R1" method="sum">
      <Question validate="false">Score_SIM_Total</Question>
    </Total>

  </Aggregator>


</Action>