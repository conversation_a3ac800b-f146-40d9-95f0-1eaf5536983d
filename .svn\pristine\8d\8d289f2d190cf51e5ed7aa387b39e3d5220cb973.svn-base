@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


.wizlet.wizletSubmitButton .submitbutton {

    &.fixed {
        position: fixed;
        right: 10px;
        bottom: 40px;
        z-index: 999;
    }

    &> .row {
        clear: both;
    }

    &> .row.fixed {
        float: none !important;

        &.left {
            text-align: left;
        }
        &.center {
            text-align: center;
        }
        &.right {
            text-align: right;
        }
    }

    .btn-large {
        font-size: inherit;
    }

    span a[href] {
        color: color("client-colors", "font2");
    }
    span[disabled] a {
        pointer-events: none;
        background-color: $button-disabled-background !important;
        box-shadow: none;
        color: $button-disabled-color !important;
        cursor: default;
        &:hover {
            background-color: $button-disabled-background !important;
            color: $button-disabled-color !important;
        }
    }
    
    @include row-submit;
}

