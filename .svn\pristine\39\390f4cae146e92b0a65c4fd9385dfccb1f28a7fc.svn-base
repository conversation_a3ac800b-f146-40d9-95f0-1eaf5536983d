<?xml version="1.0" encoding="utf-8" ?>
<!-- <Action mainAreaLayout="../../../layout/mainLayoutIntro" layout="../../../layout/tabsLayoutDebrief"> -->
<Action mainAreaLayout="../../../layout/mainLayoutIntro" layout="../../../layout/layoutDebrief">
  <Include name="Header_Intro"></Include>
  
  <!-- <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{R1_Debrief_Scenario1_Header} ",
      tabs: [
          "!{Header_LinkScenario}",
          "!{Header_LinkResults}"
      ],
      scope: null
  }]]></Component>

  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1_Img}",  alt: "!{SIM_R1_Scenario1_Title}" ,
          position: "right large", 
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1_Img}",  alt: "!{SIM_R1_Scenario1_Title}" ,
          isHiddenWhenSmall: true, 
          src_vert: "!{}"
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{SIM_R1_Scenario1_Title}",
        body: "!{SIM_R1_Scenario1_Text}"
      }      
    }]]>
  </Component> -->


  <!-- Next Tab Button  -->
  <!-- <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "zoomIn",
      id: "btn_action1", isHidden: false, 
      title: "!{Header_LinkResults}",
      icon: "redo",
      _onclick: "$(this).hide(); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanel1&quot;')[0].click()",
      onclick: "$(this).removeClass('pulse'); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanel1&quot;')[0].click()",
      pulse: false,
      color: "",
      
      _isFollower: "Follower",
      scope: [""]
    }]]>
  </Component>  -->
  
  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{R1_Debrief_Scenario1_Header}",
      subheader: "!{R1_Debrief_Scenario1_Text}",
      valign: false,
      animate: "fadeIn",
      content: {
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{}"
      }      
    }]]>
  </Component>

  
  <Include name="R1_Debrief_Scenario1_chart"></Include>
  <Include name="R1_Debrief_Scenario1_table"></Include>

  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1_FB_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1_FB_Opt1_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario1_FB_Opt1_Title}",
        body: "!{SIM_R1_Scenario1_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario1_Opt1_LTUs}","!{SIM_R1_Scenario1_Opt1_KPI1}","!{SIM_R1_Scenario1_Opt1_KPI2}","!{SIM_R1_Scenario1_Opt1_KPI3}","!{SIM_R1_Scenario1_Opt1_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1",
        condition_Diff: "2",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1_FB_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1_FB_Opt2_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario1_FB_Opt2_Title}",
        body: "!{SIM_R1_Scenario1_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario1_Opt2_LTUs}","!{SIM_R1_Scenario1_Opt2_KPI1}","!{SIM_R1_Scenario1_Opt2_KPI2}","!{SIM_R1_Scenario1_Opt2_KPI3}","!{SIM_R1_Scenario1_Opt2_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1",
        condition_Diff: "3",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1_FB_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1_FB_Opt3_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario1_FB_Opt3_Title}",
        body: "!{SIM_R1_Scenario1_FB_Opt3_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario1_Opt3_LTUs}","!{SIM_R1_Scenario1_Opt3_KPI1}","!{SIM_R1_Scenario1_Opt3_KPI2}","!{SIM_R1_Scenario1_Opt3_KPI3}","!{SIM_R1_Scenario1_Opt3_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>

  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>
  

</Action>