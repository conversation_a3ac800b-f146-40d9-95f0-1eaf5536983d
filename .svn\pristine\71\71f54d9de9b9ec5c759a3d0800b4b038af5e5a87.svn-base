@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

// .tab-component .wizlet.wizletChatBox > .container {
//     margin: 0;
//     width: initial;
// }

.wizlet.wizletChatBox {
   
    .card {
        //background-color: color("client-colors", "background2");
        background-color: color("client-colors", "white");

        padding: 5px 0;
        @include border-radius($card-panel-border);

        .card-content {
            //@include card-title; 

            .header.title {
                margin: 0;
                color: color("client-colors", "font2");
            }

            @include scroll-y-hidden();        
            padding: 0 10px;
            
            &.chatbox {
                height: 100%;
                overflow-x: hidden;

                .collection {
                    .collection-item {
                        i.circle {
                            background-color: color("client-colors", "secondary");
                        }
                        .title {
                            font-weight: bold;
                        }
                        &.avatar.right-align {
                            padding-left: 20px;
                            padding-right: 72px;
                            i.circle {
                                left: initial;
                                right: 15px;
                            }
                            img {
                                left: 0;
                            }
                        }
                        &.avatar.left-align {
                            img {
                                left: 0;
                            }
                        }
                        &.avatar.with-img {
                            i {
                                background-color: transparent;
                            }
                            img {
                                left: 0;
                                transform: scale(1.1);
                            }
                        }
                    }
                }
            }
            

            .input {                
                .remaining {
                    min-height: 18px;
                    float: right;
                    font-size: 12px;
                }
            }

        }

        &.auto-height {
            
            height: auto;
            min-height: auto;
            &.small {
                max-height: 300px;
            }
            &.medium {
                max-height: 400px;
            }
            &.large {
                max-height: 500px;
            }

            .chatbox {
                max-height: inherit;
            }

        }

    }
    

}