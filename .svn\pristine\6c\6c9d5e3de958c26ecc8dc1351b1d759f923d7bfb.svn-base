﻿/* 
    "Plain Vanilla" wizlet that takes a template and some data as the only input
    and renders the template with functionality from wizletBase, and nothing else.
*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT'], function ($, Q, WizerApi, WizletBase, doT) {

    var Vanilla = function () {
        this.type = 'Vanilla';
        this.level = 1;
    };

    Vanilla.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        if (wizletInfo.templateInEvent) {
            requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        }
        else {// either 1 from templateInEvent or template should be present
            if (wizletInfo.template.toLowerCase().indexOf(wizerApi.eventName().toLowerCase() + "/") == -1 && wizletInfo.template.toLowerCase().indexOf("/wizer/content/wizard/") == -1 && wizletInfo.template.toLowerCase().indexOf('vanilla/test') == -1) {
                // if template is defined in core, load it from theme folder
                var templateName = wizletInfo.template.split("/").pop();
                requirements.push('doT!' + wizletInfo.templatePath + templateName);
            } else {
                requirements.push('doT!' + wizletInfo.template);
            }

        }
        // if (wizletInfo.css) {
        //     requirements.push('css!' + wizletInfo.css);
        // }
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    Vanilla.prototype.unloadHandler = function () {
        $('.material-tooltip').remove();
        WizletBase.unloadHandler({ wizlet: this });
    };

    Vanilla.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            for(var i=0;i<options.wizletInfo.nextSections.length; i++)
            {
                self.wizerApi.loadActionInContainer(options.wizletInfo.nextSections[i].actionXML, options.context, self.unsedContext, $('.action-'+i)) ;
            }

            setTimeout(
                function() { 
                    options.context.find(".next-btn-container").removeClass('hidden');
                }, 
                2000
            );

            options.context.find(".next-btn").on("click", function (e) {

                //$(this).attr('disabled',true).removeClass('pulse');
                $(this).hide();
                $(this).parents('.next-btn-container').attr('hidden',true);

                var idx= parseInt($(this).data('index'));
                options.context.find("#next-section"+idx).removeAttr('hidden');
                options.context.find("#next-btn"+(idx+1)+'.showable').removeAttr('hidden');

                if (options.context.find("#next-section"+(idx+1)+'.navigation').hasClass('showable'))
                    options.context.find("#next-section"+(idx+1)).removeAttr('hidden');

            });


            return true;
        })
        .fail(this.wizerApi.showError)
    };

    Vanilla.getRegistration = function () {
        return new Vanilla();
    };

    return Vanilla;

});
