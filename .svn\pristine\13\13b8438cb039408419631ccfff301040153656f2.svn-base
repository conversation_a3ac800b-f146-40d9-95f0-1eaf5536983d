@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";



.wizlet.wizletCheckBoxes {
    
    @include header-help();

    .container {
        
        &.borders {
            @include card-border(10px,double); 
        }
    }
    
    @include header-badge;


    .card {
        .card-content {

            background-color: color("client-colors", "white");

            .row {
                margin: 0;
            }
            @include header-badge;
            .header {
                margin-top: 0;
            }
            @include card-title;
            .embed {
                clear: both;
                width: 40%;
                @media #{$small-and-down} {
                    width: 100%;
                }
                // @media #{$large-and-up} {
                //     width: 50%;
                // }
                &.left {
                    float: left;
                    margin-right: 12px;
                }
                &.right {
                    float: right;
                    margin-left: 12px;
                }
                &.xtiny {
                    width: 10%;
                }
                &.tiny {
                    width: 20%;
                }
                &.small {
                    width: 30%;
                }
                &.large {
                    width: 50%;
                }
                &.extralarge {
                    width: 70%;
                }
            }
        }
    }
    
    form {
    
        >ul{
            .subheader {
                color: color("client-colors", "font3");
                width: 100%;
                clear: both;
            }

            >li{
                $color: color("client-colors", "secondary");
                $color-on: color("client-colors", "primary");
                $color-off: color("client-colors", "light-grey");
                $color-off2: color("client-colors", "grey");
                $color-off3: color("client-colors", "dark-grey");
                $color-check: color("client-colors", "white");
                $color-text: color("client-colors", "font");
                $color-text2: color("client-colors", "font2");

                &:not(:last-child) {
                    border-bottom: 1px solid color("client-colors", "border");
                }
                padding: 10px 5px;
                margin: 0;

                &.chosen {                    
                    .hoverable {
                        box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
                    }
                    
                    label .question-label {
                        background-color: $color-on;
                    }
                }

                @include no-select-highlight;
      
                label {

                    span.question-label {
                        color: $color-text2;
                        position: absolute;
                        left: 0;
                        top: 0;
                        transform: translateX(-75%) translateY(-25%);
                        padding: 0 4px;
                        min-width: 2rem;
                        background-color: $color;
                        
                        @media #{$small-and-down} { 
                            transform: translateX(-110%) translateY(-25%);
                            min-width: 1.5rem;
                        }
                    }

                    span.title {
                        color: $color-text;
                     
                        &.title-align-items-center {
                            display: flex;
                            align-items: center;
                        } 
                        &.with-image {
                            .option {
                                &.left { padding-left: 0;}
                                &.right { padding-right: 0;}
                            }
                            .image img {
                                margin-top: -10px;
                            }
                        } 

                        .col.image {
                            padding: 0;
                        }
                        @media #{$small-and-down} { 
                            // .col.image {
                            //     display: none;
                            // }
                            // .col.option {
                            //     width: 100%;
                            // }
                            .col.image > img{
                                max-height: 3rem;
                            }
                        }
                    }
                }  
                

                cursor: pointer;
                &:hover {
                    label{
                        color: $color-on;
                    }   
                        
                    [type="checkbox"] + span:after {
                        border-color: $color-on;
                    }
                }       
                

                &.no-separator {
                    border-bottom: none;
                }
                &.reduced {
                    padding-top: 2px;
                    padding-bottom: 0px;

                    .card-panel {

                        span.question-label {
                            transform: translateX(-85%) translateY(-25%);
                            min-width: 1.5rem;
                        }

                        span.title {
                            font-weight: normal;
                        }
                    }
                }

                .subheader {
                    color: color("client-colors", "font3");
                    margin-bottom: 1rem;
                }
                
                [type="checkbox"] {
                    + span {
                        font-weight: bold;
                        font-size: 1.2rem;
                        height: auto;
                        padding-left: 50px;
                        
                        &:before {
                            height: 28px !important;
                            width: 14px !important;
                            top: -7px !important;
                            border-width: 4px !important
                        }
                        &:after {
                            height: 36px !important;
                            width: 36px !important;
                            margin-top: -7px;
                        }
                        @media #{$small-and-down} { 
                            padding-left: 35px; 
                            &:before {
                                // display: none;
                                height: 20px !important;
                                width: 9px !important;
                                top: -1px !important;
                                border-width: 3px !important
                            }
                            &:after {
                                // display: none;
                                height: 24px !important;
                                width: 24px !important;
                                margin-top: 1px;
                            }         
                        }
                    }
                    &:checked + span {
                        & {
                            color: $color-on;
                        }
                        &:before {
                            border-right-color: $color-check;
                            border-bottom-color: $color-check;
                        }
                        &:after {
                            border-color: $color-on;
                            background-color: $color-on;
                        }
                    }

                    &:disabled + span {
                        color: $color-off3;
                        &:after {
                            border-color: $color-off !important;
                            background-color: $color-off !important;
                        }
                        &.title:not(.isSolution) .option-image {
                            filter: grayscale(1);
                        }
                    }
                }
    
                .card-panel {
                    position: relative;
                    @media #{$medium-and-down} { 
                        &.hide-label {
                            padding: 12px 12px 0 12px;
                        }
                    }      
                    @media #{$small-and-down} { 
                        margin: 0;
                        padding: 12px 12px 6px 12px;
                    }

                    i.check-icon {
                        position: absolute;
                        top: 0;
                        z-index: 1;
    
                        &.left {
                            left: 0;
                        }
                        &.right {
                            right: 0;
                        }
                        &.scaled-out {
                            transform: scale(0);
                        }
                    }

                }
      

                ul.collapsible {
                    >li {
                        background-color: color("client-colors", "white") ;

                        .collapsible-header {
                            font-size: 90%;
                            border: none;
                            padding: 0.5rem;
                        }
                        .collapsible-body {
                            // font-size: 95%;
                            border: none;
                            padding: 0 1rem 1rem 1rem;
                        }
                    }
                }

                &.follower {
                    pointer-events: none;
                } 


                &.disabled,
                &[disabled] {
                    pointer-events: none;                    
                    ul.collapsible {
                        pointer-events: initial;
                    }

                    &:not(.chosen) {
                        opacity: 0.8;
                        label, label span.title:not(.isSolution) {
                            color: $color-off2;
                        }
                        label span.title:not(.isSolution)::after {
                            border-color: $color-off2;
                        }
                        &[data-correct="true"] label {
                            color: $color-off3;
                        }
                        .question-label {
                            background-color: $color-off3;
                        }
                        
                        ul.collapsible {
                            i, span {
                                color: $color-off3;
                            }
                        }
                    
                    }

                    
                    &.chosen {
                        [type="checkbox"] {

                            &:disabled + span {
                                color: $color-on;
                                &:after {
                                    border-color: $color-on !important;
                                    background-color: $color-on !important;
                                }
                            }
                        }
                    }

                }

            }
        }
        
        >ul.inline{
            @include flexbox();
            @include flex-wrap();

            >li.col{
                @include flexbox();
                @include flex-direction(column);
                display: grid;
                margin: 0;
                &:last-child {
                    border-bottom: 1px solid color("client-colors", "border");
                }
            }
            &.centered {
                text-align: center;
                display: table;
                >li.col{
                    display: inline-block;
                    float: none;
                    text-align: left;
                    border: none;
                }
            }
            &.small {
                .card-panel {
                    padding-right: 12px;
                    padding-bottom: 0;
                }
            }
        }
    }
    
    @include row-submit;
    #checkBtn {
        margin-left: 15px;
    }
}
  