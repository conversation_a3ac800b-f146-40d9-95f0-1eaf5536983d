<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayout_SIM_R1" layout="../../../layout/tabsLayout2">
  <Include name="Header_SIM_R1"></Include>
  <Include name="KPIgauges_R1"></Include>


  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "bounceInLeft",
      progress: "3",
      steps: [ "!{SIM_BreadCrumbs_0}",
               "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}" ]
    }]]>
  </Component>

  <Component type="Prioritize" customJS="true"><![CDATA[{
    css: "styles/prioritize.css",
    "header": {
      "title": "!{SIM_R1_Scenario2_FB_Header}",
      "description": "!{SIM_R1_Scenario2_Text}<br>!{SIM_R1_Scenario2_Instructions}"
    },
    "instructions": "!{Prioritize_Feedback_Answers}",
    "type": "QUESTIONS",
    "readOnlyCondition": true,
    "isFollower": "Follower",
    "trackTeam": "Team",
    "version": "2.0",
    "questions": [
      {
        "binding": "Q_SIM_R1_Scenario2_Opt3",
        "title": "!{SIM_R1_Scenario2_Opt3}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt4",
        "title": "!{SIM_R1_Scenario2_Opt4}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt6",
        "title": "!{SIM_R1_Scenario2_Opt6}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt2",
        "title": "!{SIM_R1_Scenario2_Opt2}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt1",
        "title": "!{SIM_R1_Scenario2_Opt1}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt5",
        "title": "!{SIM_R1_Scenario2_Opt5}"
      }
    ],
    "buttons": [
      {
        "navigation": {
          "title": "!{Navigation_submit}",
          "history": false,
          "message": "!{InputSubmited}",
          "idToShow": "navButton"
        }
      },
      {
        "navigation": {
          "title": "!{Navigation_submitModalClose}",
          "history": false
        }
      }
    ],
    "scope": [
      "Q_SIM_R1_Scenario2_Opt1",
      "Q_SIM_R1_Scenario2_Opt2",
      "Q_SIM_R1_Scenario2_Opt3",
      "Q_SIM_R1_Scenario2_Opt4",
      "Q_SIM_R1_Scenario2_Opt5",
      "Q_SIM_R1_Scenario2_Opt6",
      "Follower"
    ]
  }]]></Component>


  <Component type="Prioritize" customJS="true"><![CDATA[{
    css: "styles/prioritize.css",
    "header": {
      "title": "!{}",
      "description": "!{SIM_R1_Scenario2_FB_Text}"
    },
    "instructions": "!{Prioritize_Feedback_Solution}",
    "type": "QUESTIONS",
    "readOnlyCondition": true,
    "version": "2.0",
    "questions": [
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt1}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt2}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt3}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt4}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt5}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt6}"
      }
    ],
    "buttons": [
      {
        "navigation": {
          "title": "!{Navigation_submit}",
          "history": false,
          "message": "!{InputSubmited}",
          "idToShow": "navButton"
        }
      },
      {
        "navigation": {
          "title": "!{Navigation_submitModalClose}",
          "history": false
        }
      }
    ],
    "scope": []
  }]]></Component>







  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>





  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      _isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R1_Scenario2_FB_Header}"/>
  </Voting>





</Action>