<div class="tabsLayoutDebrief">

    <!-- Tabs header -->
    {{=it.components[0]}}
        
    {{=it.components[1]}} <!-- scenario intro -->

    <!-- Tabs panels -->
    <!-- * Case Tab -->
    <!-- <div id="tabPanel0" class="col s12 tab-component">
        {{=it.components[1]}}
        {{=it.components[2]}}
    </div> -->
    <!-- * Results Tab -->
    <div id="tabPanel0" class="col s12 tab-component">
        <div class="row">
            <div class="chart col s12 m12 l12 xl6">
                {{=it.components[2]}} <!-- chart -->
            </div>
            <div class="table col s12 m12 l12 xl6">
                {{=it.components[3]}} <!-- table -->
            </div>
        </div>
        {{=it.components[4]}} <!-- next tab button -->
    </div>
    <!-- * Answers Tab -->
    <div id="tabPanel1" class="col s12 tab-component">
        {{=it.components[5]}} <!-- answers -->
    </div>

    {{for (var idx = 6; idx < it.components.length; idx++) {}}
        {{=it.components[idx]}}
    {{};}}  

</div>
