@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


.wizlet.wizletGauges {
    
    $gaugesHeight: 70px;

    nav.gauges {
        
        .row {
            margin:0;
        }

        .highcharts-background,
        .highcharts-plot-background,
        .highcharts-plot-border,
        .highcharts-legend,
        .highcharts-axis-labels,
        .highcharts-grid,
        .highcharts-axis.highcharts-yaxis .highcharts-axis-line,
        .highcharts-data-labels.highcharts-tracker {
            display: none;
        }

        .highcharts-pane-group,
        .highcharts-pane-group .highcharts-pane  {
            fill: white;
            fill-opacity: 1;
            opacity: 1;
        }
        
        .highcharts-series.highcharts-gauge-series.highcharts-tracker  {
            >path {
                fill: white !important;
            }
            circle {
                display: none;
            }
        }

        z-index: 998;
        background: none;
        box-shadow: none;

        height: $gaugesHeight;

        .statusBar {
            $shadow: color("client-colors", "border");
            
            box-shadow: 0 2px 2px 0 rgba($shadow, 0.14),
                        0 3px 1px -2px rgba($shadow, 0.12),
                        0 1px 5px 0 rgba($shadow, 0.2);

                        
            $border-radius: 3.5rem;

            height: $gaugesHeight + 10px;
            margin-top: 1rem;
            border-radius: $border-radius;
            color: #fff;
            position: relative;
            // z-index: 2;
            
            @include linear-gradient2( 
                "90deg",
                lighten(color("client-colors", "secondary"), 10%), 
                darken(color("client-colors", "secondary"), 10%),
                "500%" );
                

            &:before {
                content: '';
                display: block;
                position: absolute;
                border-radius: $border-radius;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-image: url("../images/statusBar/BG.svg");
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
            }

            .top_gauges {
                // display: inline-block;
                // padding: 0;

                .gauge {
                    position: relative;
                    text-align: center;
                    padding: 0;
                    top: 0.2rem;

                    .score-container {
                        display: inline-flex;

                        div.totalmax {
                            margin-left: 0.5rem;

                            >span {
                                font-size: 80%;
                            }
                        }
                    }

                    span {
                        font-size: 1.1rem;
                        line-height: 1.25rem;
                        color: #fff !important;
                        //font-weight: lighter !important;

                        &.gauge-label {
                            @include truncate-line;
                            margin-top: -10px;
                            display: block;
                            &:after {
                                content: none;
                            }
                            &.total {
                                font-weight: bold;
                            }
                        }
                    }

                    .gauge-value {
                        position: absolute;
                        top: 0;
                        height: 25px;
                        left: 50%;
                        transform: translateX(-50%) translateY(50%);

                        span {
                            font-size: 1.2rem;
                        }

                        &.percentage span:after {
                            content: ' %';
                            display: inline-block;
                        }
                    }

                    .highcharts-container {
                        width: $gaugesHeight + 10px !important;
                        height: $gaugesHeight - 10px !important;
                        margin: auto;
                        margin-top: 0px;

                        >svg {
                            >desc, >defs {
                                display: none;
                            }
                        }

                        .highcharts-series > path {
                            //fill: color("client-colors", "tertiary");
                        }

                        .highcharts-axis-labels.highcharts-yaxis-labels text {
                            display: none;
                        }
                        
                    }

                    //Custom gauges color
                    .gaugeContainer {
                        $gauges: color('yellow','base'), color('yellow','darken-1'), color('yellow','darken-2'), color('yellow','darken-3'), color('yellow','darken-4');
  
                        @for $i from 0 through 7 {
                            &.container#{$i} .highcharts-series > path {
                                //fill: color("client-colors", "gauge#{$i}"); 
                                //fill: color("light-green", "lighten-1"); 
                                //fill: nth($gauges, $i+1);
                                //fill: initial;
                            }
                        }
                    }
                }

                .yourMetrics {
                    padding: 0;

                    .gauge {
                        width: 100%;
                        padding: 0;

                        span {
                            vertical-align: -webkit-baseline-middle;
                            font-size: 1.2rem;
                            
                            &:after {
                                display: none;
                            }
                            &.gauge-label {
                                margin-top: 0;
                            }
                        }
                    }
                }

                .otherMetrics {
                    padding: 0;
                    
                    .textContainer {
                        display: inline-block;

                        ~.gauge-value {
                            margin-top: 5px;
                            span {
                                white-space: nowrap;
                                font-size: 1.5rem;
                            }
                        }

                        ~.gauge-label {
                            margin-top: 6px;
                        }
                    }
                }


                .more-info {
                    position: absolute;
                    right: 1rem;
                    top: 1rem;
                    
                    cursor: pointer;
                    &:hover {
                        transform: scale(1.2);
                    }

                    i {
                        font-size: 2rem;                        
                        &.bounce {
                            @include animation-name(bounceInfinite);
                            @include animation-duration(5s);
                            @include animation-delay(2s);
                            @include animation-iteration-count(1);
                            @include animation-timing-function(ease);
                            @include animation-fill-mode(forwards);
                        }
                    }
                }
            }
        }
        

        &.on-the-side {
            position: absolute;
            width: 100px;

            .statusBar {    
                border-top-right-radius: 0;
                border-top-left-radius: 0;
                margin-top: 0px;
                @include linear-gradient(color("client-colors", "primary"), darken(color("client-colors", "primary"),30%) );
                &:before {
                    content: none;
                }
            }

            &.left {
                left: 0;
                .statusBar {    
                    border-bottom-left-radius: 0;
                    padding-right: 20px;
                }
            }
            &.right {
                right: 0;
                .statusBar {    
                    border-bottom-right-radius: 0;
                    padding-left: 20px;
                }
            }

        }


        &.resize {
            
            @media #{$medium-and-down} {
                height: #{$gaugesHeight / 2};
            }

            .statusBar {
                @media #{$medium-and-down} {
                    height: calc(#{$gaugesHeight / 2} + 10px);
                }

                .top_gauges .more-info {
                    @media #{$medium-and-down} {
                        top: 0rem;
                    }
                }
            }

        }
        
    }

    .sticky.row {

        height: $gaugesHeight + 10px;

        nav.gauges {
            position: fixed;
            width: 90%;
            left: 5%;

            .statusBar {
                margin-top: 0;
                &, &:before {
                    border-top-right-radius: 0;
                    border-top-left-radius: 0;
                }
            }
        }
    }
}
