
<div class="container"> 
    <div>
        
        {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}
        {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        <div class="card">
            
            <div class="card-content {{?it.dataTablesHideControls}}hideControls{{?}}">
                
                {{?it.questionVotes.participationVotes.length > 0}}
                <table class="striped highlight centered {{?it.isDataTables}}dataTables{{?}} {{?it.class}}{{=it.class}}{{?}}">
                    <thead>
                        <tr>
                            {{?it.position}}<th>{{=it.position}}</th>{{?}}
                            {{?it.rank}}<th>{{=it.rank.title}}</th>{{?}}
                            {{?it.avatar}}<th>{{=it.avatar}}</th>{{?}}
                            <th>{{?!it.subheaders}}{{=it.userHeader}}{{?}}</th>
                            {{~it.columnHeaders :columnHeader:ch}}
                                {{?it.headerMultiLines}}
                                    <th class="multiline hide-on-small-and-down"><div>{{=columnHeader.title}}</div></th>
                                    <th class="singleline hide-on-med-and-up">{{=columnHeader.title}}</th>
                                {{??}}
                                    <th {{?it.headerEllipsis}}class="singleline"{{?}}>{{=columnHeader.title}}</th>
                                {{?}}
                            {{~}}
                        </tr>
                        {{?it.subheaders}}
                        <tr>
                            {{~it.subheaders :subheader:sh}}
                            <th>{{?subheader}}({{=subheader}}){{?}}</th>
                            {{~}}
                        {{?}}
                        
                {{??}}
                <table class="centered">
                    <thead>
                        <tr class="empty">
                            <th><i class="material-icons">visibility_off</i></th>
                {{?}}
                        </tr>
                    </thead>
            
                    <tbody>
                        {{~it.questionVotes.participationVotes :participation:qidx}}

                            
                            <tr {{?Wizer.ParticipantName===participation.participationName}}class="me"{{?}}>

                                {{?it.position}}<td>{{=(qidx+1)}}</td>{{?}}
                                
                                {{?participation.rank}}<td>{{=participation.rank}}</td>{{?}}

                                {{?it.avatar}}
                                    <td class="avatar">
                                        {{?participation.customAvatar}}
                                        <img class="circle" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}/images/avatar/{{=participation.customAvatar}}">
                                        {{?}}
                                    </td>
                                {{?}}

                                {{?Wizer.ParticipantName === participation.participationName}}
                                    <td>
                                        {{?it.me}}
                                        <!-- <i class="material-icons prefix">mood</i> -->
                                        {{=it.me}} ({{=participation.customName}})
                                        {{??}}
                                        {{=participation.customName}}
                                        {{?}}
                                    </td>
                                {{??}}
                                    <td>{{=participation.customName}}</td>
                                {{?}}
                                
                                {{~participation.votes :vote:vidx}}
                                    {{?it.answervalidation}}
                                        <td {{?vote!='-'}}class="{{?vote==it.subheaders[vidx+2]}}right-answer{{??}}wrong-answer{{?}}"{{?}}>{{=vote}}</td>
                                    {{??}}
                                        <td>{{=vote}}</td>
                                    {{?}}
                                {{~}}
                            </tr>
                        {{~}}
                    </tbody>

                    {{?it.totalRecords}}
                    <tfoot>
                        <h6 class="flow-text">{{=it.totalRecords}} {{=it.questionVotes.participationVotes.length}}</h6>
                    </tfoot>
                    {{?}}

                  </table>

            </div>

        </div>  
        
        
    </div>
</div>
    