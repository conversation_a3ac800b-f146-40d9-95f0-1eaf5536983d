﻿
<div class="container table{{?it.animate}} animated{{=it.animate}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"> 
    <div>
        
        {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}
        {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        <div class="card">
            
            <div class="card-content">
                {{?it.table.title}}<span class="card-title">{{=it.table.title}}</span>{{?}}
                {{?it.table.body}}<p class="flow-text">{{=it.table.body}}<br><br></p>{{?}}
   
                <!-- <table class="striped highlight {{?it.class}}{{=it.class}}{{?}}">
                    <thead {{?it.table.headerColor}}class="headerColor {{=it.table.headerColor}}"{{?}}>
                        <tr>
                            <th>{{=it.table.headers.user}}</th>
                            <th>{{=it.table.headers.vote}}</th>
                            <th>{{=it.table.headers.value}}</th>
                        </tr>
                    </thead>
            
                    <tbody contenteditable="true">
                        {{~it.table.rows :row:r}}
                        <tr>                            
                            <td>{{=row.user}}</td>
                            <td>{{=row.vote}}</td>
                            <td>
                                <input type="text" data-bind="{{=row.vote}}" maxlength="3" required class="no-validate"
                                        value="{{=row.value}}">
                                </input>
                            </td>
                        </tr>
                        {{~}}
                    </tbody>
                    {{?it.table.note}}<caption>{{=it.table.note}}</caption>{{?}}
                </table> -->


                <form class="col s12" novalidate autocomplete="off">
                            
                    {{~it.table.rows :row:r}}
                    <div class="row">
                        <div class="input-field inline col s10">
                            <i class="material-icons prefix">security</i>
                            
                            <input name="input{{=r}}" type="text" data-bind="{{=row.vote}}"  maxlength="3"
                                required class="no-validate" value="{{=row.value}}"/>
                            
                            <label for="input{{=r}}" class="active">{{=row.user}}</label>
                            
                        </div>
                        
                        <div class="submit col s2">
                            <a id="submitBtn" class="btn client-colors red" 
                                data-user="{{=row.user}}" data-vote="{{=row.vote}}">
                                <i class="medium material-icons right">send</i>{{=it.submitBtn.label}}
                            </a>
                        </div>
                    </div>
                    {{~}}

                </form>


                {{?it.table.body2}}<p class="flow-text">{{=it.table.body2}}<br><br></p>{{?}}

            </div>

        </div>  
        
        
    </div>

    <div class="embeddingWrapper" id="embeddingAssess" data-embed></div>
</div>
    