@charset "UTF-8";

/****      Animate CSS      ****/
@import url("animate/animate.min.css");


/********************************/
/****   Materialize Styles   ****/
/********************************/
@import "../materialize-src/sass/materialize";
/********************************/
/*** END: Materialize Styles  ***/
/********************************/


/****      Animate CSS      ****/
@import url("materialDesign/materialdesignicons.min.css");


@import "mixins";
@import "fonts";


i.mdi {
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    &.bottom {
        vertical-align: bottom;
    }
}




//HIDING PULSE COMPONENTS
#votingSlideDiv #sendContainer {
    display: none !important;
}

//Avoid animations overflows
.mainArea > .wizlet:not(.wizletFormInputs),
.mainArea > .tab-component > .wizlet {
  overflow: hidden;
}

//iBider embed with no width restrictions
// .mainArea .wizlet .container {
//   width: 95% !important ;
// }
// .mainArea .wizlet.wizletTabs > .container {
//   width: 92% !important  ;
// }


//NICE SCROLL-BAR
::-webkit-scrollbar { width: 6px; }
  
  ::-webkit-scrollbar-track {
    @include box-shadow (inset 0 0 6px rgba(0,0,0,0.3)); 
    @include border-radius (10px);
  }
  
  ::-webkit-scrollbar-thumb {
      @include box-shadow (inset 0 0 6px rgba(0,0,0,0.5));
      @include border-radius (10px);

  }


//ANIMATE DELAYS
.animate__animated {
  @for $i from 6 through 10 {
    &.animate__delay-#{$i}s{
      @include animation-delay(#{$i}s)
    }
  }
  @for $i from 3 through 25 {
    &.animate__delay-#{$i * 5}s{
      @include animation-delay(#{$i * 5}s)
    }
  }
}



//Global styles
body {
  
    background-color: color("client-colors", "background");
    // &:not([class*="LandingPage"]):not([class$="_FAC"]) {
    //   background-image: url("../images/backgrounds/background.webp");
    // }
    // background-image: url("../images/backgrounds/background.jpg");
    // background-attachment: fixed; 
    // background-repeat: no-repeat; 
    // background-position-x: center; 
    // background-position-y: bottom; 
    // background-size: cover;
    // .mainAreaContainer {
    //   background-color: rgba(255,255,255,0.5);
    // }

    // &[class^="bodyLandingPage"],
    // &[class^="bodyR1_LandingPage"],
    // &[class^="bodyR2_LandingPage"] {
    //   background-color: color("client-colors", "white");
    //   background-image: none;
    // }

    // &[class^="bodySIM_R1_"] {
    //   background-image: url("../images/backgrounds/lines_aviation.png");
    // }

    
    &[class^="bodySIM_"], &[class="bodyTeamName"] {
      .mainAreaContainer.animated-frame {

        $width: 4px;
        $length: 25px;
        $spacing: 40px;
        $color: color("client-colors", "border");

        background-image: 
            repeating-linear-gradient(0deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing), 
            repeating-linear-gradient(90deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing), 
            repeating-linear-gradient(180deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing), 
            repeating-linear-gradient(270deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing);
        background-size: $width calc(100% + #{$spacing}), calc(100% + #{$spacing}) $width, $width calc(100% + #{$spacing}) , calc(100% + #{$spacing}) $width;
        background-position: 0 0, 0 0, 100% 0, 0 100%;
        background-repeat: no-repeat;
        animation: borderAnimation 2s infinite linear;
        @keyframes borderAnimation {
            from { background-position: 0 0, -#{$spacing} 0, 100% -#{$spacing}, 0 100%; }
            to { background-position: 0 -#{$spacing}, 0 0, 100% 0, -#{$spacing} 100%; }
        }      

      }
    }
    

    // ************ MOUSE POINTER ANIMATION ***************** //
    // cursor: url("../images/cursor.png") 16 16, auto;  

    /*
    #cursor-follower {
      z-index: 9999;
      position: absolute;
      top: 50%;
      left: 50%;
      >div {
        position: absolute;
        border-radius: 50%;
        // @include animation-name(mousepulse);
        // @include animation-iteration-count(infinite);
      }
      #circle1 {
        background-color: color("client-colors", "secondary");
        // @include animation-duration(2s);
        height: 1.2em;
        width: 1.2em;
        margin-top: -0.6em;
        margin-left: -0.6em;
      }
      #circle2 {
        background-color: rgba(color("client-colors", "white"),0.8);
        // @include animation-duration(4s);
        height: 1em;
        width: 1em;
        margin-top: -0.5em;
        margin-left: -0.5em;
      }
      
      
      @include keyframes (mousepulse){
        0% {
          opacity: 0.2;
          height: 1em;
          width: 1em;
          margin-top: -0.5em;
          margin-left: -0.5em;
        }  
        50% {
          opacity: 0.9;
          height: 3em;
          width: 3em;
          margin-top: -1.5em;
          margin-left: -1.5em;
        }  
        100% {
          opacity: 0.2;
          height: 1em;
          width: 1em;
          margin-top: -0.5em;
          margin-left: -0.5em;
        }
      }
    }
    */





    //background: linear-gradient(to top, lighten($primary-color, 1%) 0%, lighten($primary-color, 15%) 100%);
    color: color("client-colors", "font");
    h1,h2,h3,h4,h5,h6 {
      color: color("client-colors", "font");
    }
    
    // &:not([class$="_FAC"]) {
    //   h1,h2,h3,h4,h5,h6 {
    //     color: color("client-colors", "font2");
    //   }
    //   h1,h2,h3 {
    //     filter: drop-shadow(.05em .05em 0em color("client-colors", "font"));
    //   }
    // }

    #modal-logout, #modal-reset, #modal-export {
      > .modal-content {
        &>h4, &>span {
          color: color("client-colors", "font");
        }
      }
    }


    // .modal, .card {
    //   h1,h2,h3,h4,h5,h6 {
    //     color: color("client-colors", "font");
    //   }
    // }
    @include font-smoothing ();
    font-family: clientRegular, sans-serif, Arial;
    
    i, em {
      font-family: clientLight, clientRegular, sans-serif, Arial;
    }
    
    font-size: 1.15rem;
    overflow-x: hidden;
    overflow-y: auto !important;

    //When no screen loaded
    // &:not([class]) {
    //     background-image: url("../images/landing.png");
    //     background-repeat: no-repeat;
    //     background-position-x: center;
    //     background-position-y: center;
    //     background-attachment: fixed;
    //     // background-size: auto 90%;
    //     background-size: contain;
    // }

    > #wizer > .page > #errorMessage {
      display: none !important;
    }

    > #progressbar {
      background-color:  color("client-colors", "primary");
      border-radius: 5px;
      border:  1px solid rgba(255,255,255,.8);
      height: 12px;

      > #progressbarfirst {
        margin-top: -1px;
        border-radius: 5px;
        height: 13px;
        background-color:  color("client-colors", "secondary");
      }
      > #progressbarsecond {
        color:  color("client-colors", "font");
      }
    }
}

.cl__loader {
  font-size: 0.25em;
    border: {
        color: $primary-color;
        left-color: $primary-color-dark;
    }
}

.wizletVanilla { font-size:inherit !important; }


[hidden] { display: none !important; }
[invisible] { visibility: hidden !important; }


a[href] {
    color: inherit;
    text-decoration: none;
}

$check_color: color("client-colors", "primary");

  ul, ol {
    text-align: left;
  }

  ul.check-list {
    list-style: none;
    padding: 0 0 0 5px;
    li {
        display: flex;    
        &:before {
            content: '✓';
            color: $check_color;
            padding-right: 5px;
        }
    }
  }
  
  ul.square-list {
    padding: 0 0 0 25px;        
    >li {
        //list-style-type: square;      
        list-style-type: none;      
        &:before {
          // content: "■";
          content: "▪";
          display: inline-block; 
          width: 1em;
          margin-left: -1em;
          color: color("client-colors", "font3");
        }  
    }
    &.strong li {
      font-weight: bold;
    }
    ul.square-list {
        padding: 0 0 0 30px;        
    }
  }
  ul.circle-list {
    padding: 0 0 0 25px;        
    >li {
        // list-style-type: circle;    
        // list-style-type: none;   
        &:before {
          content: "•";
          font-weight: bold;
          display: inline-block; 
          width: 1em;
          margin-left: -1em;
          color: color("client-colors", "font3");
        } 
        &.no-margin:before {
          margin-left: 0;
        }
        &.margin:before {
          margin-left: 1em;
        }
    }
    &.strong li {
      font-weight: bold;
    }
    
    ul.circle-list {
        padding: 0 0 0 30px;        
    }
  }

  ul.browser-default,
  ol.browser-default {
    // text-align: justify;
    padding: 0 0 0 20px;
    &[type="i"] {
      padding-left: 40px;
    }       
    >li {
        &::marker {
          font-weight: bold;
          color: color("client-colors", "font3");
        }
    }
  }


button, html [type="button"], [type="reset"], [type="submit"] {
    -webkit-appearance: none;
}
  

img.embed {
  clear: both;
  width: 40%;
  @media #{$large-and-up} {
      width: 50%;
  }
  &.tiny {
    width: 15%;
    @media #{$large-and-up} {
        width: 20%;
    }
  }
  &.small {
    width: 20%;
    @media #{$large-and-up} {
        width: 30%;
    }
  }
  @media #{$small-and-down} {
      width: 100%;
  }
  &.left {
      float: left;
      margin-right: 12px;
  }
  &.right {
      float: right;
      margin-left: 12px;
  }
  &.full {
    width: 100% !important;
    margin-bottom: 12px;
  }
}

// ***************************************** //
// ** Corrections over materialize styles ** //
// ***************************************** //

.btn {
  background-image: none;
  border: 2px solid;
  border-color: initial;
  border-radius: 5px;
  background-color: color("client-colors", "button") !important;
  color: color("client-colors", "font2") !important;
  &:hover {
    color: color("client-colors", "button") !important;
    background-color: color("client-colors", "font2") !important;
    i {
      color: color("client-colors", "button") !important;
    }
  }

  &[disabled].pulse::before {
    animation: none;
  }
}

@media #{$large-and-up} {
    .container {
      width: 80%;
    }
  }
  
@media #{$extra-large-and-up} {
    .container {
      width: 75%;
    }
  }
  
@media #{$mega-large-and-up} {
    .container {
      width: 70%;
    }
  }

.container.reduced {
  @media #{$large-and-up} { width: 90%; }
  @media #{$extra-large-and-up} { width: 85%; }
  @media #{$mega-large-and-up} { width: 80%; }
}

.container.margin {
  &.level1 { margin-bottom: 50px; }
  &.level2 { margin-bottom: 80px; }
  &.level3 { margin-bottom: 100px; }
}

@include modal-open;

.modal .modal-footer {
    width: auto;
}

.valign-container {
    display: flex;
    position: absolute;
    top: 0;
    height: 100%;
    width: 90%;
    margin: 0 5%;
    padding: 0;

    .valign-wrapper {
        width: 100%;
    }
}


.card {

    &.autoHeight {
        height: auto !important;
    }

    &.tiny {
        min-height: 25vh;
        padding: 5px 0;
        margin: 5px 0;
    }
    &.small {
        min-height: 40vh;
        padding: 5px 0;
        margin: 5px 0;
    }
    &.medium {
        min-height: 55vh;
        padding: 10px 0;
        margin: 10px 0;
    }
    &.large {
        min-height: 70vh;
        padding: 10px 0;
        margin: 10px 0;
    }

    // .card-content {
    //     text-align: justify;
    // }
}


//Responsive navBar logo
header nav #logo-container img {
    padding-top: 2px;
    @media #{$medium-and-up} {
        padding-top: 5px;
    }
}

//Makes all the columns in this row have the same height
.row.same-heigh-columns {
  display: flex;
  flex-wrap: wrap;
}


//centered toas message
#toast-container {
    top: auto !important;
    right: auto !important;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
  }



$background : color("client-colors", "primary");

.progress {    
    margin-top: 20px;
    height: 10px;
    background-color: lighten($background,10%);
    .determinate, .indeterminate {
        // background-color: darken($background,10%);
        background-color : color("client-colors", "secondary");
    }
}

  
  @include keyframes (shakeInfinite){
    0% {
      transform: translateZ(0);
    }  
    1%,3%,5%,7%,9% {
      transform: translate3d(-10px,0,0);
    }  
    2%,4%,6%,8% {
      transform: translate3d(10px,0,0);
    }
    //Time to stay quiet until the next iteration
    10%,100% {
      transform: translateZ(0);
    }
  }
  
  @include keyframes (bounceInfinite){
    0% {
      transform: translateZ(0);
    }  
    1%,6%,10%,14%,18% {
      transform: translate3d(0,-5px,0);
    }  
    4%,8%,12%,16% {
      transform: translate3d(0,5px,0);
    }
    //Time to stay quiet until the next iteration
    20%,100% {
      transform: translateZ(0);
    }
  }


/* Jeronimo Martins Updates */
#KPIgauges_LTUs_R1,
#KPIgauges_LTUs_R2,
#KPIgauges_LTUs_R3{
  overflow: hidden;
}
.SIM_R1_Roles,
.SIM_R2_Roles,
.SIM_R3_Roles,
#modal-roles{
  .wizletTabs{
    .tabs{
      .tab{
        a{
          cursor: default;
        }
      }
    }
  }
}
.SIM_R1_Scenario2{
  .pc{
    &-clearbutton{
      display: none !important;
    }
    &-submitbutton{
      margin-top: 0;
    }
    &-buttontext{
      display: inline-block;
      margin-top: -1.35rem;
    }
    &-buttons{
      &:has(.pc-savedmessage){
        display: none !important;
      }
    }
  }
}
.SIM_R1_Scenario2_FB{
  .pc{
    &-clearbutton{
      display: none !important;
    }
    &-submitbutton{
      margin-top: 0;
    }
    &-buttontext{
      display: inline-block;
      margin-top: -1.35rem;
    }
    &-buttons{
      display: none !important;
    }
    &-instructions{
      background-color: transparent;
      &__text{
        font-size: 100%;
        font-weight: bold;
        color: color("client-colors", "font");
      }
    }
  }
}