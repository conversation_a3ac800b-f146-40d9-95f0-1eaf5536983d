@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

$foreground : color("client-colors", "secondary");
$background : color("client-colors", "dark-grey");

nav.breadcrumbs {    
    
    padding-bottom: 15px;
    margin-top: -5px;
   
    .row {
        margin:0;
    }

    &.clean {
        background: none;
        box-shadow: none;
            
        .breadcrumb {
            color: rgba(0,0,0,1);

            &:before {
                color: rgba(0,0,0,0.7)
            }
        }
    }
    
    &.dot {
        background: none;
        box-shadow: none;

        .bar, .progress {
            margin-bottom: 20px;
            padding: 0;
        }
        .progress {
            background-color: $background;
            .determinate {
                background-color: $foreground;                
                &::after {
                    content: '';
                    opacity: 0;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    background: #fff;                 
                    @include animation-name(shine);
                    @include animation-duration(2s);
                    @include animation-timing-function(ease-out);
                    @include animation-iteration-count(2);
                    @include keyframes (shine) {
                        0% {opacity: .1; width: 0;}
                        50% {opacity: .6;}
                        100% {opacity: .1; width: 100%;}
                    }
                }
            }
        }
        
        a {
            position: relative;
            color: color("client-colors", "grey");
            display: inline-block;
            word-break: break-word;
            max-width: 150px;
            line-height: 1.5rem;
            padding: 1rem 0.5rem;
                            
            &:before,
            &:first-child:before {
                content: '';
                position: absolute;
                background-color: $background;
                display: block;
                width: 10px;
                height: 10px;
                margin: 0 0 0 -5px;
                border-radius: 50%;
                bottom: 0;
                left: 50%;
                z-index: 1;
                transition: width ease-in 0.1s, height ease-in 0.1s, margin ease-in 0.1s, bottom ease-in 0.1s;
            }
            
            &:after {
                content: '';
                position: absolute;
                background-color: $background;
                display: block;
                width: calc(100% + 5px);
                height: 2px;
                bottom: 4px;
                left: 50%;
            }

            &.active {
                font-weight: bolder;
                
                &:before {
                    width: 14px;
                    height: 14px;
                    margin: 0 0 0 -7px;
                    bottom: -2px;
                }

                &.last {
                    color: $foreground;
                    &:before {
                        background-color: $foreground;
                    }
                }
            }
            
            &:nth-last-child(2) {
                &:after {
                    width: calc(100% + 5px);
                }
            }
            
            &:last-child {
            
                &:after {
                    display: none;
                }
            }
        }
    }

             

}
