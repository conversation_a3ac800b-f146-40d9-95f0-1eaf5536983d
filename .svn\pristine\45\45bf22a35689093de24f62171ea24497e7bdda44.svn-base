<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro_SIM" >
  <Include name="Header_Intro"></Include>
  

  <!-- Need to use Card.js to apply animation animateLater to the image which already has an animated_animate one -->
  <!-- <Component type="Card" customJS="true"> -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{SIM_R2_Intro_Header} ",
      valign: false,
      animate: "zoomIn",
      transparentBox: false,
      animatedFrame: true,
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{Asesora_Img4}",  alt: "!{SIM_R2_Intro_Header}" ,
          position: "right tiny",
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        _img: { 
          materialboxed: false, _borders: "top right left bottom", frame: "", nopadding: false,
          src: "!{SIM_R2_Intro_Image}",  alt: "!{SIM_R2_Intro_Header}",
          isHiddenWhenSmall: true, 
          src_vert: "!{}",
          animate: "fadeInUp animate__delay-1s"
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{SIM_R2_Intro_Title}",
        body: "!{SIM_R2_Intro_Text}"
      }
    }]]>  
  </Component>



  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      _isHidden: true, _showDelay: "3000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>

  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R2_Intro}"/> 
  </Voting>


  <Include name="Init_Score"></Include> 
  

</Action>