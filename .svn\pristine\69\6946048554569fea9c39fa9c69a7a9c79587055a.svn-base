@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";


.wizlet.wizletHCLineChartModel {

    .card {
        .card-content {
            padding: 6px 12px;
            .row {
                margin: 0;
            }
        }
    }

    .highcharts-container {
        
        &>svg {
            width: 100% !important;
        }

        .highcharts-background {
            fill: transparent;
        }

        .highcharts-title {
            font-size: 1.25rem;
        }
        .highcharts-subtitle {
            font-size: 1rem;
        }

        .highcharts-label,
        .highcharts-axis-labels {
            color: initial;
            fill: initial;
            >text, >span {
                font-size: 1rem;
                &:last-child:not([transform]) {
                    transform: translateX(-4px);
                    transform: translateY(2px);
                }
            }
        }
        .highcharts-data-label {
            span {
                color: color("client-colors", "font");
            }
            text {
                fill: color("client-colors", "font");
                text-shadow: 1px 1px color("client-colors", "font2");
            }
        }

        .highcharts-axis {
            .highcharts-axis-title {
                tspan {
                    font-weight: bold;
                }
            }
        }

        @for $i from 1 through 12 {
            .highcharts-color-#{$i - 1} {
                fill: color("client-colors", "chart#{$i}");
                stroke: color("client-colors", "chart#{$i}");
            }
        } 

        .highcharts-tooltip {
            table {
                tr, td {
                    padding: 0;
                }
            }
        }
        
    }

    .bach-content-lineChart--container {
        
        &.color {
            @for $i from 1 through 4 {
                &.categoria#{$i} {
                    .highcharts-color-0 {
                        .highcharts-point, 
                        &.highcharts-legend-item .highcharts-graph {
                            fill: color("client-colors", "categoria#{$i}");
                            stroke: color("client-colors", "categoria#{$i}");
                        }
                        &.highcharts-spline-series .highcharts-graph {
                            stroke: color("client-colors", "categoria#{$i}");
                        }
                    }                
                }
            }  
        }
        &.color1 {
            @for $i from 1 through 3 {
                &.categoria#{$i} {
                    .highcharts-color-1 {
                        .highcharts-point, 
                        &.highcharts-legend-item .highcharts-graph {
                            fill: color("client-colors", "categoria#{$i+1}");
                            stroke: color("client-colors", "categoria#{$i+1}");
                        }   
                        &.highcharts-spline-series .highcharts-graph {
                            stroke: color("client-colors", "categoria#{$i+1}");
                        }
                    }                
                }
            }  
        }
    }


}