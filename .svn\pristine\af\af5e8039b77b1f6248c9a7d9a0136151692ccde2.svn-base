<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayout_SIM_R2" layout="../../../layout/tabsLayout2Case">
  <Include name="Header_SIM_R2"></Include>
  <Include name="KPIgauges_R2"></Include>


  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "bounceInLeft",
      progress: "6",
      steps: [ "!{SIM_BreadCrumbs_0}",
               "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}",
               "!{SIM_BreadCrumbs_5}" ]
    }]]>
  </Component>


  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{} ",
      tabs: [
          "!{SIM_R2_Scenario6_Header}",
          "!{SIM_Scenario_Tab2}"
      ],
      scope: null
  }]]></Component>


  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R2_Scenario6_Img}",  alt: "!{SIM_R2_Scenario6_Title}" ,
          position: "right large", 
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R2_Scenario6_Img}",  alt: "!{SIM_R2_Scenario6_Title}" ,
          isHiddenWhenSmall: true, 
          src_vert: "!{}"
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{SIM_R2_Scenario6_Title}",
        body: "!{SIM_R2_Scenario6_Text}"
      }      
    }]]>
  </Component>


  <!-- Next Tab Button  -->
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "zoomIn",
      id: "btn_action1", isHidden: false, 
      title: "!{Header_LinkDecision}",
      icon: "redo",
      _onclick: "$(this).hide(); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanel1&quot;')[0].click()",
      onclick: "$(this).removeClass('pulse'); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanel1&quot;')[0].click()",
      pulse: false,
      color: "",
      
      _isFollower: "Follower",
      scope: [""]
    }]]>
  </Component>


  <Component type="RadioButtons" customJS="true">
    <![CDATA[{
      templateInEvent: "html/radioButtonsConditional.dot",
      css: "styles/radioButtons.css",
      id: "",
      isHidden: false,
      animate: "fadeInRight",
      headerLabel: "!{}",
      header: "!{}",
      instructions: "!{RadioInstructions}",
      titleLabel: "!{}",
      title: "!{SIM_R2_Scenario6_Question}",
      subtitle: "",
      class: "with-gap",
      groupName: "radioGroup",
      preloadSaved: true,
      bind: "Q_SIM_R2_Scenario6",
      moreInfo: "!{Choice_MoreInfo}",
      lessInfo: "!{Choice_LessInfo}",
      descActive: true,
      isCheck: false,
      
      noBlockAnswer: false,
      _optionsFromVote: "Q_Title_Input",
      _moreInfoFromVote: "Q_Desc_Input",
      _includeMyVote: false,

      isInline: false,
      optionWidth: "s6 big",

      grid: {  image:"s2 m3 l3", option: "s8 m9 l9" },
      options: [
        {
          value: "1",
          label: "!{Choice_Opt1}",
          _image: "!{SIM_R2_Scenario6_Img1}",
          _icon: "thumb_down", _iconDesign:"mdi-comment-remove",
          title: "!{SIM_R2_Scenario6_Opt1}",
          _titleClass:"client-colors-text text-red",
          moreInfo: "!{SIM_R2_Scenario6_Des1}",
          isCorrect: false
        },
        {
          value: "2",
          label: "!{Choice_Opt2}",
          _image: "!{SIM_R2_Scenario6_Img2}",
          title: "!{SIM_R2_Scenario6_Opt2}",
          moreInfo: "!{SIM_R2_Scenario6_Des2}",
          isCorrect: false,
          isDisabled: true,
          condition: "Q_SIM_Initiatives_7",
          condition_Val: "1",
          classIfTrue: "available",
          classIfFalse: "disabled"
        },
        {
          value: "3",
          label: "!{Choice_Opt3}",
          _image: "!{SIM_R2_Scenario6_Img3}",
          title: "!{SIM_R2_Scenario6_Opt3}",
          moreInfo: "!{SIM_R2_Scenario6_Des3}",
          isCorrect: false,
          isDisabled: true,
          condition: "Q_SIM_Initiatives_7",
          condition_Val: "1",
          classIfTrue: "available",
          classIfFalse: "disabled"
        }
      ],
      
      autocheck: false,
      checkBtn: {
        label: "!{Choice_btnCheck}",
        animate: "slideInUp",
        _toast: { 
          ifCorrect: "!{Choice_toastCorrect}",
          ifIncorrect: "!{Choice_toastIncorrect}",
          points: "!{}"
        },
        toastMsg: "!{InputSubmited}",
        idToClick: "navButton",
        _idToShow: "navButton",
        idToShowIfCorrect: "",
        idToShowIfIncorrect: "",
        scrollToDown: false
      },
      
      _score : {
        points: { ifCorrect: "!{Quiz_pointsIfCorrect}", ifIncorrect: "!{Quiz_pointsIfIncorrect}"},
        question: "Score_SIM_R2_Scenario6",
        _questions: [ ],
        _total: "Score_SIM_R2_Scenario6_Total"
      },
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower","Q_SIM_Initiatives_7"]     
    }]]>
  </Component>




  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>






  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          _gdActionEmbed: "SIM_R2_Scenario6_AGG",
          _gdActionTrack: "GD",
          _gdActionSection: "",
          _targetSection: "",
          label: "!{Navigation_feedback}",
          tooltip: "!{Navigation_feedback_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R2_Scenario6}"/>
  </Voting>




</Action>