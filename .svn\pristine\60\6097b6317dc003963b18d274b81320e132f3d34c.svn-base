<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro2">
  <Include name="Header_Intro"></Include>
  

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "29",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row1A}", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row2A}", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row3A}", "!{R2_Logins_Table_Row3B}" ],
          [ "!{R2_Logins_Table_Row4A}", "!{R2_Logins_Table_Row4B}" ],
          [ "!{R2_Logins_Table_Row5A}", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "30",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s1}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s1}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s1}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s1}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "60",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s2}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s2}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s2}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s2}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "90",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s3}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s3}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s3}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s3}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "120",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s4}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s4}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s4}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s4}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "150",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s5}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s5}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s5}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s5}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "180",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s6}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s6}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s6}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s6}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "210",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s7}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s7}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s7}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s7}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "240",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s8}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s8}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s8}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s8}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "270",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s9}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s9}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s9}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s9}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "Team",
        condition_Diff: "300",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s10}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s10}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s10}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s10}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["Team"]   
    }]]>
  </Component>
  
  
  <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Show_charts",
        condition_Val: "0",
      orientation: "vertical",
      header: "!{R2_Logins_Table_Header} ",
      valign: false,
      animate: "zoomIn",
      transparentBox: false,
      animatedFrame: false,
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{R2_Logins_Table_SIMimg}",  alt: "!{R2_Logins_Table_SIM}" ,
          position: "left large",
          src_vert: "!{}",
          animate: "fadeInLeft animate__delay-1s", _animateLater: "bounce"
        },
        img: { 
          materialboxed: false, _borders: "top right left bottom", frame: "", nopadding: false,
          src: "!{R2_Logins_Table_SIMimg}",  alt: "!{R2_Logins_Table_SIM}",
          isHiddenWhenSmall: true, 
          src_vert: "!{}",
          animate: "fadeInUp animate__delay-1s"
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_SIM}"
      },
      scope: ["Show_charts"]
    }]]>  
  </Component>
  

</Action>