<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro2">
  <Include name="Header_Intro"></Include>
  

  <!-- Need to use Card.js to apply animation animateLater to the image which already has an animated_animate one -->
  <!-- <Component type="Card" customJS="true"> -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{R1_Introduction_Header} ",
      valign: false,
      animate: "zoomIn",
      transparentBox: false,
      _animatedFrame: true,
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{R1_Introduction_Image}",  alt: "!{R1_Introduction_Header}" ,
          position: "right medium",
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        _img: { 
          materialboxed: false, _borders: "top right left bottom", frame: "", nopadding: false,
          src: "!{R1_Introduction_Image}",  alt: "!{R1_Introduction_Header}",
          isHiddenWhenSmall: true, 
          src_vert: "!{}",
          animate: "fadeInUp animate__delay-1s"
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{R1_Introduction_Title}",
        body: "!{R1_Introduction_Text}"
      }
    }]]>  
  </Component>
  

</Action>