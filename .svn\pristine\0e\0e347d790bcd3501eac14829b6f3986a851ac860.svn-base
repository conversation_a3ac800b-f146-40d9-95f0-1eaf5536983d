﻿{/* <reference path="../../Shared/js/jquery.js" />
<reference path="../../../js/votesite.js" /> */}

/*define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'jquery.ui.touch-punch', 'jquery.tinysort', 'jquery-ui', 'logger'],
    function ($, Q, WizerApi, WizletBase, doT, ui, tinysort, ui2, log) {*/

define(['jquery', 'Q','WizerModel', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerModel, WizerApi, WizletBase, doT) {
        var Wizer = window.Wizer = window.Wizer || {};
        Wizer.Wizlet = Wizer.Wizlet || {};

        var Prioritize = function () {
            this.type = 'Prioritize';
            this.level = 1;
            this.isFollower = false;
            this.leaderVotes = {};
        };

        Prioritize.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
            this.wizletInfo = wizletInfo;
            this.wizletContext = wizletContext;
            this.wizerApi = wizerApi;
            this.wizerModel = WizerModel.getInstance() || new WizerModel({ wizerApi: this.wizerApi });
            this.templateDefer = Q.defer();
            var self = this;
            self.componentVersion = self.wizletInfo.version || 1;
            var requirements = [];
            requirements.push(WizletBase.loadTemplate(wizletInfo, 'prioritize.dot'));

            if (wizletInfo.css) {
                requirements.push(WizletBase.loadCss(wizletInfo));
            }
            require(requirements, function (doTTemplate, css) {
                self.templateDefer.resolve(doTTemplate);
            });
            self.questions = $.extend(true, [], self.wizletInfo.questions);
            return WizletBase.loadHandler({ wizlet: this, render: this.render });
        };

        //Wizer.Wizlet.Prioritize = function () { };
        Prioritize.prototype.init = function () {
            this.containerDom = $("[data-prioritizeComponentTableDiv]");
            this.makeSortable();
            this.addEventListeners();
            WizletBase.bindPulseActionSubmission(this.wizletContext, this.type);
        };

        Prioritize.prototype.addEventListeners = function () {
            var self = this;
            self.wizletContext.find("[data-submitButton]").bind("click", function (e) {
                if (self.options.wizletInfo.toastMsg)
                    M.toast({
                        html: self.options.wizletInfo.toastMsg,
                        classes: 'rounded',
                        displayLength: self.options.wizletInfo.toastDuration || 2e3
                    });
                // Note: when forced only send if changedByUser   
                self.submit(self.options);
            });
            self.wizletContext.find("[data-clearButton]").on("click", function () {
                if (self.options.wizletInfo.type === "QUESTIONS") {
                    self.render(self.options, true);
                }
                else if (self.options.wizletInfo.type === "VOTES") {
                    //self.reset(self.options);
                    self.render(self.options, true);
                    self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').removeClass('pc-savedmessage');
                }
                if (self.wizletInfo.buttons) {
                    if (self.wizletInfo.buttons[0].navigation.title) {
                        self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').find('[data-prioritizeSubmitButtonText]').html(self.wizletInfo.buttons[0].navigation.title);
                    }
                    else {
                        self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').find('[data-prioritizeSubmitButtonText]').html('Submit');
                    }
                }
                else {
                    self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').find('[data-prioritizeSubmitButtonText]').html('Submit');
                }
            });
        };

        Prioritize.prototype.checkForReadOnlyCondition = function () {
            var self = this;
            var readonly = false;
            if (self.wizletInfo.readOnlyCondition) {
                if( ( self.wizletInfo.readOnlyCondition === true ) || ( self.wizletInfo.readOnlyCondition === "true" ) ){
                    if( self.wizletInfo.readOnlyQuestion ){
                        var qIds = [];
                        if( self.wizletInfo.readOnlyQuestion ){
                            qIds.push( self.wizerApi.getQuestionIdByName( self.wizletInfo.readOnlyQuestion ) );
                        }
                        self.wizerApi.getMyVotes(qIds).then((r)=>{
                            if( r && r.success ){
                                for( var i = 0; i < qIds.length; i++ ){
                                    readonly = r.votes[ qIds[ i ] ][ 0 ];
                                    if( readonly || self.isFollower ){
                                        self.wizletContext.find('[data-prioritizebuttons]').hide();
                                        self.wizletContext.find('[data-prioritizecomponenttablediv]').addClass('pc-readonly');
                                        $("[data-prioritizeComponentTableDiv]").find("[data-prioritizeOrderListRight]").sortable( "disable" );
                                        if( self.wizletInfo.buttons[0].navigation.idToShow ){
                                            $('#'+self.wizletInfo.buttons[0].navigation.idToShow).removeAttr('hidden');
                                        }
                                        break;
                                    }
                                }
                            }
                        });
                    }
                    else{
                        readonly = true;
                        self.wizletContext.find('[data-prioritizebuttons]').hide();
                        self.wizletContext.find('[data-prioritizecomponenttablediv]').addClass('pc-readonly');
                        $("[data-prioritizeComponentTableDiv]").find("[data-prioritizeOrderListRight]").sortable( "disable" );
                        if( options.wizletInfo.buttons[0].navigation.idToShow ){
                            $('#'+options.wizletInfo.buttons[0].navigation.idToShow).removeAttr('hidden');
                        }
                    }
                }
                else{
                    readonly = self.options.wizletInfo.type === "QUESTIONS" ? self.wizerModel.checkForReadOnlyCondition(self.wizletInfo, self.wizletInfo.questions) : self.wizerModel.checkForReadOnlyCondition(self.wizletInfo, self.wizletInfo.question);
                    if (readonly) {
                        self.wizletContext.find('[data-prioritizebuttons]').hide();
                        self.wizletContext.find('[data-prioritizecomponenttablediv]').addClass('pc-readonly');
                    }
                }
            }
            return readonly;
        }

        Prioritize.prototype.makeSortable = function () {
            var self = this;

            if (Wizer.editMode) {
                // sortable do not work under edit mode - changing the order of the option list
                return;
            }

            var elements = this.containerDom.find("[data-prioritizeOrderListRight]");
            elements.sortable({
                start: function(event, ui){
                    ui.placeholder.height(ui.helper.height());
                    return true;
                },
                change: function(evt, ui){
                    
                    // added ability to track if option has been moved
                    if(self.options.wizletInfo.trackChanges){
                        ui.helper.addClass("sorted");
                        //console.log(ui.helper);
                    }

                    self.adjustHeight();
                    self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').find('[data-prioritizeSubmitButtonText]').removeClass('pc-savedmessage')
                        .html(self.wizletInfo.buttons[0].navigation.title);
                }
            });

            this.containerDom.find("div").disableSelection();
        };

        Prioritize.prototype.adjustHeight = function () {
	        var z=0;
	        $(".pc-rowitem").each(function(i, e){
	            if(!$(e).hasClass("ui-sortable-helper")){
	                $(".pc-orderlistleftpanel").each(function(i2, e2){
	                    if(i2==z){
	                        e2.style.height = e.clientHeight + "px";
	                    }
	                });

	                z++;
	            }
	        });
        };

        Prioritize.prototype.unloadHandler = function () {
            //unload wizletbase
            WizletBase.unloadHandler({ wizlet: this });
        };

        Prioritize.prototype.iAmFollower = function () {
            var self = this;
            return (self.wizletInfo.isFollower && 
                    self.wizletInfo.DB[self.wizletInfo.isFollower] == 1);
        };
    
        Prioritize.prototype.validate = function () {
            var self = this;
            var validating = new Q.defer();
            var promiseArr = [], promise;
            if (self.iAmFollower()) {
                self.isFollower = true;
                var teamId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackTeam);
                $.each(self.wizletInfo.scope, function (idx, condition) {
                    condition = ( typeof condition === "string" ) ? condition : condition.name;
                    var questionId = self.wizerApi.getQuestionIdByName(condition);
                    promise = self.wizerApi.getForemanVotes(teamId,[questionId]);    
                    promiseArr.push(promise); 
                    promise.then(function (response) {
                        self.leaderVotes[questionId] = response.votes[questionId][0];
                        if ( response.votes[questionId].length > 0 ) {
                            self.wizletInfo.DB[condition] = response.votes[questionId][0];
                        }
                    });  
                });
                Q.all(promiseArr).then(function (response) {
                    validating.resolve();
                });
            } else {
                promiseArr.push(null);
                validating.resolve();
            };
            return validating.promise;
        };

        Prioritize.prototype.render = function (options, resetVotes) {
            var self = this;
            if (resetVotes === undefined)
                resetVotes = false;
            return self.validate().then(()=>{
                self.templateDefer.promise.then(function (template) {
                    var fragment = template(options.wizletInfo);
                    options.context.html(fragment);
                    self.options = options;
                    if (self.wizletInfo.type === "VOTES") {
                        var qId = self.wizerApi.getQuestionIdByName(options.wizletInfo.question);
                        var gettingVotes = self.wizerApi.getVotes({ questionId: qId, resetVotes: resetVotes });
                        gettingVotes.then(function (result) {
                            if (self.componentVersion < 2) {
                                self.result = result;
                                var leftPanelDivsContainer = options.context.find('[data-prioritizeComponentTable]').find('[data-prioritizeOrderListLeft]');
                                var rightPanelDivsContainer = options.context.find('[data-prioritizeComponentTable]').find('[data-prioritizeOrderListRight]');
                                for (var i = 1; i <= result.votes.length; i++) {
                                    $(leftPanelDivsContainer).append('<div class="prioritizeOrderListleft' + i + ' prioritizeOrderListLeftPanel" data-orderlistleftpanel><span class="prioritizeOrderIndex">' + i + '</span></div>');
                                    $(rightPanelDivsContainer).append('<div class="prioritizeVotes' + i +
                                        ' prioritizeRowItem" id="prioritizeOrderList" data-rowitem><div class="prioritizeRowItem' + i +
                                        'RightPanel prioritizeRightPanel"><div class="prioritizeRowItem' + i +
                                        'DraggableContainer draggableContainer"><div class="prioritizeRowItem' + i +
                                        'DraggableSign draggableSign1"></div><div class="prioritizeRowItem' + i +
                                        'DraggableSign draggableSign2"></div><div class="prioritizeRowItem' + i +
                                        'DraggableSign draggableSign3"></div></div><div class="prioritizeRowItem' + i +
                                        'Text prioritizeComponentVotes" data-votes rel=' + result.votes[i - 1].universalId + '><p>' +
                                        result.votes[i - 1].responseText + '</p></div></div></div>');
                                }
                            } else {
                                options.wizletInfo.results = result.votes;
                                self.result = result;
                                var fragment = template(options.wizletInfo);
                                options.context.html(fragment);
                            }
                            self.setHeightAndCheckReadonly();
                        });

                    } else if (self.wizletInfo.type === "QUESTIONS") {
                        if (self.componentVersion < 2) {
                            var leftPanelDivsContainer = options.context.find('[data-prioritizeComponentTable]').find('[data-prioritizeOrderListLeft]');
                            var rightPanelDivsContainer = options.context.find('[data-prioritizeComponentTable]').find('[data-prioritizeOrderListRight]');
                            for (var i = 1; i <= options.wizletInfo.questions.length; i++) {
                                $(leftPanelDivsContainer).append('<div class="prioritizeOrderListleft' + i + ' prioritizeOrderListLeftPanel" data-orderlistleftpanel><span class="prioritizeOrderIndex">' + i + '</span></div>');
                                $(rightPanelDivsContainer).append('<div class="prioritizeVotes' + i +
                                ' prioritizeRowItem" id="prioritizeOrderList" data-rowitem><div class="prioritizeRowItem' + i +
                                'RightPanel prioritizeRightPanel"><div class="prioritizeRowItem' + i +
                                'DraggableContainer draggableContainer"><div class="prioritizeRowItem' + i +
                                'DraggableSign draggableSign1"></div><div class="prioritizeRowItem' + i +
                                'DraggableSign draggableSign2"></div><div class="prioritizeRowItem' + i +
                                'DraggableSign draggableSign3"></div></div><div class="prioritizeRowItem' + i +
                                'Text prioritizeComponentVotes" data-votes rel="' + options.wizletInfo.questions[i - 1].binding + '"><p>' +
                                options.wizletInfo.questions[i - 1].title + '</p></div></div>');

                            }
                            self.setHeightAndCheckReadonly();
                        } else {
                            if (!resetVotes) {
                                var questionIds = [];
                                var questionMap = {};
                                $.each(self.wizletInfo.questions, function (id, question) {
                                    var q = question.binding;
                                    var qid = self.wizerApi.getQuestionIdByName(q);
                                    question.qId = qid;
                                    questionMap["Q_" + qid] = question;
                                    questionIds.push(qid);
                                });

                                votes = self.wizerApi.getMyVotes(questionIds);
                                votes.then(function (response) {
                                    $.each(response.votes, function (id, vote) {
                                        var q = questionMap["Q_" + id];
                                        if( self.isFollower ){
                                            if( self.leaderVotes[id] && self.leaderVotes[id][0] ){
                                                q.resposeText = self.leaderVotes[id][0];
                                            }
                                        }
                                        else{
                                            q.resposeText = vote[0];
                                        }
                                    });
                                    self.wizletInfo.questions.sort(function (a, b) {
                                        if (b.resposeText == undefined || a.resposeText == undefined)
                                            return 0;
                                        else
                                            return b.resposeText - a.resposeText
                                    });
                                    options.wizletInfo.results = self.wizletInfo.questions;
                                    self.loadTemplate(options, template);
                                });
                            } else {
                                options.wizletInfo.results = self.questions;
                                self.loadTemplate(options, template);
                            }
                        }

                    }

                    $(window).on('resize', function (event) {
                        var win = $(this); //this = window
                        self.setHeight();
                    });
                    return true;
                })
                .fail(this.wizerApi.showError)
            });
        };

        Prioritize.prototype.setHeightAndCheckReadonly = function () {
            var self = this;
            self.setHeight();
            if (!self.checkForReadOnlyCondition())
                self.init();
        };
        Prioritize.prototype.loadTemplate = function (options, template) {
            var self = this;
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            self.setHeightAndCheckReadonly();
        };
        Prioritize.prototype.setHeight = function (result) {
            var self = this;
            var leftPanelDivsContainer = self.options.context.find('[data-prioritizeComponentTable]').find('[data-prioritizeOrderListLeft]');
            var rightPanelDivsContainer = self.options.context.find('[data-prioritizeComponentTable]').find('[data-prioritizeOrderListRight]');
            var leftPanelDivs = leftPanelDivsContainer.find('[data-orderlistleftpanel]');
            var rightPanelDivs = rightPanelDivsContainer.find('[data-rowitem]');
            if (self.wizletInfo.type === "VOTES") {
                for (var i = 1; i <= self.result.votes.length; i++) {
                    var height = $(rightPanelDivs[i - 1]).height();
                    $(leftPanelDivs[i - 1]).css('height', height + 'px');
                }
            } else if (self.wizletInfo.type === "QUESTIONS") {
                for (var i = 1; i <= self.options.wizletInfo.questions.length; i++) {
                    var height = $(rightPanelDivs[i - 1]).height();
                    $(leftPanelDivs[i - 1]).css('height', height + 'px');
                }
            }
        };

        Prioritize.prototype.submit = function (options) {
            var self = this;
            var rankItems = self.options.context.find("[data-prioritizeOrderListRight] div.[data-votes]");
            if (self.wizletInfo.type === "VOTES") {
                var res = "questionId=" + self.wizerApi.getQuestionIdByName(self.options.wizletInfo.question);
                for (var i = 0; i < rankItems.length; i++) {
                    var rankItemDom = $(rankItems[i]);
                    var universalId = rankItemDom.attr("rel");
                    res += "&ranks[" + i + "].id=" + universalId + "&ranks[" + i + "].rank=" + (rankItems.length - i);
                }
                AjaxGetJson('Vote', 'SetRanks', res, function (val) {
                    //callback: (eval(val) == true);

                }, function () {
                    //callback(false);
                });

            } else if (self.wizletInfo.type === "QUESTIONS") {
                var rankItemsText = self.options.context.find("[data-prioritizeOrderListRight] div.responseText");
                var votes = [];
                for (var i = 0; i < rankItems.length; i++) {
                    var rankItemDom = $(rankItems[i]);
                    var rankItemTextDom = $(rankItemsText[i]);
                    var qId = self.wizerApi.getQuestionIdByName(rankItemDom.attr("rel"));
                    var responseText = (rankItems.length + 1) - (i + 1);
                    votes.push({ questionId: qId, responseText: responseText });
                }
                self.wizerApi.addVotes({ votes: votes }).then(function(){
                if( self.wizletInfo.readOnlyCondition ){
                    self.checkForReadOnlyCondition();
                }});

            }
            self.setCssClass();
            if( options.wizletInfo.buttons[0].navigation.idToShow ){
                $('#'+options.wizletInfo.buttons[0].navigation.idToShow).removeAttr('hidden');
            }
        };

        Prioritize.prototype.setCssClass = function () {
            var self = this;
            if (self.componentVersion < 2) {
                self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').addClass('prioritizeSavedMessage');
            } else {
                self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').find('[data-prioritizeSubmitButtonText]').addClass('pc-savedmessage');
            }
            var confirmMsg = self.wizletInfo.confirmMsg;
            if (!confirmMsg) confirmMsg = "Saved";
            self.options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').find('[data-prioritizeSubmitButtonText]').html(confirmMsg);
        };
        
        Prioritize.prototype.reset = function (options) {
            var self = this;
            var leftPanelDivs = options.context.find('[data-prioritizeComponentTable]').find('[data-orderlistleftpanel]');
            var rightPanelDivsContainer = options.context.find('[data-prioritizeComponentTable]').find('[data-prioritizeOrderListRight]');
            rightPanelDivsContainer.html('');
            if (self.componentVersion < 2) {
                for (var i = 1; i <= this.result.votes.length; i++) {
                    $(rightPanelDivsContainer).append('<div class="prioritizeVotes' + i + ' prioritizeRowItem" id="prioritizeOrderList" data-rowitem><div class="prioritizeRowItem' + i + 'RightPanel prioritizeRightPanel"><div class="prioritizeRowItem' + i + 'DraggableContainer draggableContainer"><div class="prioritizeRowItem' + i + 'DraggableSign draggableSign1"></div><div class="prioritizeRowItem' + i + 'DraggableSign draggableSign2"></div><div class="prioritizeRowItem' + i + 'DraggableSign draggableSign3"></div></div><div class="prioritizeRowItem' + i + 'Text prioritizeComponentVotes" rel=' + this.result.votes[i - 1].universalId + ' data-votes><p>' + this.result.votes[i - 1].responseText + '</p></div></div></div>');
                }
            } else {
                options.context.find('[data-prioritizeButtons]').find('[data-submitButton]').find('[data-prioritizeSubmitButtonText]').removeClass('pc-savedmessage');
                for (var i = 1; i <= this.result.votes.length; i++) {
                    $(rightPanelDivsContainer).append('<div class="prioritizeVotes' + i + ' pc-rowitem" id="prioritizeOrderList" data-rowitem><div class="prioritizeRowItem' + i + 'RightPanel pc-rightpanel"><div class="prioritizeRowItem' + i + 'DraggableContainer pc-draggablecontainer"><div class="prioritizeRowItem' + i + 'DraggableSign pc-draggablesign1"></div><div class="prioritizeRowItem' + i + 'DraggableSign pc-draggablesign2"></div><div class="prioritizeRowItem' + i + 'DraggableSign pc-draggablesign3"></div></div><div class="prioritizeRowItem' + i + 'Text pc-votes" rel=' + this.result.votes[i - 1].universalId + ' data-votes><p>' + this.result.votes[i - 1].responseText + '</p></div></div></div>');
                }
            }
            self.setHeight();
            self.init();
        };
        Prioritize.getRegistration = function () {
            return new Prioritize();
        };
        return Prioritize;
    });