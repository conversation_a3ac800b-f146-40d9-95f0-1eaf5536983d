<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutFAC">
  <Include name="HeaderFAC"></Include>
  

  <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      _header: "!{R2_Welcome_Header} ",
      valign: false,
      animate: "zoomIn",
      transparentBox: false,
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{R2_Welcome_Image}",  alt: "!{R2_Welcome_Header}" ,
          position: "left large",
          src_vert: "!{}",
          animate: "fadeInLeft animate__delay-1s", _animateLater: "bounce"
        },
        img: { 
          materialboxed: false, _borders: "top right left bottom", frame: "", nopadding: false,
          src: "!{R2_Welcome_Image}",  alt: "!{R2_Welcome_Header}",
          isHiddenWhenSmall: true, 
          src_vert: "!{}",
          animate: "fadeInUp animate__delay-1s"
        },
        position: "up down",
        animate: "fadeInUp animate__delay-1s",
        title: "!{R2_Welcome_Title}",
        body: "!{R2_Welcome_Text}"
      }
    }]]>  
  </Component>


  <Component type="Collapsible" customJS="true">
    <![CDATA[{
      templateInEvent: "html/collapsible.dot",
      css: "styles/collapsible.css",
      animate: "fadeInLeft animate__delay-2s",
      accordion: true,
      popout: false,
        expand_more: "expand_more",
        expand_less: "expand_less",
      items: [
        {
          active: false,
          title: "!{R2_Welcome_FACtit}", 
          text: "!{R2_Welcome_FACtext}",
          _texts: [
            { title: "!{}", text: "!{}" },
            { title: "!{}", text: "!{}" } 
          ]
        }
      ]
    }]]>
  </Component> 


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left clicked",
          gdActionTrack: "GD", gdActionSection: "R2_Welcome",
          icon: "replay"
        },
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left2",
          gdActionTrack: "GD", gdActionSection: "LandingPage_Pause",
          icon: "pause_circle_filled"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list",
          isFloat: false, 
          tooltip: "!{}"
        },
        {
          type: "target",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Start",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{GD_SIM_R2_Landing} (SELFPACED MODE)",
          icon: "screen_share"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>