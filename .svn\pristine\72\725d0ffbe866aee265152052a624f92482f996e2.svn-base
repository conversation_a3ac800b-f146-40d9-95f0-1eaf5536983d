@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
//@import "../materialize-src/sass/components/variables";
@import "mixins";



.confetti-wrapper {
  position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;


  [class|="confetti"] {
    position: absolute;
  }

  $colors: (
    color("red", "base"), 
    color("pink", "base"), 
    color("purple", "base"), 
    color("deep-purple", "base"), 
    color("indigo", "base"), 
    color("light-blue", "base"), 
    color("cyan", "base"), 
    color("teal", "base"), 
    color("green", "base"), 
    color("light-green", "base"), 
    color("lime", "base"), 
    color("yellow", "base"), 
    color("amber", "base"), 
    color("orange", "base"), 
    color("deep-orange", "base"));

  @for $i from 0 through 200 {
    $w: random(10);
    $l: random(110)-10;
    .confetti-#{$i} {
      width: #{$w}px;
      height:#{$w*0.4}px;
      background-color: nth($colors, random(15));
      top: -10%;
      left: unquote($l+"%");
      opacity: random() + 0.5;
      transform: rotate(#{random()*360}deg);
      animation: drop-#{$i} unquote(4+random()+"s") unquote(random()+"s") infinite;
      @include animation-delay(1s);
    }

    @keyframes drop-#{$i} {
      100% {
        top: 110%;
        left: unquote($l+random(15)+"%");
      }
    }
  }
}

