@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

.wizlet.wizletNavigation {
    .container > .row {
        clear: both;
        padding-top: 5px;
        &.fixed {
            float: none !important;
            &.left {
                text-align: left;
            }
            &.center {
                text-align: center;
            }
            &.right {
                text-align: right;
            }
        }
    }
    .btn-large {
        font-size: inherit;
    }

    a.btn-floating {

        &.left, &.left2 {
            position: absolute;
            left: 0;
            width: 35px;
        }
        &.left2 {
            margin-left: 5px;
            transform: translateX(100%);
        }

        &.pause.clicked {
            opacity: 0.5;
            filter: grayscale(1);
        }
    }

    span a[href] {
        color: color("client-colors", "font2");
    }
    span[disabled] a {
        pointer-events: none;
        background-color: $button-disabled-background !important;
        box-shadow: none;
        color: $button-disabled-color !important;
        cursor: default;
        &:hover {
            background-color: $button-disabled-background !important;
            color: $button-disabled-color !important;
        }
    }
}