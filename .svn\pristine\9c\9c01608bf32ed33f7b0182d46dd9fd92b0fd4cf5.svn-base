.tabsdoubleColumn .tabs-holder li.tab {
    width: 50% !important;
}

.tabsdoubleColumn .tab-component.doubleColumn > .row.doubleTextArea,
.tabsdoubleColumn .tab-component.doubleColumn > .row.doubleChatBox {
    margin: 20px auto 0 auto;
}

.tabsdoubleColumn .tab-component.doubleColumn .row.doubleChatBox {
    margin-top: 0;
}
.tabsdoubleColumn .tab-component.doubleColumn .row.doubleTextArea .container,
.tabsdoubleColumn .tab-component.doubleColumn .row.doubleChatBox .container {
    width: 90%;
}
