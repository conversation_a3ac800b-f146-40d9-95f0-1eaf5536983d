<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayout_SIM_R3" layout="../../../layout/tabsLayout2">
  <Include name="Header_SIM_R3"></Include>
  <Include name="KPIgauges_R3"></Include>


  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "bounceInLeft",
      progress: "6",
      steps: [ "!{SIM_BreadCrumbs_0}",
               "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}",
               "!{SIM_BreadCrumbs_5}" ]
    }]]>
  </Component>



  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R3_Scenario5b_FB_Header}",
      swipeable: true,
      tabs: [
          "!{Feedback_Opt} !{Choice_Opt1}",
          "!{Feedback_Opt} !{Choice_Opt2}",
          "!{Feedback_Opt} !{Choice_Opt3}"
      ],
      correctOption: 0,
      myText: "!{Feedback_MyOpt}",
      isCheck: false,
      bind: "Q_SIM_R3_Scenario5",
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower","Q_SIM_R3_Scenario5"]
  }]]></Component>


  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      _isHidden: true,
        _condition: "Q_SIM_R3_Scenario5",
        _condition_Diff: "1",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R3_Scenario5b_FB_Opt1_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R3_Scenario5b_FB_Opt1_Title}",
        body: "!{SIM_R3_Scenario5b_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R3_Scenario5b_Opt1_KPI1}","!{SIM_R3_Scenario5b_Opt1_KPI2}","!{SIM_R3_Scenario5b_Opt1_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R3_Scenario5",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R3_Scenario5"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      _isHidden: true,
        _condition: "Q_SIM_Initiatives_1",
        _condition_Diff: "1",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R3_Scenario5b_FB_Opt2_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R3_Scenario5b_FB_Opt2_Title}",
        body: "!{SIM_R3_Scenario5b_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R3_Scenario5b_Opt2_KPI1}","!{SIM_R3_Scenario5b_Opt2_KPI2}","!{SIM_R3_Scenario5b_Opt2_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R3_Scenario5",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R3_Scenario5","Q_SIM_Initiatives_1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R3_Scenario4",
        condition_Val: "1",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R3_Scenario5b_FB_Opt3_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R3_Scenario5b_FB_Opt3_Title}",
        body: "!{SIM_R3_Scenario5b_FB_Opt3_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R3_Scenario5b_Opt3_KPI1}","!{SIM_R3_Scenario5b_Opt3_KPI2}","!{SIM_R3_Scenario5b_Opt3_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R3_Scenario5",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R3_Scenario5","Q_SIM_R3_Scenario4"]
    }]]>
  </Component>





  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>





  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      _isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "SIM_R3_Scenario6",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R3_Scenario5b_FB_Header}"/>
  </Voting>





</Action>