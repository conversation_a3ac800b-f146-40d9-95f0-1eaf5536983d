@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


.wizlet.wizletCard,
.wizlet.wizletVanilla {

    .container {

        &.borders {
            @include card-border(10px,double); 
        }
        .fit-modal {
            position: absolute;
            top: 70px;
            margin-left: 16px;
            z-index: 99;
            color: color("client-colors", "font2");
            text-shadow: 2px 2px #232323;
            @media only screen  and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
                font-size: 2rem; 
            }
            @media #{$medium-and-down} {
                font-size: 1.5rem;
                width: 80%;
                text-align: left;
                top: 8%;
            }
        }

        &[highlight]{
            opacity: 0.3 !important;
        }
    }

    .card {

        &.no-header {
            margin-top: 10px;
        }
        &.transparent {
            margin-top: 0;
            background: transparent;
            border: none;
            box-shadow: none;

            .card-image {
                background: transparent;
            }
        }
        &.gray {
            opacity: 0.5;
            pointer-events: none;
            filter: grayscale(100%);
        }

        &.horizontal {

            .card-content.no-image {
                width: 100%;
            }
            .card-image {
                padding: 24px 0px;
            }
            &.up .card-image {
                padding-right: 24px;
            }
            &.down .card-image {
                padding-left: 24px;
            }
        }

        .card-image {
            min-width: 40%;
            max-width: 60%;
            
            @media #{$large-and-up} {
                min-width: 30%;
                max-width: 40%;
            }

            &.small {
                width: 40%;                
                @media #{$large-and-up} {
                    width: 30%;
                }
            }

            &.full-width {
                max-width: 100%;
                max-height: 100%;
                height: 100%;
                img {
                    //height: 100%;
                    border-radius: 0;
                    object-fit: contain;
                }
            }
            &.center-width{
                width: 220px;
                height: 430px;
                padding: 1rem;
                margin: auto;
                img {
                    height: 100%;
                    object-fit: contain;
                }
            }
            &.center-width-small{
                width: 100%;
                padding: 1rem;
                margin: auto;
                img {
                    height: 100%;
                    object-fit: contain;
                }
            }
            &.center-width-small2{
                width: 100%;
                height: 300px;
                padding: 1rem;
                margin: auto;
                img {
                    height: 100%;
                    object-fit: contain;
                }
            }
            
            &.extra-padding {
                padding: 10px;
            }
        
            display: block;
            img:not(.with-grid) { 
                margin: auto; 
            }

            &.borders {
                @include card-border(2px,solid,""); 
            }
            img {
                &.frame {
                    border: 2px color("client-colors", "border") solid;
                    border-radius: 10px;
                    &.bottom {
                        border-width: 10px;
                        border-top: none;
                        border-right: none;
                        border-left: none;
                        border-radius: 10px;
                    }
                }
                &.maxwidth {
                    max-width: fit-content; 
                    max-height: 50%;
                }
                &.with-height {
                    object-fit: cover;
                }
            }

            video { 
                display: block;
                position: relative;
                // position: absolute;
                left: 0; right: 0; top: 0; bottom: 0;
                width: 100%;
                height: 100%;
                background-color: black;
            }
        }

        .card-image, .card-content {
            background-color: color("client-colors", "white");
        }

        .card-content {

            overflow: hidden;
            
            //display: inline-block;
            &.row {
                margin: 0;
            }

            @include card-title; 
            p {
                text-align: left;
                &:not(.flow-text) {
                    font-size: inherit;
                }
            }
            
            @media #{$medium-and-down} {
                padding: 12px;
            }

            // overflow: auto;

            img.embed {
                clear: both;
                width: 40%;
                @media #{$small-and-down} {
                    width: 100%;
                }
                @media #{$large-and-up} {
                    width: 45%;
                }
                &.left {
                    float: left;
                    margin-right: 12px;
                }
                &.right {
                    float: right;
                    margin-left: 12px;
                }
                &.verytiny {
                    width: 15%;
                }
                &.tiny {
                    width: 20%;
                }
                &.small {
                    width: 30%;
                }
                &.medium {
                    width: 40%;
                }
                &.large {
                    width: 50%;
                }
                &.extralarge {
                    width: 70%;
                }

                ~span{
                    word-break: break-word;
                }

                
                &.bounce {
                    @include animation-name(bounceInfinite);
                    @include animation-duration(10s);
                    @include animation-iteration-count(infinite);
                    @include animation-timing-function(ease);
                    @include animation-delay(5s);
                    @include animation-fill-mode(forwards);    
                }
            }

            &.centered {
                //width: 100%;
                text-align: center;
                span, p {
                    text-align: center;
                }
            }

            &.onlyImage {
                padding: 0;
            }
            
            &.borders {
                @include card-border(10px,outset,""); 
            }

            .feedback {
                
                @include card-title(125%); 

                li {
                    clear: both;
                    margin: 1rem 0 0 0;
                   
                    > div {
                        display: inline-flex;
                
                        span {
                            white-space: nowrap;
                            &.value {
                                font-weight: bold;
                            }
                            &.title {
                                margin-right: 5px;
                            }
                            &.title2 {
                                margin-left: 5px;
                            }
                        }
                    }
                }

                .feedback-image {
                    text-align: center;
                }
            }
        }

        
        .score-container {
            margin: 20px 25% 0 25%;
            width: 50%;
            clear: both;
            @media #{$small-and-down} {
                margin: 20px 10% 0 10%;
                width: 80%;
            }

            border-radius: 5px;
            border: 3px solid color("client-colors", "border");

            
            .card-title {
                border-color: color("client-colors", "border");
                padding: 10px 0;
                &.centered {
                    text-align: center;
                }
            }

            &.selected {
                border-color: color("client-colors", "secondary");
                .card-title {
                    border-color: color("client-colors", "secondary");
                }

            }

            .points {
                overflow: hidden;
                text-align: center;
                margin: 10px;
                padding-top: 5px;

                &:not(:last-child) {    
                    // padding-bottom: 5px;
                    border-bottom: 1px solid color("client-colors", "font");
                }

                &.bold span {
                    font-size: 120%;
                    font-weight: bolder;
                }

                .label > span {
                
                    position: relative;
                    > img {
                        width:42px; height: 42px;
                        object-fit: contain;
                        position: absolute;
                        padding: 5px;
                        //top: -50%;
                        bottom: -10px;
                        transform: translateX(-110%);
                        // margin-right: 5px;
                        // border-radius: 5px;
                    }
                }

                .score > span {
                    vertical-align: super;
                    &.first {
                        font-size: 150%;
                        font-weight: bolder;
                    }
                }
                
                .score > i.inverse {
                    &.red-text {
                        color: color("client-colors", "green") !important;
                    }
                    &.green-text {
                        color: color("client-colors", "red") !important;
                    }
                }
            }

        }

        
        &.horizontal {
            .score-container {
                margin: 20px 5% 0 5%;
                width: 90%;
                @media #{$small-and-down} {
                    margin: 20px 0% 0 0%;
                    width: 100%;
                }
            }
        }        
        &.vertical {
            &.up {
                .card-image, .card-image > img {
                    border-bottom-left-radius: $card-panel-border;
                    border-bottom-right-radius: $card-panel-border;
                }
            }
            &.down {
                .card-image, .card-image > img {
                    border-top-left-radius: $card-panel-border;
                    border-top-right-radius: $card-panel-border;
                }
            }
        }


        &.noborders, 
        &.noborders > .card-image, 
        &.noborders > .card-image > img {
            border-radius: 0 !important;
        }
        
        .detail-container {
            text-align: right;

            .header.detail {
                padding: 0 36px;
            }
            i.detail {
                position: absolute;
                font-size: 2rem;
                right: 2.5rem;
                bottom: 1rem;

                cursor: pointer;
                &:hover {
                    font-size: 3rem;
                }

                &.bounce {
                    @include animation-name(bounceInfinite);
                    @include animation-duration(5s);
                    @include animation-iteration-count(infinite);
                    @include animation-timing-function(ease);
                    @include animation-fill-mode(forwards);
                }
            }
        }


        &.with-more-info-button {
            padding-bottom: 5rem;

            .row.moreInfo {
                position: absolute;
                bottom: 0;
                right: 0;
                //right: $card-padding;

                >a[href] {
                    font-size: 10px;
                    padding-top: 0; padding-bottom: 0;
                    color: color("client-colors","font2");
                }
            }
        }

        &.animated-frame {

            $width: 4px;
            $length: 25px;
            $spacing: 40px;
            $color: color("client-colors", "border");

            padding: $width;

            background-image: 
                repeating-linear-gradient(0deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing), 
                repeating-linear-gradient(90deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing), 
                repeating-linear-gradient(180deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing), 
                repeating-linear-gradient(270deg, $color, $color $length, transparent $length, transparent $spacing, $color $spacing);
            background-size: $width calc(100% + #{$spacing}), calc(100% + #{$spacing}) $width, $width calc(100% + #{$spacing}) , calc(100% + #{$spacing}) $width;
            background-position: 0 0, 0 0, 100% 0, 0 100%;
            background-repeat: no-repeat;
            animation: borderAnimation 2s infinite linear;
            @keyframes borderAnimation {
                from { background-position: 0 0, -#{$spacing} 0, 100% -#{$spacing}, 0 100%; }
                to { background-position: 0 -#{$spacing}, 0 0, 100% 0, -#{$spacing} 100%; }
            }

        }
    }
    
}