<Action>

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{SIM_R2_Scenario6_Question}",
        binding: "Q_SIM_R2_Scenario6"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario6_FB_Solution}"
    ],
    trackQuestion: "Show_charts",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>


</Action>