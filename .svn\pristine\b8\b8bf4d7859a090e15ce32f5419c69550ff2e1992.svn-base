<?xml version="1.0" encoding="utf-8" ?>
<Action>


<!-- ******* -->
<!-- RANKING -->
<!-- ******* -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    class: "",
    position: "!{Ranking_Position}",
    _rank: 
      {
        title: "!{Ranking_Position}",
        binding: "Score_SIM_Total_R2_Rank"
      },
    header: "!{}",
    subheader: "!{}",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_verticalMode_ myresponsive-table _noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{KPI_Metric1}",
        binding: "Score_SIM_Total_R2_KPI1",
        format: "##0", _suffix: "%"
      },
      {
        show: true,
        title: "!{KPI_Metric2}",
        binding: "Score_SIM_Total_R2_KPI2",
        format: "##0", _suffix: "%"
      },
      {
        show: true,
        title: "!{KPI_Metric3}",
        binding: "Score_SIM_Total_R2_KPI3",
        format: "##0", _suffix: "%"
      },
      {
        show: true,
        title: "!{KPI_TOTAL}",
        binding: "Score_SIM_Total_R2",
				format: "0.0"
      }
    ],
    trackQuestion: "Show_charts",
    rankQuestion: "",
    sortByQuestion: "Score_SIM_Total_R2",
    sortOrder: "desc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>
  
  
  

</Action>