﻿/* 
    "LineChart" component that takes a template and some data as the input
    and renders the chart using highcharts library.

*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'highcharts-styled', 'css!lib/highcharts/code/css/highcharts.css', 'numeral', 'jsCalcLib/numberFormatting', '../styles/materialize-src/js/materialize.min'], 
    function ($, Q, WizerApi, WizletBase, doT, Highcharts, HighchartsCss, numeral, numberFormatting) {

    var HCLineChart = function () {
        this.type = 'HCLineChart';
        this.level = 1;
        this.chartDefaults = {
            "lang": {
                "thousandsSep": ",",
                "numericSymbols": ['k', 'm', 'b', 't']
            },
            "chart": {
                "type": 'line'
            },
            "credits": {
                "enabled": false
            },
            "legend":{
            },
            "title": {
                "text": ''
            },
            "xAxis": [
			{
                "categories": []
            }
			],
            "yAxis": {
                "stackLabels": {
                    "enabled": false,
                }
            },
            "tooltip": {
                "headerFormat": '<b>{point.x}</b><br/>',
                "pointFormat": '{series.name}: {point.y}'
            },
            "plotOptions": {
                "series": {
                    "dataLabels":{
                        "enabled": true
                    }
                },
            }
        }
    };

    HCLineChart.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = new Q.defer();
        var self = this;
        var requirements = [];
        requirements.push(WizletBase.loadTemplate(wizletInfo, 'lineChart.dot'));

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    HCLineChart.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
        $(document).off("wizer:model:change", this.redrawChart);
    };

    HCLineChart.prototype.render = function (options) {
        var self = this;
        var fetching = new Q.defer();

        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            self.isDelivery = self.wizletInfo.isDelivery || false;
              
            if (self.wizletInfo.hasModals) {
                self.modal = options.context.find('#hcLineChartModal');
                self.modal.modal();
            }

            self.renderChart().then(function () {
                fetching.resolve(true);
                // add redraw binder
                if (self.wizletInfo.listenModel)
                    $(document).off("wizer:model:change", self.redrawChart).on("wizer:model:change", {self: self}, self.redrawChart);
            });
        }).fail(this.wizerApi.showError);
    };

    HCLineChart.prototype.renderChart = function() {
        var self = this;
        var rendering = new Q.defer();

        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-linechartholder]");
        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);
        

        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            
            self.fetchVotes().then(function (data) {
                self.renderChartData.call(self, data, chartOptions, chartElem);
                rendering.resolve(true);
            });
        }
        else {

            if (self.wizletInfo.dataFromCSV && self.wizletInfo.dataFromCSV.csv) {

                var eventPath = '/Wizer/Pages/Events/'+self.wizerApi.eventName()+'/';

                $.get(eventPath+self.wizletInfo.dataFromCSV.csv, function (data) {
                    
                    // Split the lines
                    var itemDelimiter = self.wizletInfo.dataFromCSV.itemDelimiter || ';';
                    var lineDelimiter = self.wizletInfo.dataFromCSV.lineDelimiter || '\n';
                    var lines = data.split(lineDelimiter);
                    var alreadyDefinedSeries = (chartOptions.series && chartOptions.series.length>0) ? true : false;
                    
                    var series = chartOptions.series;

                    $.each(lines, function(lineNo, line) {
                        var items = line.split(itemDelimiter);
                        /* header line
                            1: xAxis.title.text
                            2: serie[0].name
                            3: serie[1].name
                            N: serie[N-1].name
                        */
                        if (lineNo == 0) {
                            $.each(items, function(itemNo, item) {
                                if (itemNo == 0) {
                                    chartOptions.xAxis[0].title = {text: item};
                                } else {
                                    if (!alreadyDefinedSeries)
                                        series.push ({name: item, data: []});
                                }
                            });
                        }
                        //the rest of the lines contain data with their name in the first position
                        else {
                            $.each(items, function(itemNo, item) {
                                if (itemNo == 0) {
                                    chartOptions.xAxis[0].categories.push(item);
                                } else {

                                    //Check if symbol marker for this data point
                                    if (series[itemNo-1].markers && series[itemNo-1].markers[lineNo] && series[itemNo-1].markers[lineNo].symbol) {
                                        
                                        series[itemNo-1].data.push({
                                                    y:parseFloat(item),
                                                    marker: {
                                                        symbol: 'url('+eventPath+series[itemNo-1].markers[lineNo].symbol+')'
                                                    }
                                        })
                                    } else {
                                        series[itemNo-1].data.push(parseFloat(item));
                                    }
                                }
                            });
                        }

                    });

                    chartOptions.series = series;

                    if (self.wizletInfo.hasModals)
                        chartOptions.plotOptions.series.point.events = {
                              click: function (e) {
                                  var yAxis = e.point.series.userOptions.yAxis;
                                  var index = e.point.index + 1;

                                  if (series[yAxis].markers && series[yAxis].markers[index] && series[yAxis].markers[index].text) {
                                    self.modal.find('.modal-content > .text').html(series[yAxis].markers[index].text);
                                    var instance = M.Modal.getInstance(self.modal);
                                    instance.open()
                                  }

                              }
                            };

                    chartElem.highcharts(chartOptions);
                    rendering.resolve(true);
                });

            } else {
                chartElem.highcharts(chartOptions);
                rendering.resolve(true);

            }


        }

        return rendering.promise;
    };


    HCLineChart.prototype.fetchVotes = function() {
        var self = this;
        var questions = [];
        var gettingVotes = new Q.defer();

        self.wizletInfo.questions.forEach(function(q) {
            questions.push(q.binding);
        });

        if(self.wizletInfo.xAxisQuestion){
            questions.push(self.wizletInfo.xAxisQuestion);
        }
        
        self.wizletInfo.chartConfig.xAxis.forEach(function(axis) {
            if(axis.question){
                questions.push(axis.question);
            }           
        });

        self.wizletInfo.chartConfig.series.forEach(function(axis) {
            if(axis.question){
                questions.push(axis.question);
            }           
        });
		

        self.wizerApi.getVotesByQuestionName(questions, self.wizletInfo.trackQuestion, self.wizletInfo.sorting, null, self.isDelivery).then(function (response) {
            var value = self.processVotes(response);
            gettingVotes.resolve(value);
        });

        return gettingVotes.promise;

    }

    HCLineChart.prototype.processVotes = function(votes) {
        var retVal = {}, series = [], self = this, name = [], symbol = [];
        
        self.wizletInfo.chartConfig.series.forEach(function(axis){
            
            for (var index = 0; index < votes.participants.length; index++) {
                const p = votes.participants[index];
                val = "";
                if(axis.question){
                    if(p.questionMap[axis.question] && 
                        p.questionMap[axis.question].value){
                        val = p.questionMap[axis.question].value;
                    }
                }
                if (axis.showTeam) val = p.name + ' ('+val+')';
                if (axis.isName && !val) val = p.name;
                name.push(val);
                if (axis.symbols && axis.symbols[index]) symbol.push(axis.symbols[index])
            }            
        });

        for (var i = 0; i < votes.participants.length; i++) {
            var data = [];
            var val;
            
            for (var index = 0; index < self.wizletInfo.questions.length; index++) {
                const q = self.wizletInfo.questions[index];
                const p = votes.participants[i];
				if(p.questionMap && p.questionMap[q.binding] && p.questionMap[q.binding].value){
                    val = numeral(p.questionMap[q.binding].value).value();
				}
                var dataObj = {y: val};                

                if (symbol && symbol[i]) {
                    dataObj.marker = { symbol: 'url('+'/Wizer/Pages/Events/'+self.wizerApi.eventName()+symbol[i]+')' };
                    if (self.wizletInfo.chartConfig.series[0].symbol_width && self.wizletInfo.chartConfig.series[0].symbol_height) {
                        dataObj.marker.width = self.wizletInfo.chartConfig.series[0].symbol_width;
                        dataObj.marker.height = self.wizletInfo.chartConfig.series[0].symbol_height
                    }
                }
                data.push(dataObj);
            }
            series.push({data: data, name: name[i]});
        }

        self.wizletInfo.chartConfig.xAxis.forEach(function(axis) {
            var cat = axis.categories || [];
            var val;
            for (var index = 0; index < votes.participants.length; index++) {
                const p = votes.participants[index];
                val = ""; 
                if(axis.question){
                    if(p.questionMap[axis.question] && 
                        p.questionMap[axis.question].value){
                        val = p.questionMap[axis.question].value;
                    }
                }else{
                    val = p.name;
                }
                cat.push(val);
            }
            axis.categories = cat;
            
            //series.push({data: cat});
        });
        retVal.series = series;
        retVal.xAxis = self.wizletInfo.chartConfig.xAxis;
        return retVal;
    }

    HCLineChart.prototype.renderChartData = function(data, chartOptions, chartElem) {

        chartOptions.xAxis = $.extend(true, [], chartOptions.xAxis, data.xAxis);
        chartOptions.series = $.extend(true, [], chartOptions.series, data.series);

        this.applyNumberFormat(chartOptions);
        this.formatTooltips(chartOptions);
        // render chart
        chartElem.highcharts(chartOptions);
    };

    HCLineChart.prototype.formatTooltips = function(chartOptions) {
        var formatters = discover(chartOptions, 'numformat');

        formatters.forEach(function(ob) {
            var format = ob.numformat,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0, point = this, out = '<strong>' + point.series.name + '</strong><br />';
                        out += '' + point.x + ': ';
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        out += numberFormatting.format(val, format, scaler);
                        return out;
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCLineChart.prototype.applyNumberFormat = function(chartOptions) {
        var formatters = discover(chartOptions, 'formatter');

        formatters.forEach(function(ob) {
            var format = ob.formatter,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0;
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        return numberFormatting.format(val, format, scaler);
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCLineChart.prototype.redrawChart = function(e) {
        var self = e.data.self;
        var redraw = new Q.defer();
        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-linechartholder]").highcharts();
        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);

        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            self.fetchVotes().then(function(data) {
                self.renderChartData.call(self, data, chartOptions, chartElem);
                // chart.series = data.series;
                redraw.resolve();
            });
        }
        return redraw.promise;
    };

    HCLineChart.getRegistration = function () {
        return new HCLineChart();
    };

    function search(tree, propertyName, result) {
        if ($.isArray(tree)) {
            for (var i = 0; i < tree.length; ++i) {
                search(tree[i], propertyName, result);
            }
        } else if ($.isPlainObject(tree)) {
            for (var pName in tree) {
                if (tree.hasOwnProperty(pName)) {
                    var subTree = tree[pName];
                    if (pName == propertyName) {
                        result.push(tree);
                    } else {
                        search(subTree, propertyName, result);
                    }
                }
            }
        }
    };

    function discover(src, propertyName) { 
        var propertyName = propertyName || 'formatter';
        var formatters = [];
        search(src, propertyName, formatters);
        return formatters;
    };

	
	
    return HCLineChart;

});
