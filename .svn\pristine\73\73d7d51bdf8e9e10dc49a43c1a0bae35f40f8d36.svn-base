
<div class="container{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.borders}} borders {{=it.borders}}{{?}}{{?it.containerMargin}} margin level{{=it.containerMargin}}{{?}}"
    {{?it.id}}id="{{=it.id}}"{{?}}
    
    {{?it.isHidden}}
        {{?it.condition}}
            {{?!it.DB[it.condition] || 
                (it.condition_Val && (it.DB[it.condition] == it.condition_Val)) ||
                (it.condition_Diff && (it.DB[it.condition] != it.condition_Diff)) ||
                (it.condition_Greater && (it.DB[it.condition] > it.condition_Greater)) ||
                (it.condition_Less && (it.DB[it.condition] < it.condition_Less))}}
                hidden
            {{?}}
        {{??}}
            hidden
        {{?}}
    {{?}}
    >  

    <div>
            
        {{?it.help}}
        <div class="header-container">   
        {{?}}

        {{?it.header}}
        <h4 class="header {{?it.headerLabel}}with-label{{?}}{{?it.help}} help{{?}}">
            {{?it.headerLabel}}<span class="new badge header-label" data-badge-caption="{{=it.headerLabel}}"></span>{{?}}
            {{=it.header}}
        </h4>
        {{?}}
   
        {{?it.subheader}}<h5 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.subheader}}</h5>{{?}}
   
        {{?it.help}}
            <i class="material-icons help client-colors-text text-primary {{?it.help.modalID}}modal-trigger" href="#{{=it.help.modalID}}{{?}}">{{?it.help.icon}}{{=it.help.icon}}{{??}}help{{?}}</i>
         </div>   
        {{?}}

        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        <div class="card">
            
            <div class="card-content">
                
                <!-- {{?it.title}}<h5 class="header title flow-text">{{=it.title}}</h5>{{?}} -->
                {{?it.title}}
                <h5 class="header title {{?it.titleLabel}}with-label{{?}}">
                    {{?it.titleLabel}}<span class="new badge header-label" data-badge-caption="{{=it.titleLabel}}"></span>{{?}}
                    <span class="text">{{=it.title}}</span>
                </h5>
                {{?}}
                {{?it.subtitle}}<h6 class="header strong subtitle">{{=it.subtitle}}</h6>{{?}}

                
                {{?it.content}}
                    {{?it.content.title}}<span class="card-title">{{=it.content.title}}</span>{{?}}
                                
                    {{?it.content.img_embed && it.content.img_embed.src}}
                        <img class="embed {{?it.content.img_embed.position}}{{=it.content.img_embed.position}}{{?}}" {{?it.content.img_embed.style}}style="{{=it.content.img_embed.style}}"{{?}}
                        src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img_embed.src}}" {{?it.content.img_embed.alt}}alt="{{=it.content.img_embed.alt}}"{{?}}/>
                    {{?}}                
        
                    {{?it.content.body}}
                        <span class="flow-text">{{=it.content.body}}</span>
                    {{?}}
                {{?}}



                {{?it.sideImage}}
                <div class="row same-heigh-columns">
                    <div class="col s1 side-image">
                        {{?it.sideImage.topTitle1}}<span class="title">{{=it.sideImage.topTitle1}}</span>{{?}}
                        {{?it.sideImage.topTitle2}}<span class="title">{{=it.sideImage.topTitle2}}</span>{{?}}
                        <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.sideImage.src}}" alt="{{=it.sideImage.alt}}"/>
                        {{?it.sideImage.bottomTitle1}}<span class="title">{{=it.sideImage.bottomTitle1}}</span>{{?}}
                        {{?it.sideImage.bottomTitle2}}<span class="title">{{=it.sideImage.bottomTitle2}}</span>{{?}}
                    </div>
                    <form class="col s11" novalidate autocomplete="off">
                {{??}}        
                <div class="row">
                    <form class="col s12" novalidate autocomplete="off">
                {{?}}


                        {{?it.options}}
                        <ul {{?it.isInline}}class="inline row"{{?}}>
                        {{~it.options :option:idx}}
                            <li class="{{?it.class}}{{=it.class}}{{?}} {{?it.DB[it.isFollower]>0}}disabled{{?}}{{?it.isInline}} col {{=it.optionWidth}}{{?}}
                                {{?option.isDisabled}}
                                    {{?option.condition}}
                                        {{?option.condition.questions}}
                                            {{?option.condition.type == "OR"}}
                                                {{?option.condition.questions.length == option.condition.reference.length}}
                                                    {{var cResult = false;}}
                                                    {{~option.condition.questions :question:qidx}}
                                                        {{?option.condition.reference[qidx]}}
                                                            {{?it.DB[question] == option.condition.reference[qidx]}}
                                                                {{cResult = true;}}
                                                            {{?}}
                                                        {{?}}
                                                    {{~}}
                                                    {{?option.condition.negate}}
                                                        {{cResult = !cResult;}}
                                                    {{?}}
                                                    {{?cResult}}
                                                        disabled {{?it.isCheck}}checked{{?}}
                                                    {{?}}
                                                {{??}}
                                                    {{?option.condition.reference[0]}}
                                                        {{var cResult = false;}}
                                                        {{~option.condition.questions :question:qidx}}
                                                            {{?option.condition.reference[qidx]}}
                                                                {{?it.DB[question] == option.condition.reference[qidx]}}
                                                                    {{cResult = true;}}
                                                                {{?}}
                                                            {{?}}
                                                        {{~}}
                                                        {{?option.condition.negate}}
                                                            {{cResult = !cResult;}}
                                                        {{?}}
                                                        {{?cResult}}
                                                            disabled {{?it.isCheck}}checked{{?}}
                                                        {{?}}
                                                    {{?}}
                                                {{?}}
                                            {{??}}
                                                {{?option.condition.questions.length == option.condition.reference.length}}
                                                    {{var cResult = true;}}
                                                    {{~option.condition.questions :question:qidx}}
                                                        {{?it.DB[question] != option.condition.reference[qidx]}}
                                                            {{cResult = false;}}
                                                        {{?}}
                                                    {{~}}
                                                    {{?option.condition.negate}}
                                                        {{cResult = !cResult;}}
                                                    {{?}}
                                                    {{?cResult}}
                                                        disabled {{?it.isCheck}}checked{{?}}
                                                    {{?}}
                                                {{??}}
                                                    {{var cResult = true;}}
                                                    {{~option.condition.questions :question:qidx}}
                                                        {{?it.DB[question] != option.condition.reference[0]}}
                                                            {{cResult = false;}}
                                                        {{?}}
                                                    {{~}}
                                                    {{?option.condition.negate}}
                                                        {{cResult = !cResult;}}
                                                    {{?}}
                                                    {{?cResult}}
                                                        disabled {{?it.isCheck}}checked{{?}}
                                                    {{?}}
                                                {{?}}
                                            {{?}}
                                        {{??}}
                                            {{?it.DB[option.condition] && 
                                                (option.condition_Val && (it.DB[option.condition] == option.condition_Val)) ||
                                                (option.condition_Greater && (it.DB[option.condition] > option.condition_Greater)) ||
                                                (option.condition_Less && (it.DB[option.condition] < option.condition_Less))}}
                                                disabled {{?it.isCheck}}checked{{?}}
                                            {{?}}
                                        {{?}}
                                    {{??}}
                                        disabled
                                    {{?}}
                                {{?}}"
                                {{?option.isCorrect}}data-correct="{{=option.isCorrect}}"{{?}}
                                {{?option.hidden}}
                                    {{?option.condition}}
                                        {{?option.condition.questions}}
                                            {{?option.condition.type == "OR"}}
                                                {{?option.condition.questions.length == option.condition.reference.length}}
                                                    {{var cResult = false;}}
                                                    {{~option.condition.questions :question:qidx}}
                                                        {{?option.condition.reference[qidx]}}
                                                            {{?it.DB[question] == option.condition.reference[qidx]}}
                                                                {{cResult = true;}}
                                                            {{?}}
                                                        {{?}}
                                                    {{~}}
                                                    {{?option.condition.negate}}
                                                        {{cResult = !cResult;}}
                                                    {{?}}
                                                    {{?cResult}}
                                                        hidden
                                                    {{?}}
                                                {{??}}
                                                    {{var cResult = false;}}
                                                    {{~option.condition.questions :question:qidx}}
                                                        {{?option.condition.reference[qidx]}}
                                                            {{?it.DB[question] == option.condition.reference[qidx]}}
                                                                {{cResult = true;}}
                                                            {{?}}
                                                        {{?}}
                                                    {{~}}
                                                    {{?option.condition.negate}}
                                                        {{cResult = !cResult;}}
                                                    {{?}}
                                                    {{?cResult}}
                                                        hidden
                                                    {{?}}
                                                {{?}}
                                            {{??}}
                                                {{?option.condition.questions.length == option.condition.reference.length}}
                                                    {{var cResult = true;}}
                                                    {{~option.condition.questions :question:qidx}}
                                                        {{?it.DB[question] != option.condition.reference[qidx]}}
                                                            {{cResult = false;}}
                                                        {{?}}
                                                    {{~}}
                                                    {{?option.condition.negate}}
                                                        {{cResult = !cResult;}}
                                                    {{?}}
                                                    {{?cResult}}
                                                        hidden
                                                    {{?}}
                                                {{??}}
                                                    {{var cResult = true;}}
                                                    {{~option.condition.questions :question:qidx}}
                                                        {{?it.DB[question] != option.condition.reference[0]}}
                                                            {{cResult = false;}}
                                                        {{?}}
                                                    {{~}}
                                                    {{?option.condition.negate}}
                                                        {{cResult = !cResult;}}
                                                    {{?}}
                                                    {{?cResult}}
                                                        hidden
                                                    {{?}}
                                                {{?}}
                                            {{?}}
                                        {{??}}
                                            {{?!it.DB[option.condition] || (it.DB[option.condition]==option.condition_Val)}}
                                                hidden
                                            {{?}}
                                        {{?}}
                                    {{??}}
                                        hidden
                                    {{?}}
                                {{?}}
                                >
                                <div class="card-panel hoverable">
                                    <label class="row">
                                        {{?option.label}}<span class="new badge question-label" data-badge-caption="{{=option.label}}"></span>{{?}}
                                        <input type="radio" class="{{=it.inputclass}}" name="{{=it.groupName}}"
                                            {{?it.DB[it.isFollower]>0}}
                                                disabled value="{{=option.value}}" data-correct="{{=option.isCorrect}}"
                                            {{??}}
                                                value="{{=option.value}}" {{?it.bind}}data-bind="{{=it.bind}}"{{?}} {{?it.binding}}data-binding="{{=it.binding}}"{{?}} data-correct="{{=option.isCorrect}}"
                                            {{?}}/>

                                        <!-- <span>{{=option.title}}</span> -->
 
                                        <span class="title {{?option.titleClass}}{{=option.titleClass}}{{?}}">
                                            
                                            {{?it.grid && option.image}}
                                            <div class="image col {{?it.grid.image}}{{=it.grid.image}}{{??}}s2{{?}}">
                                                <img class="responsive-img option-image" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=option.image}}"/>
                                            </div>
                                            <div class="option col {{?it.grid.option}}{{=it.grid.option}}{{??}}s10{{?}}">
                                            {{??}}
                                            <div class="col s12">
                                            {{?}}
                                                {{?option.icon}}
                                                    <i class="material-icons medium left">{{=option.icon}}</i>
                                                {{?}}
                                                {{?option.iconDesign}}
                                                    <i class="mdi medium left {{=option.iconDesign}}"></i>
                                                {{?}}
                                                {{=option.title}}
                                            </div>
                                        </span>


                                    </label>
                                    {{?it.isCheck && !it.hideIcons}}
                                    <i class="check-icon material-icons small right {{?option.isCorrect}}green-text{{??}}red-text{{?}} scale-transition scaled-out">
                                        {{?option.isCorrect}}check_circle{{??}}cancel{{?}}
                                    </i>
                                    {{?}}
                                </div>
                                
                                {{?option.moreInfo}}
                                    {{?option.condition && option.condition.blockDesc && cResult}}
                                        <ul class="collapsible blocked">   
                                            <li data-index="{{=idx+1}}">
                                                <div class="collapsible-header flow-text">
                                                    <i class="expand material-icons large bottom">expand_more</i>
                                                    <span class="strong">{{?it.descActive}}{{=it.lessInfo}}{{??}}{{=it.moreInfo}}{{?}}</span>
                                                </div>
                                                <div class="collapsible-body flow-text"><span>{{=option.moreInfo}}</span></div>
                                            </li>
                                        </ul>
                                    {{??}}
                                        <ul class="collapsible">   
                                            <li data-index="{{=idx+1}}" {{?it.descActive}}class="active"{{?}}>
                                                <div class="collapsible-header flow-text">
                                                    <i class="expand material-icons large bottom">{{?it.descActive}}expand_less{{??}}expand_more{{?}}</i>
                                                    <span class="strong">{{?it.descActive}}{{=it.lessInfo}}{{??}}{{=it.moreInfo}}{{?}}</span>
                                                </div>
                                                <div class="collapsible-body flow-text"><span>{{=option.moreInfo}}</span></div>
                                            </li>
                                        </ul>
                                    {{?}}
                                {{?}}

                            </li>  
                             
                        {{~}}
                        </ul>
                        {{?}}
                        
                    </form>
                </div>
            </div>

        </div>  
        
        
        {{?it.timer}}
        <div class="row timer {{?it.timer.aligned}}{{=it.timer.aligned}}{{??}}left{{?}}">

            <div class="card countDownTimerHolder client-colors-text text-font2">
                <i class="small material-icons left">av_timer</i>
                {{?it.timer.leadText}}
                    <span class="countDownleadText">{{=it.timer.leadText}}</span>
                {{?}}
                
                <span class="countDownTimer" data-name="{{?it.timer.timeUpText}}{{=it.timer.timeUpText}}{{??}}time is up!{{?}}" data-timeinms="{{=it.timeInMs}}">{{?it.time}}{{=it.time}}{{??}}{{=it.timer.timeUpText}}{{?}}</span>
            </div>
        </div>
        {{?}}

        {{?it.checkBtn}}
        <div class="row submit">
            <a id="checkBtn" hidden class="btn pulse client-colors button {{?it.checkBtn.animate}}animated {{=it.checkBtn.animate}}{{?}}">
                <i class="medium material-icons right">check_circle</i>{{=it.checkBtn.label}}
            </a>
        </div>
        {{?}}
        
        {{?it.resetBtn || it.resultsBtn}}
        <div class="row submit">
            {{?it.resetBtn}}
            <a id="resetBtn" hidden class="btn client-colors button2 {{?it.resetBtn.animate}}animated {{=it.resetBtn.animate}}{{?}}">
                <i class="medium material-icons right">{{?it.resetBtn.icon}}{{=it.resetBtn.icon}}{{??}}delete{{?}}</i>{{?it.resetBtn.label}}{{=it.resetBtn.label}}{{?}}
            </a>
            {{?}}
            {{?it.resultsBtn}}
            <a id="resultsBtn" hidden class="modal-trigger btn client-colors button2 {{?it.resultsBtn.animate}}animated {{=it.resultsBtn.animate}}{{?}}"
                href="#{{=it.resultsBtn.popupID}}" data-modal="{{=it.resultsBtn.popup}}"{>
                <i class="medium material-icons right">{{?it.resultsBtn.icon}}{{=it.resultsBtn.icon}}{{??}}pie_chart{{?}}</i>{{?it.resultsBtn.label}}{{=it.resultsBtn.label}}{{?}}
            </a>
            {{?}}
        </div>
        {{?}}


    </div>
</div>
    

<!-- If autoShow we need an outer modal component -->
{{?it.resultsBtn && !it.resultsBtn.outOfTabsLayout}}
<div id="{{=it.resultsBtn.popupID}}" class="modal large">
    <div class="modal-content"> </div>
    <div class="modal-footer">
        <a class="btn modal-close client-colors button2">
            <i class="small material-icons right">close</i>{{=it.resultsBtn.close}}
        </a>
    </div>
</div>
{{?}}