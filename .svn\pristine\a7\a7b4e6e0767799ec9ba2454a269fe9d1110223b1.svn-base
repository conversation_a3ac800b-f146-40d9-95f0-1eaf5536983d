@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

.wizlet.wizletHCLineChart {

    @include header-badge;

    .card {
        .card-content {
            padding: 6px 12px;
            .row {
                margin: 0;
            }
        }
    }

    .highcharts-container {
        
        &>svg {
            width: 100% !important;
        }

        .highcharts-background {
            fill: transparent;
        }

        .highcharts-title {
            font-size: 1.25rem;
        }
        .highcharts-subtitle {
            font-size: 1rem;
        }

        .highcharts-label,
        .highcharts-axis-labels {
            color: initial;
            fill: initial;

            &.highcharts-xaxis-labels {
                font-size: 125%;
            }
        }


        @for $i from 1 through 12 {
            .highcharts-color-#{$i - 1} {
                fill: color("client-colors", "chart#{$i}");
                stroke: color("client-colors", "chart#{$i}");
            }
        } 

        .highcharts-tooltip {
            table {
                tr, td {
                    padding: 0;
                }
            }
        }
        
    }



    .answers.card-panel {
        padding: 8px 12px;

        .btn-text {
            //display: inline-block;
            @include vertical-align-middle;
        }
    }
}