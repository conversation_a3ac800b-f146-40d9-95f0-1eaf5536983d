@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";


// .tab-component .wizlet.wizletSortable > .container,
// .tab-component .wizlet.wizletSortable_result > .container {
//     margin: 0;
//     width: initial;
// }

.wizlet.wizletSortable,
.wizlet.wizletSortable_result {
   
    @include header-badge;

    @include side-image;

    .side-items {
        padding-right: 0;
    }

    ul#sortable {
        margin: 40px 0 20px 0;
        $color: color("client-colors", "secondary");

        &.custom-colors {
            &.length-3 { @include degrade-li-items(3, $color); }
            &.length-4 { @include degrade-li-items(4, $color); }
            &.length-5 { @include degrade-li-items(5, $color); }
            &.length-6 { @include degrade-li-items(6, $color); }
            &.length-7 { @include degrade-li-items(7, $color); }
            &.length-8 { @include degrade-li-items(8, $color); }
            &.length-9 { @include degrade-li-items(9, $color); }       
            &.length-10 { @include degrade-li-items(10, $color); }      

        } 
    }

    
    .card-panel {
        padding: 5px 10px;
        
        &.sortable { 
            height: initial !important; 
            cursor: -webkit-grab;
            width: 98%;
            margin-left: auto;
            margin-right: auto;

            &.disabled {
                pointer-events: none;
                opacity: .8;
                filter: grayscale(100%);
            }
        }

        div.badge {
            margin-top: 5px;
        }
        div.text {
            margin: 5px 0;
            padding: 0 5px;
            span.text {
                @media #{$medium-and-down} { font-size: 90%; }
            }
        }
        
        &.answers {
            padding: 8px 12px;
    
            .btn-text {
                //display: inline-block;
                @include vertical-align-middle;
            }
        }
    }
    

    ul#sortable.isResult {
        li.sortable {
            &.scaled-out {
                transform: scale(0);
            }

            span.position {
                font-size: 100%;
                @media #{$medium-and-down} { 
                    font-size: 75%; 
                    min-width: 2.2rem;
                }
                line-height: 26px;
                height: 100%;
                background-color: color("client-colors", "button");
                
                &.left {
                    margin-left: 0;
                    padding: 0;
                }
            }
            
            i.check-icon {
                position: absolute;
                top: 5px;
                z-index: 1;

                &.left {
                    left: 0;
                }
                &.right {
                    right: -30px;
                }
                &.scaled-out {
                    transform: scale(0);
                }
                @media #{$small-and-down} { font-size: 3rem; }
            }
        }
    }
    
    @include row-submit;

}