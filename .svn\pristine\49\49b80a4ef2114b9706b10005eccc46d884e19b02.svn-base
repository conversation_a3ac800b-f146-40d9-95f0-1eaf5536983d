
@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


.wizlet.wizletRoles {
        
    .psn {

        >.instructions {
            margin-bottom: 20px;
        }

        &-container {
            overflow: hidden;
        }
        &-info {
            float: left;
            margin-top: -.5rem;
            &_Wrapper {
                @include flexbox();
                align-items: center;
                padding: 1rem;
            }                
            &_image {                    
                //border-radius: 50% !important;
                border-radius: 15px !important;
                //border: 3px solid color("client-colors", "border");
                // width: 300px; height: 300px;
                max-width: 300px;
                width: auto !important;
                height: auto !important;
            }

            
            .card-content {

                overflow: hidden;
                
                &.row {
                    margin: 0;
                }

                @include card-title; 
                p {
                    text-align: left;
                    &:not(.flow-text) {
                        font-size: inherit;
                    }
                }
                
                @media #{$medium-and-down} {
                    padding: 12px;
                }

                img.embed {
                    clear: both;
                    margin-bottom: 1rem;
                    
                    &.left {
                        float: left;
                        margin-right: 1.5rem;
                    }
                    &.right {
                        float: right;
                        margin-left: 1.5rem;
                    }
                    &.verytiny {
                        width: 100px; height: 100px;
                        @media #{$medium-and-down} { width: 75px; height: 75px;  }
                    }
                    &.tiny {
                        width: 150px; height: 150px;
                        @media #{$medium-and-down} { width: 100px; height: 100px;  }
                    }
                    &.small {
                        width: 200px; height: 200px;
                        @media #{$medium-and-down} { width: 150px; height: 150px;  }
                    }
                    &.medium {
                        width: 300px; height: 300px;
                        @media #{$medium-and-down} { width: 200px; height: 200px;  }
                    }
                    &.large {
                        width: 400px; height: 400px;
                        @media #{$medium-and-down} { width: 300px; height: 300px;  }
                    }
                    &.extralarge {
                        width: 500px; height: 500px;
                        @media #{$medium-and-down} { width: 400px; height: 400px;  }
                    }

                }
            }

            
            .card-image {
                display: block;
                min-width: 40%;
                max-width: 60%;
                
                @media #{$large-and-up} {
                    min-width: 30%;
                    max-width: 40%;
                }

                &.small {
                    width: 40%;                
                    @media #{$large-and-up} {
                        width: 30%;
                    }
                } 
                
                
                padding: 24px 0px;                
                &.right {
                    padding-right: 24px;
                }
                &.left {
                    padding-left: 24px;
                }
            }

            
        }
        &-thumbnails {
            @include flexbox();
            justify-content: center;
            align-items: center;
            flex-flow: wrap;
            text-align: center;
            margin-bottom: 1rem !important;
        }
        &-thumbnail {
            padding: 0 .5rem;
            &:after {
                content: '\f107';
                font-family: 'fa-solid-900';
                font-size: 12px;
                color: color("client-colors", "white");
                line-height: 17px;
                height: 15px;
                width: 15px;
                display: block;
                margin: -5px auto 0;
                border-radius: 50%;
                background-color: color("client-colors", "tertiary");
                box-shadow: 0px -2px 5px rgba(0, 0, 0, 0.7);
                opacity: 0;
            }
            &_name {
                display: none;
            }
            &_icon {
                margin: auto;
                width: 80px;
                height: 80px;
                border: 3px solid color("client-colors", "light-grey");
                border-radius: 50%;
                background-position: center;
                background-size: cover;
                background-repeat: no-repeat;
                cursor: pointer;
                box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5), -2px -2px 10px rgba(0, 0, 0, 0.4) inset, 2px 2px 15px white inset;
                -webkit-filter: saturate(0);
                filter: saturate(0);
            }
            &:hover .psn-thumbnail_icon {
                filter: none;
                border-color: color("client-colors", "tertiary");
            }
            &--highlight {
                @include vendor(transform, translateY(10px) scale(1.3));
                @include vendor(transition, all .5s ease);
                &:after {
                    opacity: 1;
                }
                .psn-thumbnail_icon {
                    border: 3px solid color("client-colors", "tertiary");
                    //box-shadow: none;
                    -webkit-filter: none;
                    filter: none;
                }
            }
        }         
    }


    @media #{$medium-and-down} {
        .psn {
            &-thumbnail {
                transform: none !important;
                padding: 0;
            }
        }
    }

    @media #{$small-and-down} {
        .psn {
            &-info {
                &_Wrapper {
                    padding: 0.5rem 1.3rem !important;
                    flex-flow: wrap;
                }
            }
        }
    }

}