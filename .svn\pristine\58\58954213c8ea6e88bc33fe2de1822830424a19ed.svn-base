@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


.sidenav-overlay {
    background-color: rgba(0,0,0,.75);
}


.wizlet.wizletHeader {
    background-color: color("client-colors", "navheader");
    >.divider {
        display: none;
    }

    .embeddingWrapper {
        display: none;
    }

    header {
        &.navbar-fixed {
            z-index: 999;

            &.ibiderEmbed {

                height: $navbar-height / 2;
                
                nav.navheader {

                    height: $navbar-height / 2;

                    @include box-shadow ( 0 0 5px #888 );

                    .nav-wrapper {
                        #logo-container, .participantName {
                            display: none !important;
                        }
                        
                        ul.right.hide-on-small-and-down {
                            display: block !important;
                        }
                        a.sidenav-trigger,
                        ul.left.hide-on-small-and-down,
                        #nav-mobile.sidenav {
                            display: none !important;
                        }
                    }

                }

            }
        }
        
        nav {
            $shadow: color("client-colors", "font");
            
            box-shadow: 0 2px 2px 0 rgba($shadow, 0.14),
                        0 3px 1px -2px rgba($shadow, 0.12),
                        0 1px 5px 0 rgba($shadow, 0.2);

            a {
                color: color("client-colors", "navcolor");
            }


            #logo-container img {
                width: auto;
                height: $navbar-height;//40px;
                
                padding: 5px 0;
                @media #{$small-and-up} {
                    padding-top: 5px;
                }
            }


            @mixin link-class {  
                li {
                                        
                    a {            
                        width: 100%;
                        color: color("client-colors", "font");

                        img {
                            width: 1.5rem;
                            margin-right: 5px;
                        }

                        &.disabled,
                        &[disabled]{                                
                            pointer-events: none;
                            background-color: $button-disabled-background;
                            opacity: .5;
                            filter: grayscale(100%);
                        }
                    }
                }
            }


            ul {

                img.avatar {
                    height: $navbar-height;
                    filter: drop-shadow(2px 4px 6px black);
                    padding: 5px 0 5px 10px;
                }

                .name {
                    font-size: inherit;
                    padding: 0 10px;
                }

                li {
                    margin: 0;

                    a {
                        display: inline-block;
                        @include truncate-line;
                        &:hover {
                            background-color: inherit;
                        }
                        >i.right {
                            margin-left: 5px;
                        }
                    }


                    .dropdown-content {
                        overflow: hidden;
                        border-bottom-left-radius: $card-panel-border;
                        border-bottom-right-radius: $card-panel-border;
                        @include link-class;
                          
                        // li:not(:last-child) {
                        //     border-bottom: 1px solid color("client-colors", "font");
                        // }       

                        a {            
                            padding: 20px 16px;
                        }
                    }
                }
            }

            
            ul.left, ul.right {
                height: 100%;
                > li {
                    height: 100%;
                }
            }

            .sidenav {
                max-width: 50vw;
                @include link-class;

                .user-view {
                    padding: 15px;
                    background-color: color("client-colors", "navheader2");
                    
                    img {
                        max-width: 150px;
                    }
                    .background img {
                        width: 100%;
                    }
                }
            }

        }


        .sidenav.panel {
            @media #{$small-and-down} { 
                display: none; 
            }
            @media #{$medium-and-up} {                 
                width: 10% !important;
                z-index: -1;
                padding: 2*$navbar-height 1% $navbar-height 1%;
                &.animated {
                    animation-delay: 1s;
                }

                li {
                    padding: 20px 0;
                    
                    a{
                        padding: 0;
                        text-align: center;
                        img {
                            height: 48px;
                            max-width: 100%;
                            object-fit: contain;
                        }
                        figcaption {
                            @include truncate-line;
                            line-height: 15px;
                            margin-top: -15px;
                            font-size: 12px;
                            font-weight: bolder;
                            color: color("client-colors", "font");
                        }

                        &:hover {
                            transform: scale(1.1);
                        }

                        &.on {
                            filter: drop-shadow(2px 4px 4px color("client-colors", "dark-grey"));
                            img.off {
                                display: none;
                            }
                        }

                        &.off {
                            opacity: 0.75;
                            img.on {
                                display: none;
                            }
                        }
                    }
                }
            }
        }

    }
    
}