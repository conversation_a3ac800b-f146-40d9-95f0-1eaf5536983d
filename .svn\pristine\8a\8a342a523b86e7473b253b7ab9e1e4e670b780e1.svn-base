<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro">
  <Include name="Header_Intro"></Include>
  
  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      header: "!{R1_Debrief_Ranking_Header}",
      subheader: "!{R1_Debrief_Ranking_Title}",
      instructions: "!{R1_Debrief_Ranking_Text}"
    }]]>  
  </Component>
  <Include name="SIM_Ranking_included"></Include>

  

  <Component type="Confetti" customJS="true"><![CDATA[{
    templateInEvent: "html/confetti.dot",
    css: "styles/confetti.css",
    pieces: 200,
    hideAfter: 5000
  }]]></Component>
  

  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>
  

</Action>