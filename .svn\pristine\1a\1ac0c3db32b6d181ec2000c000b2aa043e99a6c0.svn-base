<?xml version="1.0" encoding="utf-8" ?>
<!-- <Action mainAreaLayout="../../../layout/mainLayoutIntro" layout="../../../layout/tabsLayoutDebrief"> -->
<Action mainAreaLayout="../../../layout/mainLayoutIntro" layout="../../../layout/layoutDebrief">
  <Include name="Header_Intro"></Include>
  
  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{R1_Debrief_Scenario1c_Header}",
      subheader: "!{R1_Debrief_Scenario1c_Text}",
      valign: false,
      animate: "fadeIn",
      content: {
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{}"
      }      
    }]]>
  </Component>

  
  <Include name="R1_Debrief_Scenario1c_chart"></Include>
  <Include name="R1_Debrief_Scenario1c_table"></Include>

  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1c",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1c_FB_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1c_FB_Opt1_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario1c_FB_Opt1_Title}",
        body: "!{SIM_R1_Scenario1c_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario1c_Opt1_LTUs}","!{SIM_R1_Scenario1c_Opt1_KPI1}","!{SIM_R1_Scenario1c_Opt1_KPI2}","!{SIM_R1_Scenario1c_Opt1_KPI3}","!{SIM_R1_Scenario1c_Opt1_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1c",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1c"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1c",
        condition_Diff: "2",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1c_FB_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1c_FB_Opt2_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario1c_FB_Opt2_Title}",
        body: "!{SIM_R1_Scenario1c_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario1c_Opt2_LTUs}","!{SIM_R1_Scenario1c_Opt2_KPI1}","!{SIM_R1_Scenario1c_Opt2_KPI2}","!{SIM_R1_Scenario1c_Opt2_KPI3}","!{SIM_R1_Scenario1c_Opt2_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1c",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1c"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1c",
        condition_Diff: "3",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1c_FB_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1c_FB_Opt3_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario1c_FB_Opt3_Title}",
        body: "!{SIM_R1_Scenario1c_FB_Opt3_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario1c_Opt3_LTUs}","!{SIM_R1_Scenario1c_Opt3_KPI1}","!{SIM_R1_Scenario1c_Opt3_KPI2}","!{SIM_R1_Scenario1c_Opt3_KPI3}","!{SIM_R1_Scenario1c_Opt3_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1c",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1c"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1c",
        condition_Diff: "4",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1c_FB_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario1c_FB_Opt4_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario1c_FB_Opt4_Title}",
        body: "!{SIM_R1_Scenario1c_FB_Opt4_Text}"
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1c"]
    }]]>
  </Component>


  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>
  

</Action>