
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT'], function ($, Q, WizerApi, WizletBase, doT) {
    
    var MyCode = function () {
        this.type = 'MyCode';
        this.level = 1;
    };

    MyCode.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    MyCode.prototype.unloadHandler = function () {
        WizletBase.unloadHandler({ wizlet: this });
    };

    
    MyCode.prototype.render = function (options) {
        
        var self = this;
        
        return self.templateDefer.promise.then(function (template) {
  
            var copiedVotes = [];
        
            $.each(self.wizletInfo.eventVotes, function (key, vote) {   
                copiedVotes.push({
                    questionId:   self.wizerApi.getQuestionIdByName(vote.to), 
                    responseText: self.wizletInfo.DB[self.wizletInfo.eventName+'.'+vote.from]                
                })
            });   

            self.wizerApi.addVotes({votes:copiedVotes}).then(function (result) {        
                console.log('*votes across events included*');
            });    

        })
        .fail(this.wizerApi.showError)
    };

    

    MyCode.getRegistration = function () {
        return new MyCode();
    };

    return MyCode;

});
