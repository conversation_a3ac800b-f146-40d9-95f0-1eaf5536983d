<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    position: "",
    header: "!{Logins_Table_InfoTitle}",
    userHeader: "!{Logins_Table_User}",
    me: "",
    boundName: "",
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    isDataTables: false,
    class: "_verticalMode_ _myresponsive-table_ noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{Logins_Table_Name}",
        binding: "Q_LOGIN_DATE_R1",
        timestamp: true
      }
    ],
    totalRecords: "!{Logins_Table_Records}",
    trackQuestion: "Show_charts",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:1000,
    showNear: 5,
    liveUpdate: true,
    markUpdate: false
  }]]></Component>


</Action>