﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var ImageTooltip = function () {
        this.type = 'ImageTooltip';
        this.level = 1;
    };

    ImageTooltip.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.votesBeforeUpdate = [];

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    ImageTooltip.prototype.unloadHandler = function () {
        $('.material-tooltip').remove();;
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };

    ImageTooltip.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
                     
            //Init Materialize Modal
            options.context.find('.modal').modal();

            //Launch one by one
            var timeout = options.wizletInfo.timeout ? parseInt(options.wizletInfo.timeout) : 1000;
            $.each(options.context.find('.tooltipped.timage'), function (idx, tooltip) {
                
                setTimeout(function(){ $(tooltip).removeAttr('hidden') }, (idx+1)*timeout);
                $(tooltip).tooltip( {
                    enterDelay: 100, html: "timage"
                });
                
            });

            //Give the specific class (to difference from another tooltips) to the material-tooltip created elements
            $('.tooltip-content').filter(function() {return $(this).text() == "timage";}).parent().addClass('timage');

              
            //Add position to the tooltip box to handle in the CSS
            $.each($('.material-tooltip.timage:not(.positioned)'), function (idx, tooltip) {
                $(tooltip).addClass( "positioned " + options.wizletInfo.tooltips[idx].position );  
            });


            //Load a new page(an action XML) into a modal
            options.context.find("[data-modal]").off("click").on("click", function (e) {
                self.loadPage(
                    $(this).data('modal'), 
                    options.context, 
                    self.unsedContext, 
                    $( $( $(this).attr('href')).find('.modal-content') ) 
                );
            });

            return true;
        })
        .fail(this.wizerApi.showError);
    };


    /**
     * Load an action screen inside a modal window
     */
    ImageTooltip.prototype.loadPage = function (actionXMLName, context, unusedContext, modalContainer) {
        var self = this;

        //unload previous 
        if (self.currentWizletModule && self.currentWizletModule.length > 0) {
            $.each(self.currentWizletModule, function (index, module) {
                if (module.wizletInstance.unloadHandler) {
                    module.wizletInstance.unloadHandler();
                }
            });
        }
        
        $('.material-tooltip:not(.timage)').remove();;
        
        var loading = self.wizerApi.loadActionInContainer(actionXMLName, context, unusedContext, modalContainer);
        loading.then(function (loads) {
           
            self.currentWizletModule = loads;
            
            var page = "wizer:action:init.mainArea";
                        
            $(document).trigger(page, actionXMLName);
        });
        
    };
    
    ImageTooltip.getRegistration = function () {
        return new ImageTooltip();
    };

    return ImageTooltip;

});