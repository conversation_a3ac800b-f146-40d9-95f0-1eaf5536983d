<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC">
  <Include name="HeaderFAC"></Include>
      

  <Component type="ChatBox" customJS="true">
    <![CDATA[{
      templateInEvent: "html/chatBox.dot",
      css: "styles/chatBox.css",
      animate: "fadeInRight",
      size: "large",
      onlyResults: true,
      valign: false,
      header: "!{Extra_OpenQuestion_Input_Header}",
      subheader: "!{}",
      instructions: "!{InputInstructionsAnswers}",
      placeHolder: "!{}",
      _remaining: {},
      me: "!{Me}",
      boundName: "Q_My_Name",
      boundAvatar: "Q_My_Avatar",
      defaultAvatar: "!{defaultAvatar}",
      binding: "Q_Extra_OpenQuestion2_Input",
      showAuthor: true,
      reverse: true,
      restrictions: [ "allVotes" ],
      maxLists: 100
    }]]>
  </Component>



  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "Extra_OpenQuestion1_Input",
          targetSection: "Extra_OpenQuestion1_Answers_FAC",
          label: "!{GD_Extra_OpenQuestion1_Input}",
          icon: "chat"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "Extra_OpenQuestion3_Input",
          targetSection: "Extra_OpenQuestion3_Answers_FAC",
          label: "!{GD_Extra_OpenQuestion3_Input}",
          icon: "chat"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>

</Action>