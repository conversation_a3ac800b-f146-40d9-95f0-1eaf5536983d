
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT'], function ($, Q, WizerApi, WizletBase, doT) {
    
    var MyCode = function () {
        this.type = 'MyCode';
        this.level = 1;
    };

    MyCode.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    MyCode.prototype.unloadHandler = function () {
        WizletBase.unloadHandler({ wizlet: this });
    };

    
    MyCode.prototype.render = function (options) {
        
        var self = this;
        
        return self.templateDefer.promise.then(function (template) {
            
            var idsQuestions = self.wizletInfo.ids
            var numQuestions = idsQuestions.length;
            
            self.loadJSONQuestions(self.wizletInfo.json).then( function (json) {

                var questionsList = [];
                
                //List of questions in sequential order
                for (let idx = 0; idx < json.total; idx++) {
                    questionsList[idx] = idx+1;                    
                }
                
                //Shuffle the list
                if (self.wizletInfo.randomQuestions)
                    questionsList = self.shuffleList(questionsList);                    
                
                //Trim the list to the number of questions components defined in the XML
                questionsList = questionsList.slice(0,numQuestions)
                

                //Populate each component question with the assigned Json question
                $(document).one('wizer:action:init', function(e, currentAction) { 
                    for (let idx = 0; idx < numQuestions; idx++) {
                        console.log(idsQuestions[idx], questionsList[idx])
                        self.populateQuestion( idsQuestions[idx], json[ questionsList[idx] ]);
                    }
                });

            });


        })
        .fail(this.wizerApi.showError)
    };

    

    MyCode.prototype.populateQuestion = function(questionId, json) {
        
        var self = this;

        var $question = $('#'+questionId);

        if ($question[0] && json) {

            //Question Title
            $question.find('.card-content > .header.title > span.text').html( json.title )
            
            //Question Options
            var checkCorrect = self.wizletInfo.checkCorrect ? self.wizletInfo.correctIcon : false;
            var $options = $question.find('.card-content form > ul > li')
            $.each($options, function (idx, option) {
                if (json.options[idx]) {
                    $(option).find('label span.title').html( json.options[idx] )
                    //Set check correct icon
                    if (checkCorrect && json.correct && (json.correct == (idx+1))) {
                        $(option).find('label input').attr('data-correct',true);
                        $(option).find("i.check-icon").html(checkCorrect).removeClass('red-text').addClass('green-text')
                    }
                } else {
                    $(option).remove();
                }
            });
        }

    }


    MyCode.prototype.shuffleList = function(array) {

        let currentIndex = array.length,  randomIndex;
      
        // While there remain elements to shuffle...
        while (currentIndex != 0) {
      
            // Pick a remaining element...
            randomIndex = Math.floor(Math.random() * currentIndex);
            currentIndex--;
        
            // And swap it with the current element.
            [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
        }
      
        return array;
    }

    
    MyCode.prototype.loadJSONQuestions = function (url) {
        
        var self = this;

        var loading = new Q.defer();
    
        var xobj = new XMLHttpRequest();
        xobj.overrideMimeType("application/json");
        xobj.open('GET', 
                '/Wizer/Pages/Events/'+self.wizerApi.eventName()+url+'?nocache=' + (new Date()).getTime(), 
                true); 
        xobj.onreadystatechange = function () {
            if (xobj.readyState == 4 && xobj.status == "200") {
                loading.resolve( JSON.parse( xobj.responseText ) );
            }
        };
        xobj.send(null);

        return loading.promise;
    };


    MyCode.getRegistration = function () {
        return new MyCode();
    };


    return MyCode;

});
