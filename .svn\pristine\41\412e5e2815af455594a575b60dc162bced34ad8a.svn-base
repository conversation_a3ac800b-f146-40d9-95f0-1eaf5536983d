<?xml version="1.0" encoding="utf-8" ?>
<Action>
    
  <Component type="Header" wizletName="Header_SIM_R1" customJS="true"><![CDATA[{
    templateInEvent: "html/header.dot",
    css: "styles/header.css",
    logo:   {
        src: "!{<PERSON><PERSON>_logo}",
        alt: "logo"
    },
    user: {
      background: "",
      avatar: "!{Header_logo_menu}",
      my_avatar: "Q_My_Avatar"
    },
    
    myName: "Q_My_Name",
    myTeam: "",
      
    follower_suffix: " - !{Header_Follower}",
    trackTeam: "Team",
    isFollower: "Follower",

    scope: ["Q_My_Name","Q_My_Avatar","Follower"],

    _links: [
      {
        section: true,
        sectionID: "dropdownInfo",
        sectionTitle: "!{Header_LinkSection_Control}",
        sectionLast: false,
        id: "link_individual",
        title: "!{Header_Link_ControlStart}",
        icon: "settings_backup_restore",
        popup: "SIM_R1_Start_menu",
        popupID: "modal-individual"
      },
      {
        section: true,
        sectionLast: true,
        id: "link_foreman",
        title: "!{Header_Link_ControlFinish}",
        icon: "redo",
        popup: "SIM_R1_Finish_BackDirector_menu",
        popupID: "modal-foreman"
      }
    ],
    links: [
      {
        id: "link_case",
        title: "!{Header_LinkCase}",
        icon: "menu_book",
        popup: "SIM_CaseStudy_menu",
        popupID: "modal-case"
      },
        {
          divider: true
        },
      {
        id: "link_roles",
        title: "!{Header_LinkRoles}",
        icon: "people_alt",
        popup: "SIM_R1_Roles_menu",
        popupID: "modal-roles"
      },
        {
          divider: true
        },
      {
        id: "link_exit",
        title: "!{Header_LinkExit}",
        icon: "exit_to_app",
        modalID: "modal-logout"
      }
    ],
    
    close: "!{Header_LinkClose}",
    _sectionsID: [ "modal-individual", "modal-foreman", "modal-case" ],
    sectionsID: [ "modal-case", "modal-roles" ],

    logout: {
      modalID: "modal-logout",
      header: "!{Header_Modal_Logout_Title}",
      text: "!{Header_Modal_Logout_Text}",
      close: "!{Header_Modal_Logout_Close}",
      logout: "!{Header_Modal_Logout_Logout}",
      onclick: "logout()"
    }    
    
  }]]></Component>


  <Component type="Vanilla" wizletName="Footer"><![CDATA[{
    templateInEvent: "html/footer.dot",
    css: "styles/footer.css",
    footer: {
      color: "navheader",
      _banner: "!{Footer_Img}",
      _logo: {src:"", alt:""},
      title: "!{Footer_Title}",
      subtitle: "!{Footer_Subtitle}",
      copyright: "", link: ""
    }
  }]]></Component>

</Action>