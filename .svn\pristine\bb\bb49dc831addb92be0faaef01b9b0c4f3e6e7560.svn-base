<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro_SIM" layout="../../../layout/grid_CaseStudy2">
  <Include name="Header_Intro"></Include>

  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R3_Roles_Header}",
      swipeable: false,
      tabPanelID: "tabPanelCase",
      tabs: [
          "!{SIM_R3_Roles_Tab2}"
      ]
  }]]></Component>

  <!-- <Component type="cardV2" customJS="true"> 
    <![CDATA[{
      templateInEvent: "html/cardV2.dot",
      css: "styles/cardV2.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      transparentBox: false,
      overlay: {
        path: "images/avatar/",
        img: "",
        var: "Q_My_Avatar",
        show: true,
        width: "9.1%",
        height: "",
        offsetX: "55.72%",
        offsetY: "17.55%"
      },
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R3_Roles_Img}",  alt: "!{SIM_R3_Roles_Header}" ,
          position: "left large",
          src_vert: "!{}",
          animate: "fadeInLeft animate__delay-1s", _animateLater: "bounce"
        },
        img: { 
          materialboxed: false, _borders: "top right left bottom", frame: "", nopadding: false,
          src: "!{SIM_R3_Roles_Img}",  alt: "!{SIM_R3_Roles_Header}",
          isHiddenWhenSmall: false, 
          src_vert: "!{}",
          animate: "fadeInUp"
        },
        position: "down",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{}"
      },
      scope: ["Q_My_Avatar"]
    }]]>  
  </Component> -->

    <!-- Next Tab Button  -->
    <!-- <Component type="ActionButton" customJS="true">
      <![CDATA[{
        templateInEvent: "html/actionButton.dot",
        css: "styles/actionButton.css",
        animate: "zoomIn",
        id: "btn_action1", isHidden: false, 
        title: "!{SIM_R3_Roles_Tab2}",
        icon: "redo",
        _onclick: "$(this).hide(); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanelCase1&quot;')[0].click()",
        onclick: "$(this).removeClass('pulse'); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanelCase1&quot;')[0].click()",
        pulse: false,
        color: "",
        
        _isFollower: "Follower",
        scope: [""]
      }]]>
    </Component> -->


  <!-- Your Team  -->    
  <Component type="Roles" customJS="true"><![CDATA[{
    templateInEvent: "html/roles.dot",
    css: "styles/roles.css",
    _animate: "fadeIn",
    header: "!{} ",
    subheader: "!{}",
    instructions: "!{SIM_R1_Roles_Instructions}",
    intro: "!{}",
    showWhenAllVisited: "#navButton",
    hideWhenAllVisited: "#btn_action2",
    characters: [
      {
        var: "Q_My_Avatar",
        path: "images/roles/",
        name: "!{SIM_R1_Roles_Title0}"
      },
      {
        image: "!{SIM_R1_Roles_Img1}",
        name: "!{SIM_R1_Roles_Title1}"
      },
      {
        image: "!{SIM_R1_Roles_Img2}",
        name: "!{SIM_R1_Roles_Title2}"
      },
      {
        image: "!{SIM_R1_Roles_Img3}",
        name: "!{SIM_R1_Roles_Title3}"
      },
      {
        image: "!{SIM_R1_Roles_Img4}",
        name: "!{SIM_R1_Roles_Title4}"
      },
      {
        image: "!{SIM_R1_Roles_Img5}",
        name: "!{SIM_R1_Roles_Title5}"
      }
    ],
    "charactersInfo": [
      {
        "title": "!{SIM_R1_Roles_Title0}",
        "label": "!{}",
        "text": "!{SIM_R1_Roles_Text0}",
        "image": "",
        "var": {
          path: "images/roles/",
          src: "Q_My_Avatar",
          position: "left",
          size: "medium",
          embed:false
        }
      },
      {
        "title": "!{SIM_R1_Roles_Title1}",
        "text": "!{SIM_R1_Roles_Text1}",
        "image": {
          src: "!{SIM_R1_Roles_Img1}",
          position: "left",
          size: "medium",
          embed:false
        }
      },
      {
        "title": "!{SIM_R1_Roles_Title2}",
        "text": "!{SIM_R1_Roles_Text2}",
        "image": {
          src: "!{SIM_R1_Roles_Img2}",
          position: "left",
          size: "medium",
          embed:false
        }
      },
      {
        "title": "!{SIM_R1_Roles_Title3}",
        "text": "!{SIM_R1_Roles_Text3}",
        "image": {
          src: "!{SIM_R1_Roles_Img3}",
          position: "left",
          size: "medium",
          embed:false
        }
      },
      {
        "title": "!{SIM_R1_Roles_Title4}",
        "text": "!{SIM_R1_Roles_Text4}",
        "image": {
          src: "!{SIM_R1_Roles_Img4}",
          position: "left",
          size: "medium",
          embed:false
        }
      },
      {
        "title": "!{SIM_R1_Roles_Title5}",
        "text": "!{SIM_R1_Roles_Text5}",
        "image": {
          src: "!{SIM_R1_Roles_Img5}",
          position: "left",
          size: "medium",
          embed:false
        }
      }
    ],
    "isFollower": "Follower",
    "trackTeam": "Team",
    "scope": ["Q_My_Avatar", "Follower"]
  }]]></Component>

<!-- Next Tab Button  -->
    <Component type="ActionButton" customJS="true">
      <![CDATA[{
        templateInEvent: "html/actionButton.dot",
        css: "styles/actionButton.css",
        animate: "zoomIn",
        id: "btn_action2", isHidden: false, 
        title: "!{Navigation_next_profile}",
        icon: "forward",
        _onclick: "$(this).hide(); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanelCase1&quot;')[0].click()",
        _onclick: "$(this).removeClass('pulse'); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanelCase1&quot;')[0].click()",
        onClickCustomFunction: "viewNextRole",
        pulse: false,
        color: "",
        _isFollower: "Follower",
        scope: [""]
      }]]>
    </Component> 

  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft _animate__delay-2s",
      id: "navButton",
      isHidden: true, _showDelay: "3000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>

  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>

  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R3_Roles}"/> 
  </Voting>


</Action>