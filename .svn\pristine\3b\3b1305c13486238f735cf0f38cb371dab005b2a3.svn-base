<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC_R1" layout="../../../layout/tabsLayoutX2">
  <Include name="HeaderFAC_R1"></Include>


  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{R1_DebriefPage_FAC_Header}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{R1_DebriefPage_FAC_tab0}",
          "!{R1_DebriefPage_FAC_tab1}",
          "!{R1_DebriefPage_FAC_tab2}",
          "!{R1_DebriefPage_FAC_tab3}",
          "!{R1_DebriefPage_FAC_tab4}",
          "!{R1_DebriefPage_FAC_ranking}"
      ],
      scope: null
  }]]></Component>



  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ _myresponsive-table_ verticalMode _subheaders_ noBold _fixed_",
    headerMultiLines: false,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{Badge_Opt1}-!{SIM_Initiatives_Opt1}",
        binding: "Q_SIM_R1_Initiatives_1"
      },
      {
        show: true,
        title: "!{Badge_Opt2}-!{SIM_Initiatives_Opt2}",
        binding: "Q_SIM_R1_Initiatives_2"
      },
      {
        show: true,
        title: "!{Badge_Opt3}-!{SIM_Initiatives_Opt3}",
        binding: "Q_SIM_R1_Initiatives_3"
      },
      {
        show: true,
        title: "!{Badge_Opt4}-!{SIM_Initiatives_Opt4}",
        binding: "Q_SIM_R1_Initiatives_4"
      },
      {
        show: true,
        title: "!{Badge_Opt5}-!{SIM_Initiatives_Opt5}",
        binding: "Q_SIM_R1_Initiatives_5"
      },
      {
        show: true,
        title: "!{Badge_Opt6}-!{SIM_Initiatives_Opt6}",
        binding: "Q_SIM_R1_Initiatives_6"
      },
      {
        show: true,
        title: "!{Badge_Opt7}-!{SIM_Initiatives_Opt7}",
        binding: "Q_SIM_R1_Initiatives_7"
      },
      {
        show: true,
        title: "!{Badge_Opt8}-!{SIM_Initiatives_Opt8}",
        binding: "Q_SIM_R1_Initiatives_8"
      },
      {
        show: true,
        title: "!{Badge_Opt9}-!{SIM_Initiatives_Opt9}",
        binding: "Q_SIM_R1_Initiatives_9"
      },
      {
        show: true,
        title: "!{Badge_Opt10}-!{SIM_Initiatives_Opt10}",
        binding: "Q_SIM_R1_Initiatives_10"
      },
      {
        show: true,
        title: "!{Badge_Opt11}-!{SIM_Initiatives_Opt11}",
        binding: "Q_SIM_R1_Initiatives_11"
      },
      {
        show: true,
        title: "!{Badge_Opt12}-!{SIM_Initiatives_Opt12}",
        binding: "Q_SIM_R1_Initiatives_12"
      },
      {
        show: true,
        title: "!{Badge_Opt13}-!{SIM_Initiatives_Opt13}",
        binding: "Q_SIM_R1_Initiatives_13"
      }
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{}",
      subheader: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{}"
      }      
    }]]>
  </Component>

  <!-- ******* -->
  <!-- EVENT 1 -->
  <!-- ******* -->
  <Include name="R1_Debrief_Scenario1_chart"></Include>
  <Include name="R1_Debrief_Scenario1_results"></Include>

  <!-- ******* -->
  <!-- EVENT 2 -->
  <!-- ******* -->
  <Component type="Prioritize" customJS="true"><![CDATA[{
    css: "styles/prioritize.css",
    "_header": {
      "title": "",
      "description": ""
    },
    "instructions": "!{}",
    "type": "QUESTIONS",
    "readOnlyCondition": true,
    "version": "2.0",
    "questions": [
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt1}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt2}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt3}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt4}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt5}"
      },
      {
        "_binding": "",
        "title": "!{SIM_R1_Scenario2_Opt6}"
      }
    ],
    "buttons": [
      {
        "navigation": {
          "title": "!{Navigation_submit}",
          "history": false,
          "message": "!{InputSubmited}",
          "idToShow": "navButton"
        }
      },
      {
        "navigation": {
          "title": "!{Navigation_submitModalClose}",
          "history": false
        }
      }
    ],
    "scope": []
  }]]></Component>
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{}",
      subheader: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{}"
      }      
    }]]>
  </Component>

  <!-- ******* -->
  <!-- EVENT 3 -->
  <!-- ******* -->
  <Include name="R1_Debrief_Scenario3_chart"></Include>
  <Include name="R1_Debrief_Scenario3_results"></Include>

  <!-- ******* -->
  <!-- EVENT 4 -->
  <!-- ******* -->
  <Include name="R1_Debrief_Scenario4_chart"></Include>
  <Include name="R1_Debrief_Scenario4_results"></Include>





  <!-- ******* -->
  <!-- RANKING -->
  <!-- ******* -->

  <Include name="SIM_Ranking_included_R1"></Include>





  <!-- EXPORT CSV BUTTON (adding empty component to get the Export button out of the tabs layout which goes in pairs)-->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css"    
    }]]>
  </Component>
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "bounceInUp",
      id: "btn_export",
      isHidden: false, _showDelay: 3000,
      title: "!{GroupDirector_ExportBtn_R1}",
      icon: "cloud_download",
      onclick: "",
      pulse: true,
      color: "aux1",
      modal: {
        modalID: "modal-export",
        header: "!{GroupDirector_Export_Modal_Title}",
        text: "!{GroupDirector_Export_Modal_Text}",
        close: "!{GroupDirector_Export_Modal_Close}",
        action: "!{GroupDirector_Export_Modal_Action}",
        onclick: "",
        onclickFunction: "dataExport",
        onclickQuestion: "GD",
        onclickQuestion2: "Data Report SIM-R1"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



  <Include name="ScrollToButton"></Include>



  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - showDelay: show the hidden button after Xms (waiting the animation)
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "center",
      animate: "zoomIn animate__delay-2s",
      id: "btn_navigation_home",
      _isHidden: true, _showDelay: 1000,
      hasModals: true,
      _buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "R2_LandingPage",
          targetSection: "R2_LandingPage_FAC",
          label: "!{GD_R2_LandingPage}",
          icon: "screen_share"
        }
      ],
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R2_LandingPage",
          targetSection: "R2_LandingPage_FAC",
          label: "!{GroupDirector_Tab3}",
          icon: "screen_share"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>




</Action>