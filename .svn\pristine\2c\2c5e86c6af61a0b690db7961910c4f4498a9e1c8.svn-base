@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


// Shared

.wizlet.wizletCollapsible {

    .card {

        .card-content {
            @include card-title; 
        }
        
        .collapsible {

            
            border-radius: $card-panel-border;

            li {
                border-radius: $card-panel-border;

                margin: 0;
                padding: 0;
                background-color: color("client-colors", "white") ;

                .collapsible-header {
                    border-radius: $card-panel-border;
                    $primary-color: color("client-colors", "button");
                    @include linear-gradient2(to right, $primary-color, lighten($primary-color, 20%), 100%);
                    // background-color: color("client-colors", "button") ;
                    color: color("client-colors", "font2") ;
                }
                .collapsible-body {
                    border-radius: $card-panel-border;
                    padding: 1rem;
                    >span {
                        font-size: 90%;
                    }
                }
            }
            margin-bottom: 3rem;
        }
        
    }
}