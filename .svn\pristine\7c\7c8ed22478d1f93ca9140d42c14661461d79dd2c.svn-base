{{?!it.noanimated}}
<span id="splash-overlay"></span>
<span id="splash-ball" class="z-depth-4"></span>
{{?}}

<main class="landingPage {{?it.facilitator}}facilitator{{?}}{{?it.facilitator2}} facilitator2{{?}}">
    {{?!it.fullScreen}}
    <div class="section no-pad-bot">
        <div class="container">
    {{?}}
            <div class="center">
                {{?it.content.title || it.content.subtitle}}
                <div class="title-box {{?it.content.withBubbles}}with-bubbles{{?}}">
                    {{?it.content.title}}<h1 class="header title">{{=it.content.title}}</h1>{{?}}
                    {{?it.content.subtitle}}<h3 class="header subtitle light">{{=it.content.subtitle}}</h3>{{?}}
                </div>
                {{?}}
                {{?it.content.image}}
                <div class="image {{?it.content.images && it.content.images.length>1}}carousel{{?}}">
                    {{?it.content.images && it.content.images.length>1}}
                        <!-- {{~it.content.images :img:idx}}
                            <img class="top" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=img}}">
                        {{~}} -->
                        {{for (var idx = it.content.images.length-1; idx >= 0; idx--) {}}
                            <img class="top {{?idx==0 && it.content.firstIsLogo}}firstIsLogo{{?}}" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.images[idx]}}">                    
                        {{};}}      
                    {{??}}
                    <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.image}}">
                    {{?}}
                </div>
                {{?}}
            </div>
            {{?it.content.startBtn && it.content.startBtn.show}}
            <div class="row center">
                <a id="start-button" 
                   class="hide-on-small-only {{?it.content.startBtn.isLarge}}btn-large{{??}}btn{{?}} client-colors button" 
                   {{?it.content.startBtn.onclick}}onclick="{{=it.content.startBtn.onclick}}"{{?}}>
                   {{?it.content.startBtn.icon}}<i class="medium material-icons right">{{=it.content.startBtn.icon}}</i>{{?}}
                   {{=it.content.startBtn.label}}
                </a>
            </div>
            {{?}}

    {{?!it.fullScreen}}
        </div>
    </div>
    {{?}}

</main>
