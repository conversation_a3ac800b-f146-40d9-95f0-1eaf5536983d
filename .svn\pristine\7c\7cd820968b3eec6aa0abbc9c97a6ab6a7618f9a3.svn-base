<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC_R2" layout="../../../layout/tabsLayout">
  <Include name="HeaderFAC_R2"></Include>

  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R2_FAC_dashboard_Header}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{SIM_R2_FAC_dashboard_teams}",
          "!{SIM_R2_FAC_dashboard_tab2}",
          "!{SIM_R2_FAC_dashboard_tab1}",
          "!{SIM_R2_FAC_dashboard_ranking}"
      ],
      scope: null
  }]]></Component>



  <!-- ************ -->
  <!-- PROGRESS TAB -->
  <!-- ************ -->
  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    instructions: "!{}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: true,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ _subheaders_ noBold fixed",
    headerMultiLines: false,
    headerEllipsis: false,
    questions: [
      {
        show: true,
        title: "!{Logins_Table_Login}",
        binding: "Q_LOGIN_DATE_R2",
        timestamp: true
      },
      {
        show: true,
        title: "!{Logins_Table_Logout}",
        binding: "Q_FINISH_DATE_R2",
        timestamp: true
      },
      {
        show: true,
        title: "!{Logins_Table_Screen}",
        binding: "Q_CurrentScreen"
      }
    ],
    _answervalidation: true,
    _subheaders: [
      "",
      "!{}"
    ],
    trackQuestion: "Show_charts",
    rankQuestion: "",
    sortByQuestion: "Team",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: false
  }]]></Component>


  <!-- ****************** -->
  <!-- TAB 1: INITIATIVES -->
  <!-- ****************** -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ _myresponsive-table_ verticalMode _subheaders_ noBold _fixed_",
    headerMultiLines: false,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{Badge_Opt1}-!{SIM_Initiatives_Opt1}",
        binding: "Q_SIM_R2_Initiatives_1"
      },
      {
        show: true,
        title: "!{Badge_Opt2}-!{SIM_Initiatives_Opt2}",
        binding: "Q_SIM_R2_Initiatives_2"
      },
      {
        show: true,
        title: "!{Badge_Opt3}-!{SIM_Initiatives_Opt3}",
        binding: "Q_SIM_R2_Initiatives_3"
      },
      {
        show: true,
        title: "!{Badge_Opt4}-!{SIM_Initiatives_Opt4}",
        binding: "Q_SIM_R2_Initiatives_4"
      },
      {
        show: true,
        title: "!{Badge_Opt5}-!{SIM_Initiatives_Opt5}",
        binding: "Q_SIM_R2_Initiatives_5"
      },
      {
        show: true,
        title: "!{Badge_Opt6}-!{SIM_Initiatives_Opt6}",
        binding: "Q_SIM_R2_Initiatives_6"
      },
      {
        show: true,
        title: "!{Badge_Opt7}-!{SIM_Initiatives_Opt7}",
        binding: "Q_SIM_R2_Initiatives_7"
      },
      {
        show: true,
        title: "!{Badge_Opt8}-!{SIM_Initiatives_Opt8}",
        binding: "Q_SIM_R2_Initiatives_8"
      },
      {
        show: true,
        title: "!{Badge_Opt9}-!{SIM_Initiatives_Opt9}",
        binding: "Q_SIM_R2_Initiatives_9"
      },
      {
        show: true,
        title: "!{Badge_Opt10}-!{SIM_Initiatives_Opt10}",
        binding: "Q_SIM_R2_Initiatives_10"
      },
      {
        show: true,
        title: "!{Badge_Opt11}-!{SIM_Initiatives_Opt11}",
        binding: "Q_SIM_R2_Initiatives_11"
      },
      {
        show: true,
        title: "!{Badge_Opt12}-!{SIM_Initiatives_Opt12}",
        binding: "Q_SIM_R2_Initiatives_12"
      },
      {
        show: true,
        title: "!{Badge_Opt13}-!{SIM_Initiatives_Opt13}",
        binding: "Q_SIM_R2_Initiatives_13"
      }
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>

  <!-- ************* -->
  <!-- TAB 2: EVENTS -->
  <!-- ************* -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: false,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        _title: "!{SIM_R2_Scenario2_Question}",
        title: "!{SIM_R2_Scenario2_Header}: !{SIM_R2_Scenario2_Title}",
        binding: "Q_SIM_R2_Scenario2"
      },
      {
        show: true,
        _title: "!{SIM_R2_Scenario3_Question}",
        title: "!{SIM_R2_Scenario3_Header}: !{SIM_R2_Scenario3_Title}",
        binding: "Q_SIM_R2_Scenario3"
      },
      {
        show: true,
        _title: "!{SIM_R2_Scenario4_Question}",
        title: "!{SIM_R2_Scenario4_Header}: !{SIM_R2_Scenario4_Title}",
        binding: "Q_SIM_R2_Scenario4"
      },
      {
        show: true,
        _title: "!{SIM_R2_Scenario5_Question}",
        title: "!{SIM_R2_Scenario5_Header}: !{SIM_R2_Scenario5_Title}",
        binding: "Q_SIM_R2_Scenario5"
      },
      {
        show: true,
        _title: "!{SIM_R2_Scenario6_Question}",
        title: "!{SIM_R2_Scenario6_Header}: !{SIM_R2_Scenario6_Title}",
        binding: "Q_SIM_R2_Scenario6"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario2_FB_Solution}",
      "!{SIM_R2_Scenario3_FB_Solution}",
      "!{SIM_R2_Scenario4_FB_Solution}",
      "!{SIM_R2_Scenario5_FB_Solution}",
      "!{SIM_R2_Scenario6_FB_Solution}"
    ],
    trackQuestion: "Show_charts",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>



  <!-- ******* -->
  <!-- RANKING -->
  <!-- ******* -->

  <Include name="SIM_Ranking_included_R2"></Include>






  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_finish",
          popup: "SIM_R2_FAC_dashboard_finish",
          close: "!{Header_LinkClose}",
          label: "!{GD_SIM_R2_Finish}",
          icon: "cancel"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_FAC_Navigation_Tab" response="2"/>
  </Voting>



</Action>