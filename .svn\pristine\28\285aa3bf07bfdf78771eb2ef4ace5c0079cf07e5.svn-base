<?xml version="1.0" encoding="utf-8" ?>
<!-- <Action mainAreaLayout="../../../layout/mainLayoutIntro" layout="../../../layout/tabsLayoutDebrief"> -->
<Action mainAreaLayout="../../../layout/mainLayoutIntro" layout="../../../layout/layoutDebrief">
  <Include name="Header_Intro"></Include>
  
  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{R1_Debrief_Scenario4_Header}",
      subheader: "!{R1_Debrief_Scenario4_Text}",
      valign: false,
      animate: "fadeIn",
      content: {
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{}"
      }      
    }]]>
  </Component>

  
  <Include name="R1_Debrief_Scenario4_chart"></Include>
  <Include name="R1_Debrief_Scenario4_table"></Include>

  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario4",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario4_FB_Opt1_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario4_Opt1}",
        body: "!{SIM_R1_Scenario4_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario4_Opt1_LTUs}","!{SIM_R1_Scenario4_Opt1_KPI1}","!{SIM_R1_Scenario4_Opt1_KPI2}","!{SIM_R1_Scenario4_Opt1_KPI3}","!{SIM_R1_Scenario4_Opt1_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario4",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario4"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario4",
        condition_Diff: "2",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario4_FB_Opt2_Img}",  alt: "!{}" ,
          position: "right large"
        },
        title: "!{SIM_R1_Scenario4_Opt2}",
        body: "!{SIM_R1_Scenario4_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_LTU}","!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}","!{KPI_Metric4}"],
        kpi_scores: ["!{SIM_R1_Scenario4_Opt2_LTUs}","!{SIM_R1_Scenario4_Opt2_KPI1}","!{SIM_R1_Scenario4_Opt2_KPI2}","!{SIM_R1_Scenario4_Opt2_KPI3}","!{SIM_R1_Scenario4_Opt2_KPI4}" ],
        inverseIcon: [false,false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario4",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario4"]
    }]]>
  </Component>

  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>
  

</Action>