<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayout_SIM_R1">
  <Include name="Header_SIM_R1"></Include>
  <Include name="KPIgauges"></Include>
  
  
  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "bounceInLeft",
      progress: "1",
      steps: [ "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}" ]
    }]]>
  </Component>


 <Component type="RadioButtons" customJS="true">
    <![CDATA[{
      templateInEvent: "html/radioButtons.dot",
      css: "styles/radioButtons.css",
      id: "",
      isHidden: false,
      animate: "fadeInRight",
      headerLabel: "!{}",
      header: "!{SIM_R1_Scenario1b_Question}",
      instructions: "!{SIM_R1_Scenario1b_Instructions}",
      titleLabel: "!{}",
      title: "!{}",
      subtitle: "",
      class: "with-gap",
      groupName: "radioGroup",
      preloadSaved: true,
      bind: "Q_SIM_R1_Scenario1b",
      moreInfo: "!{Choice_MoreInfo}",
      lessInfo: "!{Choice_LessInfo}",
      descActive: true,
      isCheck: true, hideIcons: true,
      
      noBlockAnswer: false,
      _optionsFromVote: "Q_Title_Input",
      _moreInfoFromVote: "Q_Desc_Input",
      _includeMyVote: false,

      isInline: false,
      optionWidth: "s6 big",

      grid: {  image:"s2 m3 l3", option: "s8 m9 l9" },
      options: [
        {
          value: "1",
          label: "!{Choice_Opt1}",
          image: "!{SIM_R1_Scenario1b_Img1}",
          _icon: "thumb_down", _iconDesign:"mdi-comment-remove",
          title: "!{SIM_R1_Scenario1b_Opt1}",
          _titleClass:"client-colors-text text-red",
          moreInfo: "!{SIM_R1_Scenario1b_Des1}",
          isCorrect: true,
            isDisabled: true,
            condition: "Q_SIM_R1_Scenario1",
            condition_Val: "1"
        },
        {
          value: "2",
          label: "!{Choice_Opt2}",
          image: "!{SIM_R1_Scenario1b_Img2}",
          title: "!{SIM_R1_Scenario1b_Opt2}",
          moreInfo: "!{SIM_R1_Scenario1b_Des2}",
          isCorrect: true,
            isDisabled: true,
            condition: "Q_SIM_R1_Scenario1",
            condition_Val: "2"
        },
        {
          value: "3",
          label: "!{Choice_Opt3}",
          image: "!{SIM_R1_Scenario1b_Img3}",
          title: "!{SIM_R1_Scenario1b_Opt3}",
          moreInfo: "!{SIM_R1_Scenario1b_Des3}",
          isCorrect: true,
            isDisabled: true,
            condition: "Q_SIM_R1_Scenario1",
            condition_Val: "3"
        },
        {
          value: "4",
          label: "!{Choice_Opt4}",
          image: "!{}",
          title: "!{SIM_R1_Scenario1b_Opt4}",
          moreInfo: "!{SIM_R1_Scenario1b_Des4}",
          isCorrect: false
        }
      ],
      
      autocheck: false,
      checkBtn: {
        label: "!{Choice_btnCheck}",
        animate: "slideInUp",
        _toast: { 
          ifCorrect: "!{Choice_toastCorrect}",
          ifIncorrect: "!{Choice_toastIncorrect}",
          points: "!{}"
        },
        toastMsg: "!{InputSubmited}",
        _idToClick: "navButton",
        idToClickIfCorrect: "navButton1",
        idToClickIfIncorrect: "navButton2",
        _idToShow: "navButton",
        _idToShowIfCorrect: "navButton1",
        _idToShowIfIncorrect: "navButton2",
        scrollToDown: false
      },
      
      _score : {
        points: { ifCorrect: "!{Quiz_pointsIfCorrect}", ifIncorrect: "!{Quiz_pointsIfIncorrect}"},
        question: "Score_SIM_R1_Scenario1b",
        _questions: [ ],
        _total: "Score_SIM_R1_Scenario1b_Total"
      },
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]     
    }]]>
  </Component>
  
  


  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>

  

  


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton1",
      isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          _gdActionEmbed: "SIM_R1_Scenario1b_AGG",
          _gdActionTrack: "GD",
          _gdActionSection: "",
          _targetSection: "",
          label: "!{Navigation_feedback}",
          tooltip: "!{Navigation_feedback_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton2",
      isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "jump",
          pulse: true,
          _gdActionEmbed: "SIM_R1_Scenario1b_AGG",
          _gdActionTrack: "GD",
          _gdActionSection: "",
          targetSection: "SIM_R1_Scenario2",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R1_Scenario1b}"/> 
  </Voting>




</Action>