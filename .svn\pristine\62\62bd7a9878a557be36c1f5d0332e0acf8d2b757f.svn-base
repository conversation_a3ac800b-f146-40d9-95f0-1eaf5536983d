define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT'], function ($, Q, WizerApi, WizletBase, doT) {

    var Droppable = function () {
        this.type = 'DroppablePosits';
        this.level = 1;
        this.total = 0; 
        this.drops = 0; 
        this.right = 0;  //number of right placements
        this.wrong = 0;  //number of wrong placements
    };

    Droppable.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        
        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Droppable.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
    };

    Droppable.prototype.render = function (options) {
        var self = this;
        var waiting = new Q.defer();
        return self.templateDefer.promise.then(function (template) {
        
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);

            
            var $draggables = options.context.find('.droppable-container .draggable');
            var $droppables = options.context.find('.droppable-container .droppable');

            self.total = $draggables.length;

            if (options.wizletInfo.stacked) self.drops = 1;

            // Create Droppable UI elements in the HTML context

            if (options.wizletInfo.isSolution) {

                //make draggable elements
                $draggables.draggable({ });
                //make droppable elements
                $droppables.droppable({ });

                //Preload animated posits attending to their paired
                        
                $(document).one('wizer:action:init', function(e, currentAction) { 
                    self.preLoadFeedback(options.context, options.wizletInfo);
                });

            } else {

                
                //make draggable elements
                $draggables.draggable({ 
                    revert: 'invalid',
                    stack: ".droppable-container .draggable"
                    // zIndex: 1,
                    // handle: '.draggable-pin'
                });

                //make droppable elements
                $droppables.droppable({
                    classes: {
                        'ui-droppable-active': 'ui-state-active',
                        'ui-droppable-hover': 'ui-state-hover'
                    },
                    tolerance: "pointer", //the draggable can be dropped when mouse enters in droppable section
                    activate: function (event, ui) {
                        //container active
                    },
                    //Behaviour when drop an element
                    drop: function( event, ui ) {
                        
                        var $dropped = $(this);         //place where dropped
                        var $dragged = $(ui.draggable); //dragged element to drop
                        
                        $dragged.removeClass('bounce');

                        //for a future retry action, we need to set origin (place where was taken) of the dropped element
                        //$dropped.attr('origin', $dragged.attr('origin')); 
                        //Increase the number of postits dropped (if first time)
                        if (typeof $dragged.attr('correct') == "undefined") {
                            self.drops ++;
                            //when all placed, fix the container height and show the submit button
                            if (self.drops==self.total) {
                                //$('.draggable-container-center').css('height',0);
                                if (! options.wizletInfo.img_background && options.wizletInfo.draggables_absolute ) {
                                    var dragContainer = options.context.find("section[class^='draggable-container']");
                                    let height = options.context.find('#droppable-container').outerHeight();
                                    dragContainer.outerHeight( height) ;
                                    dragContainer.css ("position","absolute");
                                }
                                options.context.find("#submitBtn").removeAttr('hidden');
                            }
                        }
                        
                        //Compare if question matches correctly (each drag has a paired number which matches with a drop-box)
                        if ($dropped.attr('paired') === $dragged.attr('paired')) {
                            $dragged.attr('correct',true);
                            self.right ++;
                        } else  {
                            $dragged.attr('correct',false);
                            self.wrong ++;
                        }
                        

                        //Save votes: a vote for every Drag (posit) with the title of the box where has been dropped
                        var questionID = self.wizerApi.getQuestionIdByName( $dragged.data('question') );
                        //var questionValue =  $dropped.parent().prev().html(); //catch the slot header
                        var questionValue =  $dropped.attr('paired'); //catch the table index

                        self.wizerApi.addVotes({
                                votes:[{
                                    questionId:   questionID, 
                                    responseText: questionValue
                                }]});
                        
                        //Dropping highlight effect
                        $dropped.addClass('dropped');
                        setTimeout(function() {
                            $dropped.removeClass('dropped');
                        }, 500);
                    }
                });

                //Preload already saved votes (in case of reload the page)
                //self.preLoadVotes(options.context);
                
                if (options.wizletInfo.isPositioned)
                    self.preLoadFeedback(options.context, options.wizletInfo);

            }


            options.context.find('#submitBtn').on('click', function (event) {
                
                if (options.wizletInfo.submitBtn.blockAfter)
                    options.context.find('.droppable-container').addClass('disabled');

                if (options.wizletInfo.submitBtn.toast)
                    M.toast({
                        html: options.wizletInfo.submitBtn.toast,
                        classes: 'rounded',
                        displayLength: options.wizletInfo.submitBtn.toastDuration || 2e3
                    });

                var newTry= false;
                if (options.wizletInfo.id && options.wizletInfo.submitBtn.giveATry) {
                    if (localStorage.getItem( options.wizletInfo.id ) == 1) {
                        newTry= false;
                    } else {
                        newTry= true;
                        localStorage.setItem(options.wizletInfo.id, 1);       
                    }
                } 


                if (options.wizletInfo.submitBtn.hidden && !newTry) 
                    options.context.find("#submitBtn").prop('hidden',true);

                
                if (options.wizletInfo.isCheck) {
                    var points=0;
                    $.each($draggables, function(idx, drag) {
                        if (typeof $(drag).attr('correct') != "undefined") {
                            var isCorrect = ($(drag).attr('correct') == "true");
                            if (isCorrect) {
                                $(drag).find('i.check-icon').removeClass('red-text').addClass('green-text').html('check_circle');
                            } else {
                                $(drag).find('i.check-icon').removeClass('green-text').addClass('red-text').html('cancel');
                            }
                            $(drag).find('i.check-icon').addClass('scale-in').removeClass('scaled-out'); 

                            if (options.wizletInfo.score && options.wizletInfo.score.points && isCorrect)
                                points += Number(options.wizletInfo.score.points);
                            
                                console.log(points)
                        }
                    });
                                    
                    if (options.wizletInfo.score && options.wizletInfo.score.question) {                    
                        self.addVote(options.wizletInfo.score.question, points);
                    }
                }

                $(options.wizletInfo.submitBtn.idToShow).removeAttr('hidden');
            });

            return true;
        })
        .fail(this.wizerApi.showError);
    };



    Droppable.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };


    //Function to load stored values and automatically place draggables in the droppable boxes
    Droppable.prototype.preLoadFeedback = function (context, wizletInfo) {
        
        var self = this;

        var delay = 3000;
        
        var positsByBox = {};
        var myPosition, atPosition;
        
        var $drag, $drop;
        var $draggables = context.find('.droppable-container .draggable');
        var $droppables = context.find('.droppable-container');
        var pair;
        
        $.each($draggables, function(i, drag) {
            $drag = $(drag);
            pair = (wizletInfo.isSolution) ? $drag.attr('paired') : $drag.attr('origin');
  
            //save number of posits dropped in each box
            if (positsByBox[pair]) {
                positsByBox[pair] ++;
            } else {
                positsByBox[pair] = 1;
            }
                  
            //get box container from its pair
            $drop = $droppables.find('.droppable[paired='+pair+']').first();
    
            
            //as can be various posits per box, set the position to avoid they get stacked
            //if still same drop box, change the posit where to place the posit
            //position = (positsByBox[pair] % 2 === 0) ? 'right' : 'left'; 
            
            //3 postits in a row
            // var mod = positsByBox[pair] % 3;
            // switch (mod) {
            //     case 1: myPosition='center center'; atPosition='left center'; break;
            //     case 2: myPosition='center center'; atPosition='center center'; break;
            //     case 0: myPosition='center center'; atPosition='right center'; break;
            // }

            //4 postits (2 above, 2 bellow)
            var mod = positsByBox[pair] % wizletInfo.maxPerBoard;
            if (wizletInfo.stacked) {
                myPosition='center top'; atPosition='center top'; 
            } else {
                switch (mod) {
                    case 1: myPosition='left top'; atPosition='left top'; break;
                    case 2: myPosition='right top'; atPosition='right top'; break;
                    //case 3: myPosition='left center'; atPosition='left center'; break;
                    //case 4: myPosition='right center'; atPosition='right center'; break;
                    case 3: myPosition='left bottom'; atPosition='left bottom'; break;
                    case 0: myPosition='right bottom'; atPosition='right bottom'; break;
                }
            }

            
            //automatically set draggable element's position
            $drag.position({
                my: myPosition,     // position on the element being positioned to align with the target element
                at: atPosition,     // position on the target element to align the positioned element against
                of: $drop,
                using: function(pos) {
                    if (wizletInfo.stacked) pos.top = wizletInfo.offsetIni + pos.top*(mod+1)*wizletInfo.offset;
                    $drag.animate(pos, delay, 'easeOutBack');
                }
            });            
                        
            //Disable dropped element
            if (wizletInfo.isSolution)
                $drag.draggable('disable');

                
            if (wizletInfo.isPositioned) {
                
                //Compare if question matches correctly (each drag has a paired number which matches with a drop-box)
                if ($drop.attr('paired') === $drag.attr('paired')) {
                    $drag.attr('correct',true);
                    self.right ++;
                } else  {
                    $drag.attr('correct',false);
                    self.wrong ++;
                }

                var questionID = self.wizerApi.getQuestionIdByName( $drag.data('question') );
                var questionValue =  $drag.attr('origin');
                self.wizerApi.addVotes({
                        votes:[{
                            questionId:   questionID, 
                            responseText: questionValue
                        }]});
            }
            
        });

        //Disable al the drop-boxes
        if (wizletInfo.isSolution)
            $.each($droppables, function(drag) {
                $(drag).droppable('disable');
            });

        // setTimeout(function() {
        //     context.find('.slots-container .tooltip-icon.panel-left').removeClass('hidden')
        //             .tooltip( { placement:'left', container: 'body'});
        //     context.find('.slots-container .tooltip-icon.panel-right').removeClass('hidden')
        //             .tooltip( { placement:'right', container: 'body'});
        // }, delay);

    };


    //Function to load stored values and automatically place draggables in the droppable boxes
    Droppable.prototype.preLoadVotes = function (context) {
        
        var self = this;

        var qIds=[], qDrags=[], i=0;
        var $dragged, $dropped;
        var value;
        var positsByBox = {};
        var myPosition, atPosition;
        var position;

        //Get IDs from all the Draggables of left-section
        $.each(self.wizletInfo.draggables.left, function(i, draggable) {                
            qDrags.push( draggable.bind );
            qIds.push( self.wizerApi.getQuestionIdByName( draggable.bind ) );
        });
        //Get IDs from all the Draggables of right-section
        $.each(self.wizletInfo.draggables.right, function(i, draggable) {           
            qDrags.push( draggable.bind );
            qIds.push( self.wizerApi.getQuestionIdByName( draggable.bind ) );
        });
              
        //Get stored votes for all the draggables (posits) IDs
        self.wizerApi.getMyVotes(qIds).then(function (response) {
            $.each(response.votes, function(obj, vote) {
                value = vote[0]; //get dropped box TITLE as stored value
                if (value) {

                    //save number of posits dropped in each box
                    if (positsByBox[value]) {
                        positsByBox[value] ++;
                    } else {
                        positsByBox[value] = 1;
                    }

                    //get box container from its title
                    $dropped = context.find('.slots-container .header:contains('+value+')').next().children();
                    //get posit from its id
                    $dragged = context.find('.draggable[data-question='+qDrags[i]+']');

                    //for a future retry action, we need to set origin (place where was taken) of the dropped element
                    $dropped.attr('origin', $dragged.attr('origin'));
                    
                    //as can be various posits per box, set the position to avoid they get stacked
                    //if still same drop box, change the posit where to place the posit
                    //position = (positsByBox[value] % 2 === 0) ? 'right' : 'left'; 

                    var mod = positsByBox[value] % 3;
                    
                    switch (mod) {
                        case 1: myPosition='center center'; atPosition='left center'; break;
                        case 2: myPosition='center center'; atPosition='center center'; break;
                        case 0: myPosition='center center'; atPosition='right center'; break;
                    }
        
                    
                    //automatically set draggable element's position
                    $dragged.position({
                        my: myPosition,     // position on the element being positioned to align with the target element
                        at: atPosition,     // position on the target element to align the positioned element against
                        of: $dropped,
                        using: function(pos) {
                            $dragged.animate(pos, 100, 'easeOutBack');
                        }
                    });          

                    //automatically set draggable element's position
                    // $dragged.position({
                    //     my: position,     // position on the element being positioned to align with the target element
                    //     at: position,     // position on the target element to align the positioned element against
                    //     of: $dropped,
                    //     using: function(pos) {
                    //         $dragged.animate(pos, 100, 'easeOutBack');
                    //     }
                    // });
                    

                    //Compare if question matches correctly
                    if ($dropped.attr('paired') === $dragged.attr('paired')) {
                        $dragged.attr('correct',true);
                        self.right ++;
                    } else  {
                        $dragged.attr('correct',false);
                        self.wrong ++;
                    }
                    
                }
                i++; //next posit vote
            });
            

        });

    };

       

    Droppable.getRegistration = function () {
        return new Droppable();
    };

    return Droppable;

});