<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutFAC">
  <Include name="HeaderFAC"></Include>
  

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "1",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row1A}", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row2A}", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row3A}", "!{R2_Logins_Table_Row3B}" ],
          [ "!{R2_Logins_Table_Row4A}", "!{R2_Logins_Table_Row4B}" ],
          [ "!{R2_Logins_Table_Row5A}", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "2",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s1}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s1}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s1}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s1}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "3",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s2}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s2}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s2}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s2}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "4",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s3}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s3}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s3}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s3}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "5",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s4}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s4}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s4}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s4}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "6",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s5}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s5}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s5}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s5}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "7",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s6}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s6}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s6}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s6}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "8",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s7}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s7}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s7}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s7}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "9",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s8}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s8}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s8}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s8}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "10",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s9}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s9}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s9}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s9}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: true,
        condition: "GD",
        condition_Diff: "11",
      header: "!{R2_Logins_Table_Header}",
      animate: "zoomIn",
      class: "_responsive-table_ bigHeaders firstColumnBold centered fixed align-center",
      table: {
        title: "!{R2_Logins_Table_Title}",
        body: "!{R2_Logins_Table_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{R2_Logins_Table_HeaderA}", "!{R2_Logins_Table_HeaderB}" ],
        rows: [
          [ "!{R2_Logins_Table_Row_s10}_1", "!{R2_Logins_Table_Row1B}" ],
          [ "!{R2_Logins_Table_Row_s10}_2", "!{R2_Logins_Table_Row2B}" ],
          [ "!{R2_Logins_Table_Row_s10}_3", "!{R2_Logins_Table_Row3B}" ],
          [ "...", "..." ],
          [ "!{R2_Logins_Table_Row_s10}_20", "!{R2_Logins_Table_Row5B}" ]
        ],
        note: "!{}"
      },
      scope: ["GD"]   
    }]]>
  </Component>


  <Component type="Collapsible" customJS="true">
    <![CDATA[{
      templateInEvent: "html/collapsible.dot",
      css: "styles/collapsible.css",
      animate: "fadeInLeft animate__delay-2s",
      accordion: true,
      popout: false,
        expand_more: "expand_more",
        expand_less: "expand_less",
      items: [
        {
          active: false,
          title: "!{R2_Logins_Table_FACtit}", 
          text: "!{R2_Logins_Table_FACtext}",
          _texts: [
            { title: "!{}", text: "!{}" },
            { title: "!{}", text: "!{}" } 
          ]
        }
      ]
    }]]>
  </Component> 


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left clicked",
          gdActionTrack: "GD", gdActionSection: "R2_Logins_Table",
          icon: "replay"
        },
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left2",
          gdActionTrack: "GD", gdActionSection: "LandingPage_Pause",
          icon: "pause_circle_filled"
        },
        {
          type: "target",
          pulse: false,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R2_Welcome",
          targetSection: "R2_Welcome_FAC",
          label: "!{R2_Welcome}",
          icon: "info"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list",
          isFloat: false, 
          tooltip: "!{}"
        },
        {
          type: "target",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Start",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{GD_SIM_R2_Landing} (SELFPACED MODE)",
          icon: "screen_share"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>