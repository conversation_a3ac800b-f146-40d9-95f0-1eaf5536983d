@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";



.wizlet.WordCloudreport {
    
    margin: 1rem 0 0 0;

    .cl-header--main {
        background: none;
        width: 80%;
        margin: 0 auto;
        padding: 0;

        h1 {
            text-align: left;
            font-size: 1.5rem;
            line-height: 110%;
            margin: 1rem 0 0.6rem 0;
            font-weight: normal;
            font-family: clientRegular, sans-serif;
            color: color("client-colors","font");
        }
    }

    .cl-container {

        overflow: hidden;
        .cl-instructions {
            text-align: left;
        }
        .cl-content {
            padding: 2rem !important;
            border: 1px solid color("client-colors", "border");
            margin-bottom: 2rem;
            background: color("client-colors","white");
            svg {
                width: 100% !important;
            }
            .tag {
                margin: 10px 20px;
                line-height: 1;
                font-family: clientBold, impact;

                @mixin tag-size($from, $to) {
                    @for $i from $from to $to+1 {
                        &.tag-size-#{$i} {
                            font-size: #{$i}px;
                        }

                    }
                }
                @include tag-size(30,60);

            }
        }
    }
    
    
}