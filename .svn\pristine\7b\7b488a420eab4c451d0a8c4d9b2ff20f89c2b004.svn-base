<?xml version="1.0" encoding="utf-8" ?>
<Action>
  
  <!-- <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeInLeft",
      transparentBox: false,
      content: {
        position: "up",
        _animate: "fadeInUp animate__delay-3s",
        title: "!{SIM_R1_CaseStudy3_Title}",
        body: "!{SIM_R1_CaseStudy3_Text}"
      }
    }]]>  
  </Component> -->


  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/table.dot",
      css: "styles/table.css",
      isHidden: false,
      header: "!{}",
      animate: "fadeInLeft",
      class: "_responsive-table_ _bigHeaders_ _smallHeaders _firstColumnBold _centered _fixed _align-center",
      table: {
        title: "!{SIM_R1_CaseStudy3_Title}",
        body: "!{SIM_R1_CaseStudy3_Text}",
        _image: { 
          src: "!{}",  alt: "!{}", width: "5"
        },
        headers: [ "!{SIM_R1_CaseStudy3_TableHeadA}","!{SIM_R1_CaseStudy3_TableHeadB}" ],
        rows: [
          [ "!{SIM_R1_CaseStudy3_TableRow1A}", "!{SIM_R1_CaseStudy3_TableRow1B}" ],
          [ "!{SIM_R1_CaseStudy3_TableRow2A}", "!{SIM_R1_CaseStudy3_TableRow2B}" ],
          [ "!{SIM_R1_CaseStudy3_TableRow3A}", "!{SIM_R1_CaseStudy3_TableRow3B}" ]
        ],
        note: "!{}"
      },
      scope: [""]
    }]]>
  </Component>




  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>

  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      _isHidden: true, _showDelay: "3000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "SIM_R1_AGG",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


</Action>