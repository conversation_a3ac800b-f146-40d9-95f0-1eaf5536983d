﻿
<div class="{{?it.fullScreen}}fullscreen{{??}}container{{?}}{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"
    {{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    <div class="_row_">
        <div>
            
            {{?it.header}}<h4 class="header title">{{=it.header}}</h4>{{?}}
            {{?it.subheader}}<h5 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.subheader}}</h5>{{?}}
            {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

            <div class="card hoverable {{?it.size}}{{=it.size}}{{?}}">
                
                {{?it.content}}
                <div class="card-content no-pad-bot">
                    {{?it.content.title}}<span class="card-title">{{=it.content.title}}</span>{{?}}
                    {{?it.content.body}}<p class="flow-text">{{=it.content.body}}</p>{{?}}
                </div>  
                {{?}}

                <div class="card-image">
                    <img class="responsive-img" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.image.src}}" alt="{{=it.image.alt}}"/>
                    
                    {{~it.tooltips :tooltip :idx}}
                        {{?tooltip.modal}}
                        <a class="btn-tooltip modal-trigger tooltipped  {{?tooltip.color}}client-colors-text text-{{=tooltip.color}}{{?}}
                                            {{?tooltip.animate}}animated {{=tooltip.animate}}{{?}}"  
                            href="#{{=tooltip.modal.popupID}}"  data-modal="{{=tooltip.modal.popup}}"
                            style="top:{{=tooltip.top}}; left:{{=tooltip.left}}; 
                                    {{?tooltip.width}}width:{{=tooltip.width}};{{?}} 
                                    {{?tooltip.height}}height:{{=tooltip.height}};{{?}}">
                            {{?tooltip.icon}}
                                <i class="material-icons {{?tooltip.bounce}}bounce{{?}}"  style="position:absolute; {{?tooltip.iconSize}} font-size:{{=tooltip.iconSize}};{{?}} {{?tooltip.iconLeft}} left:{{=tooltip.iconLeft}};{{?}}; {{?tooltip.iconTop}} top:{{=tooltip.iconTop}};{{?}}" >{{=tooltip.icon}}</i>
                            {{?}} 
                            {{?tooltip.iconDesign}}
                                <i class="mdi medium {{=tooltip.iconDesign}} {{?tooltip.bounce}}bounce{{?}}"  style="position:absolute; {{?tooltip.iconSize}} font-size:{{=tooltip.iconSize}};{{?}} {{?tooltip.iconLeft}} left:{{=tooltip.iconLeft}};{{?}}; {{?tooltip.iconTop}} top:{{=tooltip.iconTop}};{{?}}" > </i>
                            {{?}}                          
                        </a>
                        {{??}}
                        <a hidden class="btn-tooltip tooltipped timage {{?tooltip.color}}client-colors-text text-{{=tooltip.color}}{{?}}
                                            {{?tooltip.animate}}animated {{=tooltip.animate}}{{?}}" 
                            data-position="{{=tooltip.position}}" data-tooltip="{{=tooltip.text}}"                            
                            style="top:{{=tooltip.top}}; left:{{=tooltip.left}}; 
                                    {{?tooltip.width}}width:{{=tooltip.width}};{{?}} 
                                    {{?tooltip.height}}height:{{=tooltip.height}};{{?}}">
                            {{?tooltip.icon}}
                                <i class="material-icons {{?tooltip.bounce}}bounce{{?}}" style="position:absolute; {{?tooltip.iconSize}} font-size:{{=tooltip.iconSize}};{{?}} {{?tooltip.iconLeft}} left:{{=tooltip.iconLeft}};{{?}}; {{?tooltip.iconTop}} top:{{=tooltip.iconTop}};{{?}}" >{{=tooltip.icon}}</i>
                            {{?}}
                            {{?tooltip.iconDesign}}
                                <i class="mdi medium {{=tooltip.iconDesign}} {{?tooltip.bounce}}bounce{{?}}"  style="position:absolute; {{?tooltip.iconSize}} font-size:{{=tooltip.iconSize}};{{?}} {{?tooltip.iconLeft}} left:{{=tooltip.iconLeft}};{{?}}; {{?tooltip.iconTop}} top:{{=tooltip.iconTop}};{{?}}" > </i>
                            {{?}}  
                            {{?tooltip.underline}}
                                <div class="underline {{?tooltip.pulse}}animated pulse{{?}}"
                                        style="top:{{=tooltip.underline.top}}; left:{{=tooltip.underline.left}}; width:{{=tooltip.underline.width}};">
                                </div>
                            {{?}}
                        </a>
                        {{?}}
                    {{~}}

                </div>
      
                
            </div>
        </div>
    </div>
</div>







<!-- ********************************** -->
<!-- *********  MODAL-POPUPS  ********* -->
<!-- ********************************** -->
{{~it.tooltips :tooltip:idx}}
    {{?tooltip.modal}}
    <div id="{{=tooltip.modal.popupID}}" class="modal large">
        <div class="modal-content"> </div>
        <div class="modal-footer">
            <a class="btn modal-close client-colors button2">
                <i class="small material-icons right">close</i>{{=tooltip.modal.close}}
            </a>
        </div>
    </div>
    {{?}}
{{~}}
