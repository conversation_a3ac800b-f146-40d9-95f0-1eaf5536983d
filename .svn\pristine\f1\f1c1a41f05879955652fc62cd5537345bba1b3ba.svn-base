<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
      <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- Acumulative of the Initiatives chosen in the different rounds (taken the highest value: 1 or 0)-->
    <Total result="Q_SIM_Initiatives_1" method="high">
      <Question>Q_SIM_R1_Initiatives_1</Question>
      <Question>Q_SIM_R2_Initiatives_1</Question>
      <Question>Q_SIM_R3_Initiatives_1</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_2" method="high">
      <Question>Q_SIM_R1_Initiatives_2</Question>
      <Question>Q_SIM_R2_Initiatives_2</Question>
      <Question>Q_SIM_R3_Initiatives_2</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_3" method="high">
      <Question>Q_SIM_R1_Initiatives_3</Question>
      <Question>Q_SIM_R2_Initiatives_3</Question>
      <Question>Q_SIM_R3_Initiatives_3</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_4" method="high">
      <Question>Q_SIM_R1_Initiatives_4</Question>
      <Question>Q_SIM_R2_Initiatives_4</Question>
      <Question>Q_SIM_R3_Initiatives_4</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_5" method="high">
      <Question>Q_SIM_R1_Initiatives_5</Question>
      <Question>Q_SIM_R2_Initiatives_5</Question>
      <Question>Q_SIM_R3_Initiatives_5</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_6" method="high">
      <Question>Q_SIM_R1_Initiatives_6</Question>
      <Question>Q_SIM_R2_Initiatives_6</Question>
      <Question>Q_SIM_R3_Initiatives_6</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_7" method="high">
      <Question>Q_SIM_R1_Initiatives_7</Question>
      <Question>Q_SIM_R2_Initiatives_7</Question>
      <Question>Q_SIM_R3_Initiatives_7</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_8" method="high">
      <Question>Q_SIM_R1_Initiatives_8</Question>
      <Question>Q_SIM_R2_Initiatives_8</Question>
      <Question>Q_SIM_R3_Initiatives_8</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_9" method="high">
      <Question>Q_SIM_R1_Initiatives_9</Question>
      <Question>Q_SIM_R2_Initiatives_9</Question>
      <Question>Q_SIM_R3_Initiatives_9</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_10" method="high">
      <Question>Q_SIM_R1_Initiatives_10</Question>
      <Question>Q_SIM_R2_Initiatives_10</Question>
      <Question>Q_SIM_R3_Initiatives_10</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_11" method="high">
      <Question>Q_SIM_R1_Initiatives_11</Question>
      <Question>Q_SIM_R2_Initiatives_11</Question>
      <Question>Q_SIM_R3_Initiatives_11</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_12" method="high">
      <Question>Q_SIM_R1_Initiatives_12</Question>
      <Question>Q_SIM_R2_Initiatives_12</Question>
      <Question>Q_SIM_R3_Initiatives_12</Question>
    </Total>
    <Total result="Q_SIM_Initiatives_13" method="high">
      <Question>Q_SIM_R1_Initiatives_13</Question>
      <Question>Q_SIM_R2_Initiatives_13</Question>
      <Question>Q_SIM_R3_Initiatives_13</Question>
    </Total>

    <!-- ###### -->
    <!-- SCORES -->
    <!-- ###### -->

    <!-- LTUs_C1 -->
    <Score result="Score_SIM_R3_Initiatives_LTUs_C1" type="Choice" method="sum">
      <Question name="Q_SIM_R3_Initiatives_1" value="1" Response="!{SIM_Initiatives_Opt1_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_2" value="1" Response="!{SIM_Initiatives_Opt2_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_3" value="1" Response="!{SIM_Initiatives_Opt3_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_4" value="1" Response="!{SIM_Initiatives_Opt4_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_5" value="1" Response="!{SIM_Initiatives_Opt5_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_6" value="1" Response="!{SIM_Initiatives_Opt6_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_7" value="1" Response="!{SIM_Initiatives_Opt7_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_8" value="1" Response="!{SIM_Initiatives_Opt8_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_9" value="1" Response="!{SIM_Initiatives_Opt9_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_10" value="1" Response="!{SIM_Initiatives_Opt10_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_11" value="1" Response="!{SIM_Initiatives_Opt11_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_12" value="1" Response="!{SIM_Initiatives_Opt12_LTUs_C1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_13" value="1" Response="!{SIM_Initiatives_Opt13_LTUs_C1}"></Question>
    </Score>

    <!-- KPI1 -->
    <Score result="Score_SIM_R3_Initiatives_KPI1" type="Choice" method="sum">
      <Question name="Q_SIM_R3_Initiatives_1" value="1" Response="!{SIM_Initiatives_Opt1_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_2" value="1" Response="!{SIM_Initiatives_Opt2_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_3" value="1" Response="!{SIM_Initiatives_Opt3_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_4" value="1" Response="!{SIM_Initiatives_Opt4_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_5" value="1" Response="!{SIM_Initiatives_Opt5_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_6" value="1" Response="!{SIM_Initiatives_Opt6_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_7" value="1" Response="!{SIM_Initiatives_Opt7_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_8" value="1" Response="!{SIM_Initiatives_Opt8_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_9" value="1" Response="!{SIM_Initiatives_Opt9_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_10" value="1" Response="!{SIM_Initiatives_Opt10_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_11" value="1" Response="!{SIM_Initiatives_Opt11_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_12" value="1" Response="!{SIM_Initiatives_Opt12_KPI1}"></Question>
      <Question name="Q_SIM_R3_Initiatives_13" value="1" Response="!{SIM_Initiatives_Opt13_KPI1}"></Question>
    </Score>

    <!-- KPI2 -->
    <Score result="Score_SIM_R3_Initiatives_KPI2" type="Choice" method="sum">
      <Question name="Q_SIM_R3_Initiatives_1" value="1" Response="!{SIM_Initiatives_Opt1_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_2" value="1" Response="!{SIM_Initiatives_Opt2_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_3" value="1" Response="!{SIM_Initiatives_Opt3_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_4" value="1" Response="!{SIM_Initiatives_Opt4_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_5" value="1" Response="!{SIM_Initiatives_Opt5_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_6" value="1" Response="!{SIM_Initiatives_Opt6_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_7" value="1" Response="!{SIM_Initiatives_Opt7_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_8" value="1" Response="!{SIM_Initiatives_Opt8_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_9" value="1" Response="!{SIM_Initiatives_Opt9_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_10" value="1" Response="!{SIM_Initiatives_Opt10_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_11" value="1" Response="!{SIM_Initiatives_Opt11_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_12" value="1" Response="!{SIM_Initiatives_Opt12_KPI2}"></Question>
      <Question name="Q_SIM_R3_Initiatives_13" value="1" Response="!{SIM_Initiatives_Opt13_KPI2}"></Question>
    </Score>

    <!-- KPI3 -->
    <Score result="Score_SIM_R3_Initiatives_KPI3" type="Choice" method="sum">
      <Question name="Q_SIM_R3_Initiatives_1" value="1" Response="!{SIM_Initiatives_Opt1_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_2" value="1" Response="!{SIM_Initiatives_Opt2_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_3" value="1" Response="!{SIM_Initiatives_Opt3_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_4" value="1" Response="!{SIM_Initiatives_Opt4_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_5" value="1" Response="!{SIM_Initiatives_Opt5_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_6" value="1" Response="!{SIM_Initiatives_Opt6_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_7" value="1" Response="!{SIM_Initiatives_Opt7_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_8" value="1" Response="!{SIM_Initiatives_Opt8_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_9" value="1" Response="!{SIM_Initiatives_Opt9_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_10" value="1" Response="!{SIM_Initiatives_Opt10_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_11" value="1" Response="!{SIM_Initiatives_Opt11_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_12" value="1" Response="!{SIM_Initiatives_Opt12_KPI3}"></Question>
      <Question name="Q_SIM_R3_Initiatives_13" value="1" Response="!{SIM_Initiatives_Opt13_KPI3}"></Question>
    </Score>

    <!-- ###### -->
    <!-- TOTALS -->
    <!-- ###### -->

    <!-- LTUs -->
    <Total result="Score_SIM_Total_R3_LTUs_C1" method="sum">
      <Question validate="false">Score_SIM_Init_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Initiatives_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Scenario2_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Scenario3_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Scenario4_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Scenario5_LTUs_C1</Question>
    </Total>

    <!-- Penalty -->
    <Score type="Range" result="Score_SIM_R3_Penalty_Multiplier">
      <Question>Score_SIM_Total_R3_LTUs_C1</Question>
      <Boundary value="1">0</Boundary>
      <Boundary value="0">99</Boundary>
    </Score>
    <Total result="Score_SIM_R3_Penalty_KPI2_C1" method="multiply">
      <Question validate="false">Score_SIM_Total_R3_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Penalty_Multiplier</Question>
    </Total>
    <Total result="Score_SIM_R3_Penalty_KPI3_C1" method="multiply">
      <Question validate="false">Score_SIM_Total_R3_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R3_Penalty_Multiplier</Question>
    </Total>

    <!-- KPI1 -->
    <Total result="Score_SIM_Total_R3_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Initiatives_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario4_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario5_KPI1</Question>
    </Total>

    <!-- KPI2 -->
    <Total result="Score_SIM_Total_R3_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Initiatives_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario4_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario5_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Penalty_KPI2_C1</Question>
    </Total>

    <!-- KPI3 -->
    <Total result="Score_SIM_Total_R3_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Initiatives_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario4_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario5_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Penalty_KPI3_C1</Question>
    </Total>

    <!-- ROUND TOTAL -->
    <Total result="Score_SIM_Total_R3" method="sum">
      <Question validate="false">Score_SIM_Total_R3_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI3</Question>
    </Total>

  </Aggregator>

</Action>