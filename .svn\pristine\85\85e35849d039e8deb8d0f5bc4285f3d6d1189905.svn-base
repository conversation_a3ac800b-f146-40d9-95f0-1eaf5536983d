<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
      <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- ###### -->
    <!-- SCORES -->
    <!-- ###### -->

    <!-- LTUs_C1 -->
    <Score result="Score_SIM_R2_Scenario6_LTUs_C1" type="Choice">
      <Question name="Q_SIM_R2_Scenario6">
        <Choice value="1" Response="!{SIM_R2_Scenario6_Opt1_LTUs_C1}"></Choice>
        <Choice value="2" Response="!{SIM_R2_Scenario6_Opt2_LTUs_C1}"></Choice>
        <Choice value="3" Response="!{SIM_R2_Scenario6_Opt3_LTUs_C1}"></Choice>
      </Question>
    </Score>

    <!-- KPI1 -->
    <Score result="Score_SIM_R2_Scenario6_KPI1" type="Choice">
      <Question name="Q_SIM_R2_Scenario6">
        <Choice value="1" Response="!{SIM_R2_Scenario6_Opt1_KPI1}"></Choice>
        <Choice value="2" Response="!{SIM_R2_Scenario6_Opt2_KPI1}"></Choice>
        <Choice value="3" Response="!{SIM_R2_Scenario6_Opt3_KPI1}"></Choice>
      </Question>
    </Score>

    <!-- KPI2 -->
    <Score result="Score_SIM_R2_Scenario6_KPI2" type="Choice">
      <Question name="Q_SIM_R2_Scenario6">
        <Choice value="1" Response="!{SIM_R2_Scenario6_Opt1_KPI2}"></Choice>
        <Choice value="2" Response="!{SIM_R2_Scenario6_Opt2_KPI2}"></Choice>
        <Choice value="3" Response="!{SIM_R2_Scenario6_Opt3_KPI2}"></Choice>
      </Question>
    </Score>

    <!-- KPI3 -->
    <Score result="Score_SIM_R2_Scenario6_KPI3" type="Choice">
      <Question name="Q_SIM_R2_Scenario6">
        <Choice value="1" Response="!{SIM_R2_Scenario6_Opt1_KPI3}"></Choice>
        <Choice value="2" Response="!{SIM_R2_Scenario6_Opt2_KPI3}"></Choice>
        <Choice value="3" Response="!{SIM_R2_Scenario6_Opt3_KPI3}"></Choice>
      </Question>
    </Score>

    <!-- ###### -->
    <!-- TOTALS -->
    <!-- ###### -->

    <!-- LTUs -->
    <Total result="Score_SIM_Total_R2_LTUs_C1" method="sum">
      <Question validate="false">Score_SIM_Init_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Initiatives_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Scenario4_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Scenario5_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Scenario6_LTUs_C1</Question>
    </Total>

    <!-- Penalty -->
    <Score type="Range" result="Score_SIM_R2_Penalty_Multiplier">
      <Question>Score_SIM_Total_R2_LTUs_C1</Question>
      <Boundary value="1">0</Boundary>
      <Boundary value="0">99</Boundary>
    </Score>
    <Total result="Score_SIM_R2_Penalty_KPI2_C1" method="multiply">
      <Question validate="false">Score_SIM_Total_R2_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Penalty_Multiplier</Question>
    </Total>
    <Total result="Score_SIM_R2_Penalty_KPI3_C1" method="multiply">
      <Question validate="false">Score_SIM_Total_R2_LTUs_C1</Question>
      <Question validate="false">Score_SIM_R2_Penalty_Multiplier</Question>
    </Total>

    <!-- KPI1 -->
    <Total result="Score_SIM_Total_R2_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Initiatives_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario4_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario5_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario6_KPI1</Question>
    </Total>

    <!-- KPI2 -->
    <Total result="Score_SIM_Total_R2_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Initiatives_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario4_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario5_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario6_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Penalty_KPI2_C1</Question>
    </Total>

    <!-- KPI3 -->
    <Total result="Score_SIM_Total_R2_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Initiatives_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario4_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario5_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario6_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Penalty_KPI3_C1</Question>
    </Total>

    <!-- ROUND TOTAL -->
    <Total result="Score_SIM_Total_R2" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total_R2</Question>
    </Total>

  </Aggregator>


</Action>