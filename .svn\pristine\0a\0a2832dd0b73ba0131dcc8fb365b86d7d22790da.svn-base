<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayout_SIM_R1">
  <Include name="Header_SIM_R1"></Include>
  <Include name="KPIgauges_R1"></Include>


  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "bounceInLeft",
      progress: "1",
      steps: [ "!{SIM_BreadCrumbs_0}",
               "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}" ]
    }]]>
  </Component>


  <!-- Card component with feedback values passed by scope -->
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "",
      header: "!{SIM_Initiatives_FB_Header}",
      instructions: "!{SIM_Initiatives_FB_Instructions}",
      valign: false,
      animate: "fadeInUp",
      content: {
        _img: { 
          materialboxed: false, 
          src: "!{}",  alt: "!{}" 
        },
        title: "!{}",
        body: "!{}"
      },

      feedbackType: "multi2",
      feedbackOptions: [
        {
          bind: "Q_SIM_R1_Initiatives_1",
          label: "!{Badge_Opt1}",
          title: "!{SIM_Initiatives_Opt1}",
          text: "!{SIM_Initiatives_Des1}<br><br>!{SIM_Initiatives_Opt1_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_2",
          label: "!{Badge_Opt2}",
          title: "!{SIM_Initiatives_Opt2}",
          text: "!{SIM_Initiatives_Des2}<br><br>!{SIM_Initiatives_Opt2_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_3",
          label: "!{Badge_Opt3}",
          title: "!{SIM_Initiatives_Opt3}",
          text: "!{SIM_Initiatives_Des3}<br><br>!{SIM_Initiatives_Opt3_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_4",
          label: "!{Badge_Opt4}",
          title: "!{SIM_Initiatives_Opt4}",
          text: "!{SIM_Initiatives_Des4}<br><br>!{SIM_Initiatives_Opt4_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_5",
          label: "!{Badge_Opt5}",
          title: "!{SIM_Initiatives_Opt5}",
          text: "!{SIM_Initiatives_Des5}<br><br>!{SIM_Initiatives_Opt5_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_6",
          label: "!{Badge_Opt6}",
          title: "!{SIM_Initiatives_Opt6}",
          text: "!{SIM_Initiatives_Des6}<br><br>!{SIM_Initiatives_Opt6_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_7",
          label: "!{Badge_Opt7}",
          title: "!{SIM_Initiatives_Opt7}",
          text: "!{SIM_Initiatives_Des7}<br><br>!{SIM_Initiatives_Opt7_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_8",
          label: "!{Badge_Opt8}",
          title: "!{SIM_Initiatives_Opt8}",
          text: "!{SIM_Initiatives_Des8}<br><br>!{SIM_Initiatives_Opt8_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_10",
          label: "!{Badge_Opt10}",
          title: "!{SIM_Initiatives_Opt10}",
          text: "!{SIM_Initiatives_Des10}<br><br>!{SIM_Initiatives_Opt10_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_11",
          label: "!{Badge_Opt11}",
          title: "!{SIM_Initiatives_Opt11}",
          text: "!{SIM_Initiatives_Des11}<br><br>!{SIM_Initiatives_Opt11_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_12",
          label: "!{Badge_Opt12}",
          title: "!{SIM_Initiatives_Opt12}",
          text: "!{SIM_Initiatives_Des12}<br><br>!{SIM_Initiatives_Opt12_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_13",
          label: "!{Badge_Opt13}",
          title: "!{SIM_Initiatives_Opt13}",
          text: "!{SIM_Initiatives_Des13}<br><br>!{SIM_Initiatives_Opt13_FB}"
        }
      ],
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower",
              "Q_SIM_R1_Initiatives_1",
              "Q_SIM_R1_Initiatives_2",
              "Q_SIM_R1_Initiatives_3",
              "Q_SIM_R1_Initiatives_4",
              "Q_SIM_R1_Initiatives_5",
              "Q_SIM_R1_Initiatives_6",
              "Q_SIM_R1_Initiatives_7",
              "Q_SIM_R1_Initiatives_8",
              "Q_SIM_R1_Initiatives_10",
              "Q_SIM_R1_Initiatives_11",
              "Q_SIM_R1_Initiatives_12",
              "Q_SIM_R1_Initiatives_13"]
    }]]>
  </Component>




  <Component type="Card" customJS="true">
    <!-- <Component type="Vanilla"> -->
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeInUp",
      content: {
        img: { 
          materialboxed: false, 
          src: "!{}",  alt: "!{}" 
        },
        title: "!{}",
        body: "!{SIM_Initiatives_FB_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: [
          "!{KPI_Metric1}",
          "!{KPI_Metric2}",
          "!{KPI_Metric3}"
        ],
        fromDB: true,
        kpi_scores: [],
        kpi_scoresDB: [
          "Score_SIM_R1_Initiatives_KPI1",
          "Score_SIM_R1_Initiatives_KPI2",
          "Score_SIM_R1_Initiatives_KPI3"
        ],
        lastBold: false,
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "",
        value: ""
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: [
        "Follower",
        "Score_SIM_R1_Initiatives_KPI1", 
        "Score_SIM_R1_Initiatives_KPI2", 
        "Score_SIM_R1_Initiatives_KPI3"
      ]     
    }]]>
  </Component>


  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: true, showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          _gdActionEmbed: "SIM_R1_Scenario1_AGG",
          _gdActionTrack: "GD",
          _gdActionSection: "",
          _targetSection: "",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R1_FAC_dashboard_tab2} !{Navigation_feedback}"/>
  </Voting>



</Action>