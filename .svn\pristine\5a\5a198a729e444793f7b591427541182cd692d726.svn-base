@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


@mixin dropdown-box {
    ul.dropdown-content {
        transform-origin: 0px 0px !important;

        > li {
            .model-render {
                display: none;
            }
        }
    }
}

.wizlet.wizletDropDownButton {
    
    .card {

        .card-content {
            @include card-title; 

            .dropdown {
            
                .option > .header {
                    // margin: 1rem 0;
                    padding-right: 0;
                }    

                .button {
                    .dropdown-trigger.btn {
                        display: inline-flex;
                        height: 100%;
                        width: 100%;

                        >span, >i {
                            padding: 0;
                        }
                        
                        &.disabled {
                            opacity: 1;
                            pointer-events: none;
                            filter: grayscale(100%);
                        }
                    }

                    @include dropdown-box;

                }

            }
        }
        
        
    }
}

.mainAreaContainer {
    @include dropdown-box;
}