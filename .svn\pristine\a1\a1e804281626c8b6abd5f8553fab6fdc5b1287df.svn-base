﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var Tabs = function () {
        this.type = 'Tabs';
        this.level = 1;
        this.follower = false;
        this.selectedTab = 0;
    };

    Tabs.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Tabs.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };

    Tabs.prototype.render = function (options) {
        var self = this;
        
        if (self.rendering) {
            return self.rendering.promise;
        }
        self.rendering = new Q.defer();

        //Before rendering, check if (being a follower) must take the leader's text (used in feedbacks templates)
        var validating = self.validate();
        
        //Validating data before rendering
        validating.then(function () {

            
            self.templateDefer.promise.then(function (template) {
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);
                
                //to ensure the components are all loaded, after WIZER MODEL is loaded (not when is into a modal)
                if (options.wizletInfo.tabPanelID) {
                    self.loadsTabs();
                } else {    
                    $(document).one('wizer:action:init', function(e, currentAction) {        
                        self.loadsTabs();
                    });
                }

                self.rendering.resolve();            

            })

        });

        return self.rendering.promise;

    };


    Tabs.prototype.loadsTabs = function () {
        var self = this;

        if (self.wizletInfo.saveCurrentTab) {
    
            $('.mainArea').css('visibility', 'hidden');
            window.newtimer = setTimeout( //delay for myCode_jumpTo code
                function() { 
                    self.wizletContext.find('li a.activeTab').removeClass('activeTab').addClass('active');
                    self.wizletContext.find('.tabs').tabs({ 
                        swipeable: (self.wizletInfo.swipeable==null) ? true : self.wizletInfo.swipeable,
                        onShow: function(current_item) {
                            if (self.wizletInfo.saveCurrentTab && self.wizletInfo.activeTab) {
                                self.addVote(self.wizletInfo.activeTab,this.index)
                            }
                        } 
                    });  
                    $('.mainArea').css('visibility', 'visible');
                }, 
                1000
            );
            
        } else {
            if (self.wizletInfo.activeTab)
                self.wizletContext.find('li a.activeTab').removeClass('activeTab').addClass('active');
                self.wizletContext.find('.tabs').tabs({ 
                swipeable: (self.wizletInfo.swipeable==null) ? true : self.wizletInfo.swipeable
            });  
        }
            
        //The follower loads the leader's selected option
        if (self.follower) {
            var nTab = self.selectedTab - 1;
            self.wizletContext.find('#tab'+nTab +' span').html(self.wizletInfo.myText).addClass('strong');
        };

    };

    

    Tabs.prototype.iAmFollower = function (questionName) {
        var self = this;

        return (self.wizletInfo.isFollower && 
                self.wizletInfo.DB[self.wizletInfo.isFollower] == 1);
    };

    
    Tabs.prototype.addVote = function (questionName, val) {
        var self = this;
        var questionId = self.wizerApi.getQuestionIdByName(questionName)
        self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});
    };

    
    /**
     * Promise function: code to do before rendering the Tab component (used in feedbacks templates)
     */
    Tabs.prototype.validate = function () {
        var self = this;
        var validating = new Q.defer();
        
        //The follower listens the model to load the leader's selected option and enable the navigation button
        if (self.iAmFollower()) {
                
            self.follower = true;
            var teamId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackTeam);
            var questionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.bind);
            self.wizerApi.getForemanVotes(teamId,[questionId]).then(function (response) {  
                if ( response.votes[questionId].length > 0 ) {
                    self.selectedTab = response.votes[questionId][0];
                    self.wizletInfo.DB[self.wizletInfo.bind] = self.selectedTab;
                }
                validating.resolve();
            });           

        } else {
            validating.resolve();
        };

        return validating.promise;
    };


    Tabs.getRegistration = function () {
        return new Tabs();
    };

    return Tabs;

});