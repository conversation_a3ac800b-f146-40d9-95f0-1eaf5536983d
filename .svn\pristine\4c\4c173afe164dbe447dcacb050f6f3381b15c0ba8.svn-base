﻿/* 
    "WordCloud" Component to display 
*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'd3', 'd3WordCloud', 'WodCloudUtil'], function ($, Q, WizerApi, WizletBase, doT, d3, d3WordCloud, WodCloudUtil) {

    var WordCloud = function () {
        this.type = 'WordCloud';
        this.level = 1;
    };
    var callResize = true;

    WordCloud.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.unsedContext = unused;
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.templateDefer = Q.defer();
        this.svgPromise = Q.defer();

        var self = this;
        var requirements = [];
        requirements.push(WizletBase.loadTemplate(wizletInfo, 'wordCloud.dot'));

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        //requirements.push('js!./Wizer/Content/js/lib/d3.layout.cloud.js');
        
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });
        
        return Q.all([WizletBase.loadHandler({ wizlet: this, render: this.render }), this.svgPromise.promise]);
    };

    WordCloud.prototype.unloadHandler = function () {
        //unload wizletbase
        $(window).off('resize');
        WizletBase.unloadHandler({ wizlet: this });
    };

    WordCloud.prototype.render = function (options) {
        var self = this;
          self.templateDefer.promise.then(function (template) {
            var promise = self.getVotes();
            return promise.then(function (str) {
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);
                wordCloudObj=self.initializeWordCloud(options.wizletInfo,self.wizletContext);
                self.createWordCloud(self,wordCloudObj.container,wordCloudObj.conf,str);
                $(window).off('resize').on('resize', function(event){
                    self.windowResize(str);
                });
            });
        })
        .fail(this.wizerApi.showError)
    };
    
    WordCloud.prototype.windowResize= function(str) {
        var self = this;
        var $container = $(self.wizletContext).find('[data-wordCloudContainer]');
        self.wizletContext.find('[data-wordCloudContainer]').empty();
        var w = $($container[0]).width();
        var h = $($container[0]).height();
        wordCloudObj.conf.width=w;
        wordCloudObj.conf.height=h;
        self.createWordCloud(self,wordCloudObj.container,wordCloudObj.conf,str);
    }

    WordCloud.prototype.initializeWordCloud= function(wizletInfo,wizletContext){
        
                var wordCloudObj={};
                var self = this;
                var $container = $(wizletContext).find('[data-wordCloudContainer]');
                var conf = {
                    completePromise: self.svgPromise
                }
                if (wizletInfo.maxWords) conf.maxWords = wizletInfo.maxWords;
                if (wizletInfo.angleCount) conf.angleCount = wizletInfo.angleCount;
                if (wizletInfo.angleFrom) conf.angleFrom = wizletInfo.angleFrom;
                if (wizletInfo.angleTo) conf.angleTo = wizletInfo.angleTo;
                if (wizletInfo.colors) conf.colors = wizletInfo.colors;
                if (wizletInfo.spiral) conf.spiral = wizletInfo.spiral;
                if (wizletInfo.font) conf.font = wizletInfo.font;
                if (wizletInfo.scaleType) conf.scaleType = wizletInfo.scaleType;
                if (wizletInfo.ignoreWords) conf.ignoreWords = wizletInfo.ignoreWords;
                wordCloudObj.conf=conf;
                wordCloudObj.container=$container;
                return wordCloudObj;
    }

    WordCloud.prototype.createWordCloud= function(self,container,conf,str){
        self.wordCloud = new WodCloudUtil(container, conf);
        self.wordCloud.parseText(str);
        if(navigator.userAgent.toLowerCase().indexOf('firefox') > -1){
            this.svgPromise.promise.then(function() {
                if(callResize) {
                    callResize = false;
                    setTimeout(function(){
                        self.windowResize(str);
                    }, 500) 
                }
            });
        }
    }
    
    WordCloud.prototype.getVotes = function () {
        var self = this;
        var defer = Q.defer();
        var qId=[];
        self.wizletInfo.questions.forEach(function(question,index){
            qId.push(self.wizerApi.getQuestionIdByName(self.wizletInfo.questions[index].binding));
        });
        self.trackQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackQuestion);
        self.wizerApi.getVotes({ questionIds: qId, groupQuestionId: self.trackQuestionId }).then(function (result) {
            var str = "";
            result.votes.forEach(function (vote) {
                str += (str) ? ", " + vote.responseText : vote.responseText;
            });
            defer.resolve(str);
        });        
        return defer.promise;
    }

    WordCloud.getRegistration = function () {
        return new WordCloud();
    };

    return WordCloud;

});
