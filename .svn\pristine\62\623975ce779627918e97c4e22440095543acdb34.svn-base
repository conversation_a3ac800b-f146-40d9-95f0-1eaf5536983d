@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";

.wizletCountDownTimer {

  font-size: large;
  
  .countDownTimerHolder {
    padding: 5px 10px;
    min-width: 150px;
    background-color: color("client-colors", "primary");
    // border-top-right-radius: 15px;
    text-align: center;
    
    &.big {
      padding: 50px 20px 20px 20px;
      border-radius: 15px;
      .countDownTimer {
        font-size: 100px;
      }
    }
    
    &.center {
      margin: 40vh 0 0 30vw;
      width: 30vw;
    }
    
    .countDownTimer {
      font-size: 20px;
      font-weight: bold;
      background-color: transparent !important;
    }
  }


  &.finished .countDownTimerHolder {

    &.big {
      padding-bottom: 30px;
      .countDownTimer {
        font-size: 50px;
      }
    }
    
    &.center {
      margin: 40vh 0 0 5vw;
      width: 90vw;
    }


  }
  
  &.disabled .countDownTimerHolder {
    background-color: color("client-colors", "secondary");
  }


}




