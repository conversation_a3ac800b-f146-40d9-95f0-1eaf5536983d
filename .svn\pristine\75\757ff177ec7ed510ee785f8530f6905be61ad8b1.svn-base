﻿
<div class="container{{?it.valign}} valign-container{{?}}{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.borders}} borders {{=it.borders}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} 

{{?it.isHidden || it.isHighlight}}
    {{?it.condition}}
        {{? (it.condition_Val && (it.DB[it.condition] == it.condition_Val)) ||
            (it.condition_Diff && (!it.DB[it.condition] || (it.DB[it.condition] != it.condition_Diff))) ||
            (it.condition_Greater && (!it.DB[it.condition] || (Number(it.DB[it.condition]) >= Number(it.condition_Greater)))) ||
            (it.condition_Less && (!it.DB[it.condition] || (Number(it.DB[it.condition]) < Number(it.condition_Less))))  }}
            {{?it.isHidden}}hidden{{??}}highlight{{?}}
        {{?}}
    {{??}}
        {{?it.isHidden}}hidden{{??}}highlight{{?}}
    {{?}}
{{?}}
>  

<div class="_row {{?it.valign}}valign-wrapper{{?}}">
<div>
   
   {{?it.header}}<h4 class="header {{?it.centerHeader}}center{{?}} {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}} {{?it.fitmodal}}fit-modal{{?}}">{{=it.header}}</h4>{{?}}
   {{?it.subheader}}<h5 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.subheader}}</h5>{{?}}
   {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
   
   {{?it.content}}
   <div class="card hoverable {{?it.size}}{{=it.size}}{{?}} {{?it.orientation}}{{=it.orientation}}{{?}}{{?it.content && it.content.position}} {{=it.content.position}}{{?}} {{?!it.header}}no-header{{?}} {{?it.transparentBox}}transparent{{?}} {{?it.gray}}gray{{?}} {{?it.moreInfoBtn}}with-more-info-button{{?}} {{?it.animatedFrame}}animated-frame{{?}}">
       
       {{?it.content.position && it.content.position=="up" && (it.content.title || it.content.body)}}
       <div class="card-content row {{?!it.content.img}}no-image{{?}} {{?it.content.nopadding}}no-padding{{?}} {{?it.content.centered}}centered{{?}}{{?it.content.borders}} borders {{=it.content.borders}}{{?}}">
           {{?it.content.title}}<span class="card-title {{?it.content.titleClass}}{{=it.content.titleClass}}{{?}}">{{=it.content.title}}</span>{{?}}
           
           {{?it.content.body}}
            
            {{?it.content.img_embed && it.content.img_embed.src}}
                <img class="{{?it.content.img_embed.src_vert}}hide-on-small-and-down{{?}} {{?it.content.img && it.content.img.src}}hide-on-med-and-up{{?}} embed {{?it.content.img_embed.position}}{{=it.content.img_embed.position}}{{?}}{{?it.content.img_embed.materialboxed}} materialboxed{{?}}{{?it.content.img_embed.animate}} animate__animated animate__{{=it.content.img_embed.animate}}{{?}}" 
                {{?it.content.img_embed.style}}style="{{=it.content.img_embed.style}}"{{?}}
                src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img_embed.src}}" {{?it.content.img_embed.alt}}alt="{{=it.content.img_embed.alt}}"{{?}}/>
                
                {{?it.content.img_embed.src_vert}}
                    <img class="hide-on-med-and-up embed {{?it.content.img_embed.position}}{{=it.content.img_embed.position}}{{?}}{{?it.content.img_embed.materialboxed}} materialboxed{{?}}" {{?it.content.img_embed.style}}style="{{=it.content.img_embed.style}}"{{?}}
                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img_embed.src_vert}}" {{?it.content.img_embed.alt}}alt="{{=it.content.img_embed.alt}}"{{?}}/>
                {{?}}
            {{?}}
       
                {{?it.content.animate}}<div class="animate__animated animate__{{=it.content.animate}}">{{?}}
                <span class="flow-text">{{=it.content.body}}</span>
                {{?it.content.animate}}</div>{{?}}
                
                <!-- {{?it.content.body2}}<span class="flow-text">{{=it.content.body2}}</span>{{?}} -->
            {{?}}
       </div>  
       {{?}} 


        {{?it.content.img}}
        <div class="card-image {{?it.content.img.grid}}row{{?}} {{?it.content.img.animate}}animate__animated animate__{{=it.content.img.animate}}{{?}} {{?it.orientation=='horizontal'}}{{?it.content.img.isHiddenWhenSmall}}hide-on-small-and-down{{?}}{{??}}full-width{{?}} {{?it.content.img.borders}}borders {{=it.content.img.borders}}{{?}} {{?it.content.img.nopadding}}no-padding{{?}} {{?it.content.img.extrapadding}}extra-padding{{?}}{{?it.content.img.small}} small{{?}}">
        {{??}}
            {{?it.content.video}}
                <div class="card-image full-width">
            {{?}}
        {{?}}

            {{?it.content.img && it.content.img.src}}           
               <img class="{{?it.content.img.grid}}with-grid col {{=it.content.img.grid}} {{?}}{{?it.content.img.src_vert}}hide-on-small-and-down{{?}} responsive-img {{?it.content.img.frame}}frame {{=it.content.img.frame}}{{?}} {{?it.content.img.materialboxed}}materialboxed{{?}} {{?it.content.img.maxwidth}}maxwidth{{?}} {{?it.content.img.height}}with-height{{?}}" 
                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img.src}}" alt="{{=it.content.img.alt}}"
                    {{?it.content.img.style}}style="{{=it.content.img.style}}"{{?}}/>
               
               {{?it.content.img.src_vert}}
               <img class="hide-on-med-and-up responsive-img {{?it.content.img.frame}}frame {{=it.content.img.frame}}{{?}}" 
                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img.src_vert}}" alt="{{=it.content.img.alt}}"/>
               {{?}}



               {{?it.content.img.title}}
               <span class="card-title {{?it.content.img.titleClass}}{{=it.content.img.titleClass}}{{?}}">{{=it.content.img.title}}</span>
               {{?}}

               
                {{?it.content.img && it.content.img.detail}}
                <div class="detail-container">
                    {{?it.content.img.detail.text}}<h6 class="header detail">{{=it.content.img.detail.text}}</h6>{{?}}
                    <i class="material-icons detail bounce client-colors-text text-font3 modal-trigger" 
                        href="#{{=it.content.img.detail.popupID}}" 
                        data-modal="{{=it.content.img.detail.popup}}">{{=it.content.img.detail.icon}}</i>
                </div>
                {{?}} 
            {{?}}

            {{?it.content.video}}
            <video {{?it.content.video.novideo}}class="novideo"{{??}}class="responsive-video" preload="metadata" controls controlsList="nodownload"{{?}} {{?it.content.video.autoplay}}autoplay{{?}}{{?it.content.video.loop}} loop{{?}}{{?it.content.video.muted}} muted{{?}}
                {{?it.content.video.poster}}poster="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.video.poster}}"{{?}}>
                <source src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.video.src}}" type="video/mp4">
            </video>
            {{?}}

            
        {{?it.content.img || it.content.video}}
        </div>
       
        {{?it.content.position && it.content.position=="up" && it.content.body2}}
        <div class="card-content row {{?!it.content.img}}no-image{{?}} {{?it.content.nopadding}}no-padding{{?}} {{?it.content.centered}}centered{{?}}{{?it.content.borders}} borders {{=it.content.borders}}{{?}}">
            <span class="flow-text">{{=it.content.body2}}</span>
        </div>  
        {{?}}
       

       {{?}}
       
       

       {{? (!it.content.position || it.content.position=="down") }}
       <div class="card-content row {{?!it.content.img}}no-image{{?}} {{?it.onlyImage}}onlyImage{{?}} {{?it.content.centered}}centered{{?}}{{?it.content.borders}} borders {{=it.content.borders}}{{?}}">
            {{?it.content.title}}<span class="card-title {{?it.content.titleClass}}{{=it.content.titleClass}}{{?}}">{{=it.content.title}}</span>{{?}}
            {{?it.content.body}}
            
                {{?it.content.img_embed && it.content.img_embed.src}}
                    <img class="{{?it.content.img_embed.src_vert}}hide-on-small-and-down{{?}} {{?it.content.img && it.content.img.src}}hide-on-med-and-up{{?}} embed {{?it.content.img_embed.position}}{{=it.content.img_embed.position}}{{?}}{{?it.content.img_embed.materialboxed}} materialboxed{{?}}{{?it.content.img_embed.animate}} animate__animated animate__{{=it.content.img_embed.animate}}{{?}}"
                    {{?it.content.img_embed.style}}style="{{=it.content.img_embed.style}}"{{?}}
                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img_embed.src}}" {{?it.content.img_embed.alt}}alt="{{=it.content.img_embed.alt}}"{{?}}/>
                    
                    {{?it.content.img_embed.src_vert}}
                        <img class="hide-on-med-and-up embed {{?it.content.img_embed.position}}{{=it.content.img_embed.position}}{{?}}{{?it.content.img_embed.materialboxed}} materialboxed{{?}}" {{?it.content.img_embed.style}}style="{{=it.content.img_embed.style}}"{{?}}
                        src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img_embed.src_vert}}" {{?it.content.img_embed.alt}}alt="{{=it.content.img_embed.alt}}"{{?}}/>
                    {{?}}
                {{?}}
       

                {{?it.content.animate}}<div class="animate__animated animate__{{=it.content.animate}}">{{?}}
                <span class="flow-text">{{=it.content.body}}</span>
                {{?it.content.animate}}</div>{{?}}

                {{?it.content.bodyQuestion}}
                    {{?it.content.bodyQuestion.title}}<span class="flow-text bodyQuestionTitle">{{=it.content.bodyQuestion.title}}</span>{{?}}
                    <span class="flow-text bodyQuestion">{{=it.DB[it.content.bodyQuestion.question]}}</span>
                {{?}}
                
                {{?it.content.extraBody}}<span class="flow-text">{{=it.content.extraBody}}</span>{{?}}
                
            {{?}}

            {{?it.contentByValue}}
                <span class="flow-text">{{?it.DB[it.contentByValue.value]}}{{=it.contentByValue.text[it.DB[it.contentByValue.value]]}}{{??}}{{=it.contentByValue.text[0]}}{{?}}</span>
            {{?}}

            

            {{? it.feedbackOptions}}
            <div class="feedback">
                

                {{? it.feedbackType === "text"}}

                    {{~it.feedbackOptions :option:idx}}
                    
                        {{?it.DB[option.bind] && it.DB[option.bind]==idx}}
                            {{?option.title}}<span class="card-title">{{=option.title}}</span>{{?}}
                            <span class="flow-text">{{=option.text}}</span>

                            {{? option.extra_feedback && 
                                option.extra_feedback.bind && option.extra_feedback.value &&
                                it.DB[option.extra_feedback.bind] &&
                                (it.DB[option.extra_feedback.bind] ==  option.extra_feedback.value) }}
                            <br><br><span class="flow-text">
                                {{= option.extra_feedback.text}}
                            </span>
                            {{?}}

                            {{?option.kpi_scores}}
                            <div class="score-container {{? (it.feedback_score.bind == '') || (it.DB[it.feedback_score.bind]==it.feedback_score.value)}}selected{{?}}">
                                <span class="card-title centered">{{=it.feedback_score.title}}</span>

                                {{~option.kpi_scores :kpi:idx}}
                                <div class="row points">
                                    <div class="col hide-on-small-only m8 label">
                                        <span>{{=it.feedback_score.kpi_titles[idx]}}</span>
                                    </div>
                                    <div class="col s12 m4 score">
                                        {{?kpi.split(" ")[0] > 0}}
                                            {{?it.feedback_score.icon_right}}<i class="material-icons green-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{?!it.feedback_score.lastIcon || idx==(it.feedback_score.kpi_scoresDB.length-1)}}{{=it.feedback_score.icon_right}}{{?}}</i>{{?}}
                                        {{??}}
                                            {{?kpi.split(" ")[0] < 0}}
                                                {{?it.feedback_score.icon_wrong}}<i class="material-icons red-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{=it.feedback_score.icon_wrong}}</i>{{?}}
                                            {{??}}
                                                {{?it.feedback_score.icon_none}}<i class="material-icons orange-text idx{{=idx}}">{{=it.feedback_score.icon_none}}</i>{{?}}
                                            {{?}}
                                        {{?}}
                                        
                                        <span><b>{{=kpi}}</b></span>
                                    </div>
                                </div>
                                {{~}}

                            </div>
                            {{?}}


                        {{?}}
                    {{~}}

                {{??}}
                <ul class="{{?it.feedbackListClass}}{{=it.feedbackListClass}}{{??}}square-list{{?}}">
                
                {{var cont=0;}}
                {{~it.feedbackOptions :option:idx}}
    
                    {{? it.feedbackType === "value"}}
                        {{?it.DB[option.bind] || it.beginsHidden}}
                            <li>
                                <div>
                                    <span class="title">{{=option.title}}</span> <span class="value" data-binding="{{=option.bind}}"></span> 
                                    {{?option.title2}}<span class="title2">{{=option.title2}}</span>{{?}}
                                </div>
                            </li>
                        {{?}}
                    {{?}}
    
                    {{? it.feedbackType === "single"}}
                        {{?it.DB[option.bind] && it.DB[option.bind]==(idx+1)}}
                            <li><span class="flow-text">{{?option.label}}{{=option.label}}: {{?}}{{=option.title}} <br> {{=option.text}}</li>
                        {{?}}
                    {{?}}
    
                    {{? it.feedbackType === "multi1"}}
                        <li><span class="flow-text">{{=option.title}}: {{?it.DB[option.bind]}}{{=it.DB[option.bind]}}{{??}}0{{?}} <br> {{=option.text}}</span></li> <br>
                    {{?}}
    
                    {{? it.feedbackType === "multi2"}}
                        {{?it.DB[option.bind]==1}}
                            {{?cont>0}}<br>{{?}}
                            <li><span class="flow-text"><b>{{?option.label}}{{=option.label}} - {{?}}{{=option.title}}</b> {{?option.text}}<br><br> {{=option.text}}{{?}}</span></li> <br>
                            {{?option.image}}
                                <div class="feedback-image {{?option.grid}}row{{?}}">
                                    <img class="{{?option.grid}}with-grid col {{=option.grid}} {{?}}responsive-img" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=option.image}}" alt="{{=option.title}}">
                                </div>
                            {{?}}
                            
                            {{ cont++; }} 
                        {{?}}
                    {{?}}

                {{~}}
                </ul> 
                {{?}}

                {{?it.content.body2}}<br><span class="flow-text">{{=it.content.body2}}</span>{{?}}
            </div>
           {{?}}
              

            {{? it.feedback_score}}


            {{? it.feedback_score && it.feedback_score.extra_feedback && 
                (it.feedback_score.extra_feedback.position && it.feedback_score.extra_feedback.position=="up") && 
                it.feedback_score.extra_feedback.bind && it.feedback_score.extra_feedback.value &&
                it.DB[it.feedback_score.extra_feedback.bind] &&
                (it.DB[it.feedback_score.extra_feedback.bind] ==  it.feedback_score.extra_feedback.value) }}
            <br><br><span class="flow-text">
                {{= it.feedback_score.extra_feedback.text}}
            </span>
            {{?}}


                {{? (it.feedback_score.fromDB && !it.feedback_score.bind) || 
                    (it.feedback_score.fromDB && it.feedback_score.bind && it.DB[it.feedback_score.bind]==it.feedback_score.val)}}

            <div class="score-container {{? (it.feedback_score.bind == '') || (it.DB[it.feedback_score.bind]==it.feedback_score.value)}}selected{{?}}">
                <span class="card-title centered">{{=it.feedback_score.title}}</span>
                
                    {{~it.feedback_score.kpi_scoresDB :kpi:idx}}
                    <div class="row points {{? it.feedback_score.lastBold && (idx==it.feedback_score.kpi_scoresDB.length-1)}}bold{{?}}">
                        <div class="col hide-on-small-only m8 label">
                            <span>{{=it.feedback_score.kpi_titles[idx]}}</span>
                        </div>
                        <div class="col s12 m4 score">
                            {{?it.DB[kpi] > 0}}
                                {{?it.feedback_score.icon_right}}<i class="material-icons green-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{?!it.feedback_score.lastIcon || idx==(it.feedback_score.kpi_scoresDB.length-1)}}{{=it.feedback_score.icon_right}}{{?}}</i>{{?}}
                            {{??}}
                                {{?it.DB[kpi] < 0}}
                                    {{?it.feedback_score.icon_wrong}}<i class="material-icons red-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{=it.feedback_score.icon_wrong}}</i>{{?}}
                                {{??}}
                                    {{?it.feedback_score.icon_none}}<i class="material-icons orange-text idx{{=idx}}">{{=it.feedback_score.icon_none}}</i>{{?}}
                                {{?}}
                            {{?}}
                            
                            {{var value = it.DB[kpi] ? ( Math.round(it.DB[kpi] * 100) / 100 ) : 0 ;}}  
                            <span class="{{?it.feedback_score.firstBig && (idx==0)}}first{{?}}"><b>{{=value}}</b> {{?it.feedback_score.suffix && it.feedback_score.suffix[idx]}}{{=it.feedback_score.suffix[idx]}}{{?}}</span>
                        </div>
                    </div>
                    {{~}}
                </div>

                {{??}}

                    {{? (   it.feedback_score.extra_feedback && it.feedback_score.extra_feedback.kpi_scores &&
                            it.feedback_score.extra_feedback.bind && it.feedback_score.extra_feedback.value &&
                            it.DB[it.feedback_score.extra_feedback.bind] &&
                            (it.DB[it.feedback_score.extra_feedback.bind] ==  it.feedback_score.extra_feedback.value)
                        )
                    }}

                    <div class="score-container {{? (it.feedback_score.bind == '') || (it.DB[it.feedback_score.bind]==it.feedback_score.value)}}selected{{?}}">
                        <span class="card-title centered">{{=it.feedback_score.title}}</span>
                       
                        {{~it.feedback_score.extra_feedback.kpi_scores :kpi:idx}}
                        <div class="row points">
                            <div class="col hide-on-small-only m8 label">
                                <span>{{=it.feedback_score.kpi_titles[idx]}}</span>
                            </div>
                            <div class="col s12 m4 score">
                                {{?kpi.split(" ")[0] > 0}}
                                    {{?it.feedback_score.icon_right}}<i class="material-icons green-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{?!it.feedback_score.lastIcon || idx==(it.feedback_score.kpi_scoresDB.length-1)}}{{=it.feedback_score.icon_right}}{{?}}</i>{{?}}
                                {{??}}
                                    {{?kpi.split(" ")[0] < 0}}
                                        {{?it.feedback_score.icon_wrong}}<i class="material-icons red-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{=it.feedback_score.icon_wrong}}</i>{{?}}
                                    {{??}}
                                        {{?it.feedback_score.icon_none}}<i class="material-icons orange-text idx{{=idx}}">{{=it.feedback_score.icon_none}}</i>{{?}}
                                    {{?}}
                                {{?}}
                                
                                <span><b>{{=kpi}}</b></span>
                        </div>
                    </div>
                    {{~}}
                    </div>

                {{??}}

                {{?it.feedback_score.kpi_scores && it.feedback_score.kpi_scores.length>0}}
                <div class="score-container {{?!it.content.img}}col s12 m6 offset3 {{?}} {{? (it.feedback_score.bind == '') || (it.DB[it.feedback_score.bind]==it.feedback_score.value)}}selected{{?}}">
                    <span class="card-title centered">{{=it.feedback_score.title}}</span>
                   
                    {{~it.feedback_score.kpi_scores :kpi:idx}}
                    <div class="row points">
                        <div class="col s8 label">
                            <span>{{=it.feedback_score.kpi_titles[idx]}}</span>
                        </div>
                        <div class="col s4 score">
                            {{?kpi.split(" ")[0] > 0}}
                                {{?it.feedback_score.icon_right}}<i class="material-icons green-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{?!it.feedback_score.lastIcon || idx==(it.feedback_score.kpi_scoresDB.length-1)}}{{=it.feedback_score.icon_right}}{{?}}</i>{{?}}
                            {{??}}
                                {{?kpi.split(" ")[0] < 0}}
                                    {{?it.feedback_score.icon_wrong}}<i class="material-icons red-text idx{{=idx}} {{?it.feedback_score.inverseIcon && it.feedback_score.inverseIcon[idx]}}inverse{{?}}">{{=it.feedback_score.icon_wrong}}</i>{{?}}
                                {{??}}
                                    {{?it.feedback_score.icon_none}}<i class="material-icons orange-text idx{{=idx}}">{{=it.feedback_score.icon_none}}</i>{{?}}
                                {{?}}
                            {{?}}
                            
                            <span><b>{{=kpi}}</b></span>
                        </div>
                    </div>
                    {{~}}
                </div>
                {{?}}


                {{?}}

                {{?}}

            </div>
            {{?}}

            {{? it.feedback_score && it.feedback_score.extra_feedback && 
                (!it.feedback_score.extra_feedback.position || it.feedback_score.extra_feedback.position=="down") && 
                it.feedback_score.extra_feedback.bind && it.feedback_score.extra_feedback.value &&
                it.DB[it.feedback_score.extra_feedback.bind] &&
                (it.DB[it.feedback_score.extra_feedback.bind] ==  it.feedback_score.extra_feedback.value) }}
            <br><br><span class="flow-text">
                {{= it.feedback_score.extra_feedback.text}}
            </span>
            {{?}}

       </div>            
       {{?}}


       {{?it.moreInfoBtn}}
       <div class="row moreInfo">
           <a id="{{=it.moreInfoBtn.popup}}Btn" class="modal-trigger btn client-colors button2 {{?it.moreInfoBtn.animate}}animate__animated animate__{{=it.moreInfoBtn.animate}}{{?}}"
               href="#{{=it.moreInfoBtn.popupID}}" data-modal="{{=it.moreInfoBtn.popup}}">
               <i class="medium material-icons left">{{?it.moreInfoBtn.icon}}{{=it.moreInfoBtn.icon}}{{??}}pie_chart{{?}}</i>{{?it.moreInfoBtn.label}}{{=it.moreInfoBtn.label}}{{?}}
           </a>
       </div>
       {{?}}

   </div>
   {{?}}
</div>
</div>
</div>




<!-- ********************************** -->
<!-- *********  MODAL-POPUPS  ********* -->
<!-- ********************************** -->
{{?it.content && it.content.img && it.content.img.detail}}
<div id="{{=it.content.img.detail.popupID}}" class="modal large">
<div class="modal-content"> </div>
<div class="modal-footer">
    <a class="btn modal-close client-colors button2">
        <i class="small material-icons right">close</i>{{=it.content.img.detail.close}}
    </a>
</div>
</div>
{{?}}

<!-- If autoShow we need an outer modal component -->
{{?it.moreInfoBtn && !it.moreInfoBtn.outOfTabsLayout}}
<div id="{{=it.moreInfoBtn.popupID}}" class="modal large">
    <div class="modal-content"> </div>
    <div class="modal-footer">
        <a class="btn modal-close client-colors button2">
            <i class="small material-icons right">close</i>{{=it.moreInfoBtn.close}}
        </a>
    </div>
</div>
{{?}}