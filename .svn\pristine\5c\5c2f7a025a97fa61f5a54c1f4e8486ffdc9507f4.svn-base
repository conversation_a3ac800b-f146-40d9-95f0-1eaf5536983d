<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC_R3" layout="../../../layout/tabsLayoutX2">
  <Include name="HeaderFAC_R3"></Include>


  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{R3_DebriefPage_FAC_Header}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{R3_DebriefPage_FAC_tab1}",
          "!{R3_DebriefPage_FAC_tab2}",
          "!{R3_DebriefPage_FAC_tab3}",
          "!{R3_DebriefPage_FAC_tab4}",
          "!{R3_DebriefPage_FAC_tab5}",
          "!{R3_DebriefPage_FAC_ranking}"
      ],
      scope: null
  }]]></Component>

      

<!-- ******* -->
<!-- EVENT 1 -->
<!-- ******* -->    
  <Include name="R3_Debrief_Scenario1_chart"></Include>  
  <Include name="R3_Debrief_Scenario1_results"></Include>

<!-- ******* -->
<!-- EVENT 2 -->
<!-- ******* -->    
  <Include name="R3_Debrief_Scenario2_chart"></Include>  
  <Include name="R3_Debrief_Scenario2_results"></Include>


<!-- ******* -->
<!-- EVENT 3 -->
<!-- ******* -->    
  <Include name="R3_Debrief_Scenario3_chart"></Include>  
  <Include name="R3_Debrief_Scenario3_results"></Include>
  
<!-- ******* -->
<!-- EVENT 4 -->
<!-- ******* -->    
  <Include name="R3_Debrief_Scenario4_chart"></Include>  
  <Include name="R3_Debrief_Scenario4_results"></Include>

<!-- ******* -->
<!-- EVENT 5 -->
<!-- ******* -->    
  <Include name="R3_Debrief_Scenario5_chart"></Include>  
  <Include name="R3_Debrief_Scenario5_results"></Include>


<!-- ******* -->
<!-- RANKING -->
<!-- ******* -->

  <Include name="SIM_Ranking_included_R3"></Include>

  
  
  
  
  <!-- EXPORT CSV BUTTON (adding empty component to get the Export button out of the tabs layout which goes in pairs)-->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css"    
    }]]>
  </Component>
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "bounceInUp",
      id: "btn_export",
      isHidden: false, _showDelay: 3000,
      title: "!{GroupDirector_ExportBtn_R3}",
      icon: "cloud_download",
      onclick: "",
      pulse: true,
      color: "aux1",
      modal: {
        modalID: "modal-export",
        header: "!{GroupDirector_Export_Modal_Title}",
        text: "!{GroupDirector_Export_Modal_Text}",
        close: "!{GroupDirector_Export_Modal_Close}",
        action: "!{GroupDirector_Export_Modal_Action}",
        onclick: "",
        onclickFunction: "dataExport",
        onclickQuestion: "GD",
        onclickQuestion2: "Data Report SIM-R3"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



  <Include name="ScrollToButton"></Include>



  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - showDelay: show the hidden button after Xms (waiting the animation)
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "center",
      animate: "zoomIn animate__delay-2s",
      id: "btn_navigation_home",
      _isHidden: true, _showDelay: 1000,
      hasModals: true,
      _buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "R3_LandingPage",
          targetSection: "R3_LandingPage_FAC",
          label: "!{GD_R3_LandingPage}",
          icon: "screen_share"
        }
      ],
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>




</Action>