﻿define(['jsCalcLib/numberFormatting'], function (numberFormatting) {
    function spanBox(options) {
        var span = options.el.find('span');
        var value = null;
        if (span.length === 0) {
            // create the input element
            span = $('<span></span>');
            options.el.append(span);
            //$("body").append(span);
        }
        if (value === null) {
            value = getValue(options);
        }
        span.html((value ? value.toString() : ""));

    }

    function getValue(options) {
        var value = options.value;

        try {
            //Determine if there's a period
            if (options.dataBinder.dataBinderOptions._attributes.period) {
                var period = options.dataBinder.dataBinderOptions._attributes.period;
                if (period && options.value && options.value[0]) {
                    if (period < options.value[0].length) {
                        value = options.value[0][period];
                    }
                }
            }
        } catch (err) {
            //Could not access a period.
        }

        if (options.renderOptions) {
            var units = options.renderOptions.units ? parseFloat(options.renderOptions.units.value) : 1;
            units = units ? units : 1;
            if (options.renderOptions.numberFormat) {
                if (options.renderOptions.numberFormat.value === 'String') {
                    value = value;
                }
                else {
                    value = numberFormatting.format(options.value, options.renderOptions.numberFormat.value, units);
                }
                if (options.renderOptions.numberFormat.prefix)
                    value = options.renderOptions.numberFormat.prefix + " " + value;
                if (options.renderOptions.numberFormat.suffix)
                    value = value + " " + options.renderOptions.numberFormat.suffix;
            }
        }
        return value;
    }

    return spanBox;
});


