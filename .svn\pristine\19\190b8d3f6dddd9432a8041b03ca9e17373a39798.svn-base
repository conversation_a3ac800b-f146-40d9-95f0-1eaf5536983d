@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";



.wizlet.wizletTableInputs .container.table {
    
    $color: color("client-colors", "border");
    $color2: rgba(color("client-colors", "secondary"), 0.15);
    
    .card-content {
        
        background-color: color("client-colors", "white");
        display: grid;

        @include card-title; 
        .card-title {
            margin-bottom: 20px;
        }

        table {
            
            border-collapse: collapse;
            border: none;
            
            &.striped {
                tbody {
                    tr:nth-child(odd) {
                        background-color: $color2;
                    }
                }
            }

            thead {
                tr {
                    border-bottom: 2px solid $color;
                }
                tr.empty {
                    border: none;
                }

                &.headerColor {
                    tr th {
                        border-radius: 20px 20px 0 0;
                        border: 2px white solid;
                    }
                }
                th {
                    padding: 5px 5px;
                    text-align: center;
                }
            }
            tbody {
                tr {
                    pointer-events: none;
                    &> td:not(:first-child) {
                        border-left: 2px solid $color;
                    }
                    td {
                        padding: 5px 5px;
                        vertical-align: middle;
                        &.bold {
                            font-weight: bold;
                        }
                        &.big {
                            font-size: 150%;
                        }
                        ol,ul {
                            padding: 0 0 0 20px;
                            font-size: 90%;
                            text-align: left;
                            li span {
                                display: inline-table;
                                margin-left: -10px;
                            }
                        }
                        .textImage {
                            align-items: center;
                            display: flex;
                            justify-content: center;
                            img {
                                max-width: 4rem;
                                max-height: 4rem;
                                object-fit: contain;
                                margin-right: 1rem;
                            }
                        }
                        .input-cell{
                            white-space: nowrap;
                        }

                        .input-field {
                            margin: 0;
                            display: inline-block;
                            >i {
                                position: absolute;
                                height: 3rem;
                                line-height: 3rem;
                                left: -1rem;     
                                @media #{$small-and-down} { 
                                    display: none;
                                }  
                            }
                            >input {
                                pointer-events: auto;
                                &[type="number"]::-webkit-inner-spin-button, 
                                &[type="number"]::-webkit-outer-spin-button {                                  
                                    -webkit-appearance: "Always Show Up/Down Arrows"; 
                                    opacity: 1;
                                    margin-left: -20px;                               
                                }    
                            }
                            span.suffix {
                                position: absolute;
                                top: 0.75rem;
                                right: 1rem;
                            }

                            &.disabled {
                                >i {
                                    display: none;
                                }
                                >input {
                                    opacity: 1;
                                    pointer-events: none;
                                    filter: grayscale(100%);
                                    &[type="number"]::-webkit-inner-spin-button, 
                                    &[type="number"]::-webkit-outer-spin-button {                                  
                                        display: none;                        
                                    }    
                                }
                            }
                        }
                    }
                }
                tr td{
                    @media #{$small-and-down} {
                        font-size: 90%;
                    }
                }
            }
            caption {
                display: inline-block;
                margin-top: 10px;
                text-align: left;
                font-family: clientLight;
                font-size: 80%;
            }
            

            &.bigHeaders thead {
                th {
                    @media #{$medium-and-up} {
                        font-size: 120%;
                    }
                }
            }

            &.smallHeaders thead {
                th {
                    font-size: 90%;
                }
            }

            &.responsive-table thead {
                tr {
                    @media #{$medium-and-down} {
                        border-bottom: none;
                        border-right: 2px solid $color;
                    }
                }
            }

            &.fixed {
                table-layout: fixed;
            }

            &.firstColumnBold {
                tbody {
                    tr {
                        &> td:first-child {
                            font-weight: bold;
                            font-size: 110%;
                        }
                    }
                }
            }
            &.lastColumnBold {
                tbody {
                    tr {
                        &> td:last-child {
                            font-weight: bold;
                            font-size: 110%;
                        }
                    }
                }
            }

        }
        

    }
}
  