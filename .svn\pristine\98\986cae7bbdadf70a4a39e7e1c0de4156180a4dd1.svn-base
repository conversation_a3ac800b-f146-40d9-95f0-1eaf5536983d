<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutFAC" layout="../../../layout/tabsLayoutDebriefFAC">
  <Include name="HeaderFAC"></Include>
  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{R1_Debrief_Scenario5_Header}",
      subheader: "!{R1_Debrief_Scenario5_Text}",
      valign: false,
      animate: "fadeIn",
      content: {
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{}",
        body: "!{}"
      }      
    }]]>
  </Component>

  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{} ",
      tabs: [
          "!{Header_LinkResults}",
          "!{Header_LinkChoices}"
      ],
      scope: null
  }]]></Component>
  
  
  <Include name="R1_Debrief_Scenario5_chart"></Include>
  <Include name="R1_Debrief_Scenario5_table"></Include>


  <!-- Next Tab Button  -->
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "zoomIn",
      id: "btn_action1", isHidden: false, 
      title: "!{Header_LinkChoices}",
      icon: "redo",
      onclick: "$(this).removeClass('pulse'); $('#scrollToButton > .submit > a')[0].click(); $('a[href=&quot;#tabPanel1&quot;')[0].click()",
      pulse: false,
      color: "",
      
      _isFollower: "Follower",
      scope: [""]
    }]]>
  </Component> 
  

  <Include name="R1_Debrief_Scenario5_results"></Include>





  <Component type="Collapsible" customJS="true">
    <![CDATA[{
      templateInEvent: "html/collapsible.dot",
      css: "styles/collapsible.css",
      animate: "fadeInLeft animate__delay-2s",
      accordion: true,
      popout: false,
        expand_more: "expand_more",
        expand_less: "expand_less",
      items: [
        {
          active: false,
          title: "!{R1_Debrief_Scenario5_FACtit}", 
          text: "!{R1_Debrief_Scenario5_FACtext}",
          _texts: [
            { title: "!{}", text: "!{}" },
            { title: "!{}", text: "!{}" } 
          ]
        }
      ]
    }]]>
  </Component> 


  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left clicked",
          gdActionTrack: "GD", gdActionSection: "R1_Debrief_Scenario5",
          icon: "replay"
        },
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left2",
          gdActionTrack: "GD", gdActionSection: "LandingPage_Pause",
          icon: "pause_circle_filled"
        },
        {
          type: "target",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R1_Debrief_Scenario3",
          targetSection: "R1_Debrief_Scenario3_FAC",
          label: "!{R1_Debrief_Scenario3}",
          icon: "looks_3"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list",
          isFloat: false, 
          tooltip: "!{}"
        },
        {
          type: "target",
          pulse: true,
          gdActionEmbed: "SIM_R1_AGG",
          gdActionTrack: "GD",
          gdActionSection: "R1_Debrief_Ranking",
          targetSection: "R1_Debrief_Ranking_FAC",
          label: "!{R1_Debrief_Ranking}",
          icon: "emoji_events"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_FAC_Navigation_Tab" response="1"/> 
  </Voting>

</Action>