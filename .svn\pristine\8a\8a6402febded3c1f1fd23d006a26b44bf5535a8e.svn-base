<?xml version="1.0" encoding="utf-8" ?>
<Action>
    
  <Component type="Header" wizletName="Header_Intro" customJS="true"><![CDATA[{
    templateInEvent: "html/header.dot",
    css: "styles/header.css",
    logo:   {
        src: "!{Header_logo}",
        alt: "logo"
    },
    user: {
      background: "",
      avatar: "!{Header_logo_menu}",
      my_avatar: "Q_My_Avatar"
    },
    
    myName: "Q_My_Name",
    myTeam: "",
      
    follower_suffix: " - !{Header_Follower}",
    trackTeam: "Team",
    isFollower: "Follower",

    scope: ["Q_My_Name","Q_My_Avatar","Follower"],

    links: [
      {
        id: "link_exit",
        title: "!{Header_LinkExit}",
        icon: "exit_to_app",
        modalID: "modal-logout"
      }
    ],
    
    close: "!{Header_LinkClose}",
    sectionsID: [ ],

    _help: {
      modalID: "modal-help",
      text: "!{Header_Modal_Help_Text}",
      close: "!{Header_LinkClose}"
    },

    logout: {
      modalID: "modal-logout",
      header: "!{Header_Modal_Logout_Title}",
      text: "!{Header_Modal_Logout_Text}",
      close: "!{Header_Modal_Logout_Close}",
      logout: "!{Header_Modal_Logout_Logout}",
      onclick: "logout()"
    }    
    
  }]]></Component>


  <Component type="Vanilla" wizletName="Footer"><![CDATA[{
    templateInEvent: "html/footer.dot",
    css: "styles/footer.css",
    footer: {
      color: "navheader",
      _banner: "!{Footer_Img}",
      _logo: {src:"", alt:""},
      title: "!{Footer_Title}",
      subtitle: "!{Footer_Subtitle}",
      copyright: "", link: ""
    }
  }]]></Component>
  

</Action>