<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <Component type="Gauges" wizletName="KPIgauges_R3" customJS="true">
    <![CDATA[{
			templateInEvent: "html/gauges.dot",
			css: "styles/gauges.css",
			animate: "zoomIn",

      stickyOnTop: false,
      resize: false,

			totals: [
				{
					label: "!{KPI_TOTAL}",
					binding: "Score_SIM_Total_R3",
					_maxscore: "!{KPI_Total_Max}",
					_maxscoresep: "!{KPI_Total_Max_sep}"
				}
			],
			dynamic: "true",

      gauges:[
        {
          binding: "Score_SIM_Total_R3_KPI1",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_Metric1}"  },
          initMarker: !{KPI_Metric1_Init},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_Metric1_Min},
            max: !{KPI_Metric1_Max},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R3_KPI2",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_Metric2}"  },
          initMarker: !{KPI_Metric2_Init},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_Metric2_Min},
            max: !{KPI_Metric2_Max},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R3_KPI3",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_Metric3}"  },
          initMarker: !{KPI_Metric3_Init},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_Metric3_Min},
            max: !{KPI_Metric3_Max},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R3_LTUs_C1",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_LTU_C1}"  },
          initMarker: !{KPI_LTU_Init_C1},
          yAxis: {
            stops:[
              [0, '#ff5722'],
              [0.49, '#ff5722'],
              [0.5, '#ffc400'],
              [0.51, '#9ccc65'],
              [1, '#9ccc65']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_LTU_Min_C1},
            max: !{KPI_LTU_Max_C1},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        }
      ],

      
			bindings: {
				"Score_SIM_Total_R3": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3",
          	trackQuestion: "Team",          			
					render: "renderer/spanBox",
					renderOptions: {
						numberFormat: {
							value: "0.0"
						}
					}
				},
				"Score_SIM_Total_R3_KPI1": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_KPI1",
          	trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R3_KPI2": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_KPI2",
          	trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R3_KPI3": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_KPI3",
          	trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				},
				"Score_SIM_Total_R3_LTUs_C1": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_LTUs_C1",
          	trackQuestion: "Team",     
					render: "events/JM_ProjectManagement_MBS_2025/js/renders/spanBox",
					renderOptions: {
						numberFormat: {
							value: "##0"
						}
					}
				}
			},

      _modal_details: {
        id: "KPIgauges_LTUs_R3",
        icon: "timer",
        close: "!{Header_LinkClose}"
      },
      
      trackTeam: "Team",
      isFollower: "Follower",

			scope: [
        "Follower",
        "Score_SIM_Total_R3",
        "Score_SIM_Total_R3_KPI1",
        "Score_SIM_Total_R3_KPI2",
        "Score_SIM_Total_R3_KPI3",
        "Score_SIM_Total_R3_LTUs_C1"
      ]
		}]]>
  </Component>


</Action>