<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutFAC">
  <Include name="HeaderFAC"></Include>
  
  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      header: "!{R2_Debrief_Ranking_Header}",
      subheader: "!{R2_Debrief_Ranking_Title}",
      instructions: "!{R2_Debrief_Ranking_Text}"
    }]]>  
  </Component>
  <Include name="SIM_Ranking_included"></Include>

  

  <!-- Scroll To Top Button -->
  <Include name="ScrollToButton"></Include>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left clicked",
          gdActionTrack: "GD", gdActionSection: "R2_Debrief_Ranking",
          icon: "replay"
        },
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left2",
          gdActionTrack: "GD", gdActionSection: "LandingPage_Pause",
          icon: "pause_circle_filled"
        },
        {
          type: "target",
          pulse: false,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R2_Debrief_Scenario3",
          targetSection: "R2_Debrief_Scenario3_FAC",
          label: "!{R2_Debrief_Scenario3}",
          icon: "looks_3"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list",
          isFloat: false, 
          tooltip: "!{}"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>