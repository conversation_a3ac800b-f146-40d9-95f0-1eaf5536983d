<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "",
      header: "!{SIM_Initiatives_Header}",
      instructions: "!{}",
      valign: false,
      animate: "fadeInUp",
      content: {
        title: "!{}",
        body: "!{}"
      },

      feedbackType: "multi2",
      feedbackOptions: [
        {
          bind: "Q_SIM_R1_Initiatives_1",
          label: "!{Badge_Opt1}",
          title: "!{SIM_Initiatives_Opt1}",
          text: "!{SIM_Initiatives_Des1}<br><br>!{SIM_Initiatives_Opt1_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_2",
          label: "!{Badge_Opt2}",
          title: "!{SIM_Initiatives_Opt2}",
          text: "!{SIM_Initiatives_Des2}<br><br>!{SIM_Initiatives_Opt2_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_3",
          label: "!{Badge_Opt3}",
          title: "!{SIM_Initiatives_Opt3}",
          text: "!{SIM_Initiatives_Des3}<br><br>!{SIM_Initiatives_Opt3_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_4",
          label: "!{Badge_Opt4}",
          title: "!{SIM_Initiatives_Opt4}",
          text: "!{SIM_Initiatives_Des4}<br><br>!{SIM_Initiatives_Opt4_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_5",
          label: "!{Badge_Opt5}",
          title: "!{SIM_Initiatives_Opt5}",
          text: "!{SIM_Initiatives_Des5}<br><br>!{SIM_Initiatives_Opt5_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_6",
          label: "!{Badge_Opt6}",
          title: "!{SIM_Initiatives_Opt6}",
          text: "!{SIM_Initiatives_Des6}<br><br>!{SIM_Initiatives_Opt6_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_7",
          label: "!{Badge_Opt7}",
          title: "!{SIM_Initiatives_Opt7}",
          text: "!{SIM_Initiatives_Des7}<br><br>!{SIM_Initiatives_Opt7_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_8",
          label: "!{Badge_Opt8}",
          title: "!{SIM_Initiatives_Opt8}",
          text: "!{SIM_Initiatives_Des8}<br><br>!{SIM_Initiatives_Opt8_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_9",
          label: "!{Badge_Opt9}",
          title: "!{SIM_Initiatives_Opt9}",
          text: "!{SIM_Initiatives_Des9}<br><br>!{SIM_Initiatives_Opt9_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_10",
          label: "!{Badge_Opt10}",
          title: "!{SIM_Initiatives_Opt10}",
          text: "!{SIM_Initiatives_Des10}<br><br>!{SIM_Initiatives_Opt10_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_11",
          label: "!{Badge_Opt11}",
          title: "!{SIM_Initiatives_Opt11}",
          text: "!{SIM_Initiatives_Des11}<br><br>!{SIM_Initiatives_Opt11_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_12",
          label: "!{Badge_Opt12}",
          title: "!{SIM_Initiatives_Opt12}",
          text: "!{SIM_Initiatives_Des12}<br><br>!{SIM_Initiatives_Opt12_FB}"
        },
        {
          bind: "Q_SIM_R1_Initiatives_13",
          label: "!{Badge_Opt13}",
          title: "!{SIM_Initiatives_Opt13}",
          text: "!{SIM_Initiatives_Des13}<br><br>!{SIM_Initiatives_Opt13_FB}"
        }
      ],
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: [
          "!{KPI_Metric1}",
          "!{KPI_Metric2}",
          "!{KPI_Metric3}"
        ],
        fromDB: true,
        kpi_scores: [],
        kpi_scoresDB: [
          "Score_SIM_R1_Initiatives_KPI1",
          "Score_SIM_R1_Initiatives_KPI2",
          "Score_SIM_R1_Initiatives_KPI3"
        ],
        lastBold: false,
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "",
        value: ""
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower",
              "Score_SIM_R1_Initiatives_KPI1", 
              "Score_SIM_R1_Initiatives_KPI2", 
              "Score_SIM_R1_Initiatives_KPI3",
              "Q_SIM_R1_Initiatives_1",
              "Q_SIM_R1_Initiatives_2",
              "Q_SIM_R1_Initiatives_3",
              "Q_SIM_R1_Initiatives_4",
              "Q_SIM_R1_Initiatives_5",
              "Q_SIM_R1_Initiatives_6",
              "Q_SIM_R1_Initiatives_7",
              "Q_SIM_R1_Initiatives_8",
              "Q_SIM_R1_Initiatives_9",
              "Q_SIM_R1_Initiatives_10",
              "Q_SIM_R1_Initiatives_11",
              "Q_SIM_R1_Initiatives_12",
              "Q_SIM_R1_Initiatives_13"]
    }]]>
  </Component>





  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario1_Title}",
        body: "!{SIM_R1_Scenario1_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario1_Opt1_KPI1}","!{SIM_R1_Scenario1_Opt1_KPI2}","!{SIM_R1_Scenario1_Opt1_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1",
        condition_Diff: "2",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario1_Title}",
        body: "!{SIM_R1_Scenario1_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario1_Opt2_KPI1}","!{SIM_R1_Scenario1_Opt2_KPI2}","!{SIM_R1_Scenario1_Opt2_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario1",
        condition_Diff: "3",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario1_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario1_Title}",
        body: "!{SIM_R1_Scenario1_FB_Opt3_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario1_Opt3_KPI1}","!{SIM_R1_Scenario1_Opt3_KPI2}","!{SIM_R1_Scenario1_Opt3_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario1",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>




  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario2_Header}",
      valign: false,
      animate: "fadeIn",
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>
  <Component type="Prioritize" customJS="true"><![CDATA[{
    css: "styles/prioritize.css",
    "header": {
      "title": "!{}",
      "description": "!{SIM_R1_Scenario2_FB_Text}"
    },
    "type": "QUESTIONS",
    "readOnlyCondition": true,
    "isFollower": "Follower",
    "trackTeam": "Team",
    "version": "2.0",
    "questions": [
      {
        "binding": "Q_SIM_R1_Scenario2_Opt3",
        "title": "!{SIM_R1_Scenario2_Opt3}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt4",
        "title": "!{SIM_R1_Scenario2_Opt4}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt6",
        "title": "!{SIM_R1_Scenario2_Opt6}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt2",
        "title": "!{SIM_R1_Scenario2_Opt2}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt1",
        "title": "!{SIM_R1_Scenario2_Opt1}"
      },
      {
        "binding": "Q_SIM_R1_Scenario2_Opt5",
        "title": "!{SIM_R1_Scenario2_Opt5}"
      }
    ],
    "buttons": [
      {
        "navigation": {
          "title": "!{Navigation_submit}",
          "history": false,
          "message": "!{InputSubmited}",
          "idToShow": "navButton"
        }
      },
      {
        "navigation": {
          "title": "!{Navigation_submitModalClose}",
          "history": false
        }
      }
    ],
    "scope": [
      "Q_SIM_R1_Scenario2_Opt1",
      "Q_SIM_R1_Scenario2_Opt2",
      "Q_SIM_R1_Scenario2_Opt3",
      "Q_SIM_R1_Scenario2_Opt4",
      "Q_SIM_R1_Scenario2_Opt5",
      "Q_SIM_R1_Scenario2_Opt6",
      "Follower"
    ]
  }]]></Component>






  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario3",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario3_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario3_Title}",
        body: "!{SIM_R1_Scenario3_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario3_Opt1_KPI1}","!{SIM_R1_Scenario3_Opt1_KPI2}","!{SIM_R1_Scenario3_Opt1_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario3",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario3"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario3",
        condition_Diff: "2",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario3_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario3_Title}",
        body: "!{SIM_R1_Scenario3_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario3_Opt2_KPI1}","!{SIM_R1_Scenario3_Opt2_KPI2}","!{SIM_R1_Scenario3_Opt2_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario3",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario3"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario3",
        condition_Diff: "3",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario3_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario3_Title}",
        body: "!{SIM_R1_Scenario3_FB_Opt3_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario3_Opt3_KPI1}","!{SIM_R1_Scenario3_Opt3_KPI2}","!{SIM_R1_Scenario3_Opt3_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario3",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario3"]
    }]]>
  </Component>






  <!-- <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario4",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario4_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "Wobbler",
        body: "!{SIM_R1_Scenario4b_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario4b_Opt1_KPI1}","!{SIM_R1_Scenario4b_Opt1_KPI2}","!{SIM_R1_Scenario4b_Opt1_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario4",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario4"]
    }]]>
  </Component> -->
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario4",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario4_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario4_Title}",
        body: "!{SIM_R1_Scenario4_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario4_Opt1_KPI1}","!{SIM_R1_Scenario4_Opt1_KPI2}","!{SIM_R1_Scenario4_Opt1_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario4",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario4"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario4",
        condition_Diff: "2",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario4_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario4_Title}",
        body: "!{SIM_R1_Scenario4_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario4_Opt2_KPI1}","!{SIM_R1_Scenario4_Opt2_KPI2}","!{SIM_R1_Scenario4_Opt2_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario4",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario4"]
    }]]>
  </Component>






  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario5",
        condition_Diff: "1",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario5_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario5_Title}",
        body: "!{SIM_R1_Scenario5_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario5_Opt1_KPI1}","!{SIM_R1_Scenario5_Opt1_KPI2}","!{SIM_R1_Scenario5_Opt1_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario5",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario5"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario5",
        condition_Diff: "2",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario5_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario5_Title}",
        body: "!{SIM_R1_Scenario5_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario5_Opt2_KPI1}","!{SIM_R1_Scenario5_Opt2_KPI2}","!{SIM_R1_Scenario5_Opt2_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario5",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario5"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      isHidden: true,
        condition: "Q_SIM_R1_Scenario5",
        condition_Diff: "3",
      orientation: "vertical",
      header: "!{SIM_R1_Scenario5_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario5_Title}",
        body: "!{SIM_R1_Scenario5_FB_Opt3_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R1_Scenario5_Opt3_KPI1}","!{SIM_R1_Scenario5_Opt3_KPI2}","!{SIM_R1_Scenario5_Opt3_KPI3}"],
        inverseIcon: [false,false,false],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R1_Scenario5",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario5"]
    }]]>
  </Component>

</Action>