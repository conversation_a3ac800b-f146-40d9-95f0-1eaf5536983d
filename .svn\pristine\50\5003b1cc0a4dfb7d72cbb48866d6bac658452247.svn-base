@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";

.wizlet.wizletVanilla {

    .card.video {

        .card-content {

            @include card-title; 

            
            @media #{$medium-and-down} {
                padding: 12px;
            }

            
            .video-box {
                padding-top: 12px;
                text-align: center;

                video.responsive-video {
                    width: 100%;
                    max-width: 80vw;
                    max-height: 60vh;
                }
            }

        }

        .card-image {
            max-width: 25%;
            padding: 10px;
            
            min-width: 40%;
            max-width: 60%;
            
            @media #{$medium-and-up} {
                min-width: 20%;
                max-width: 30%;
            }

            display: flex;
            img { margin: auto; }
        }
    }

    
}