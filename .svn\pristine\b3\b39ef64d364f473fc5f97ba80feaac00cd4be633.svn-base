@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";


.wizlet.wizletVanilla .container.flipcard {

    padding: 0;

    .row.no-padding {
        margin: 10px -10px;
    }

    .row.is-flex {
        @include flexbox();
        @include flex-wrap();
    
        & > .col {
            @include flexbox();
            @include flex-direction(column);
            margin: auto;
        }
    }

    .container > .intro {
        padding-bottom: 1rem;
        margin-top: 1rem;
    }
    

    .card {

        .badge.header-label {
            z-index: 2;
            position: absolute;
            left: 0;
            top: 0;
            background-color: color("client-colors", "secondary");
            min-height: 2rem;
            font-size: 1.5rem;
            line-height: 2rem;
            margin: 0;
        }

        .card-image {
            min-width: 30%;
            max-height: 75%;
        }


        .card-content {
            @include card-title(120%);
            .card-title {         
                // line-height: 2rem;
                word-break: break-word;                
                @media #{$small-and-down} { 
                    font-size: 20px;  
                }
            }            
        }

        
        &.disabled {
            pointer-events: none;
            filter: grayscale(100%);
        }


        &.flipable {        
                     
            // min-height: 220px;
            // @media #{$large-and-up} {
            //     min-height: 450px;
            // }
            // @media #{$extra-large-and-up} {
            //     min-height: 380px;
            // }
            perspective: 500px;
            background-color: transparent;
            box-shadow: none;

            .card-content {
                position: absolute;
                padding: 0;   
                line-height: 120%;
                text-align: left;
                width: 100%;
                height: 100%;
                box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.12), 0 1px 5px 0 rgba(0, 0, 0, 0.2);
            
                transition: transform 1s;
                transform-style: preserve-3d;
                
                .front,
                .back {
                    position: absolute;
                    padding: 10px;
                    height: 100%;
                    width: 100%;
                    color: color("client-colors", "font");
                    background: color("client-colors", "white");
                    backface-visibility: hidden;
                    &.image {
                        img {
                            max-height: calc(100% - 35px);
                            max-width: 100%;
                        }
                        
                        &.full {
                            display: flex;
                            img {
                                width: 100%;
                                max-height: 100%;
                                margin: auto;
                            }
                        }
                        .card-title, .card-body {
                            background-color: rgba(255,255,255,0.85);
                            padding: 5px;
                        }
                        .card-title {
                            padding-top: 0;
                            padding-bottom: 0;
                        }
                    }
                    
                    &.isFlex {
                        display: flex;
                        .card-body {
                            margin:auto;
                        }
                    }
                }

                .front {
                    text-align: center;
                    .card-title {
                        text-align: center;
                    }
                }



                .back {
                    overflow-y: auto;
                    min-height: 100%;
                    padding: 0 10px 10px 10px;
                    color: color("client-colors", "font2");
                    background: color("client-colors", "secondary");
                    transform: rotateY( 180deg );            
                    @media #{$small-and-down} { 
                        .card-body {
                            font-size: 90%;  
                        }
                    }
                }
            }

        }
        
        &.flipable:hover .card-content {
            transform: rotateY( 180deg ) ;
            transition: transform 0.5s;
        }

    }


}