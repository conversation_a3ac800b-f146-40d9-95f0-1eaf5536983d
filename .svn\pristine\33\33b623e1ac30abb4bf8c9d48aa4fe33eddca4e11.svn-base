<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro_SIM">
  <Include name="Header_Intro"></Include>


  <Component type="FormInputs" customJS="true">
    <![CDATA[{
      templateInEvent: "html/formInputs.dot",
      css: "styles/formInputs.css",
      id: "nameForm", 
      animate: "fadeIn",
      header: "!{TeamName_Header}",
      instructions: "",
      
      valign: false,
      orientation: "",
      content: {
        title: "!{}",
        body: "!{TeamName_Body}"
      },  

      remaining: "!{InputRemaining}",
      inputRight: "!{InputRight}",
      inputWrong: "!{InputWrong}",
      
      myName: "Q_My_Name",

      inputs: [
        {
          name: "name",
          type: "text", required: true,
          bind: "Q_My_Name",
          label: "!{TeamName_Placeholder}",
          length: "!{TeamName_length}",
          icon: "person",
          hideRemaining: true,
          helper: false,
          grid: "s12"
        },
        {
          name: "avatar",
          type: "select", required: true,
          bind: "Q_My_Avatar",
          label: "!{TeamName_Avatar}",
          icon: "face",
          grid: "s12",
          title: "!{TeamName_Avatar_select}",
          json: "/content/avatars.json",
          icons: "/images/avatar/",
          helper: false,
          options: []
        }
      ],

      submitBtn: {
        clearAfter: false,
        disabled: true,
        label: "!{InputForm}",
        toast: "!{InputForm_OK}",
        showId: "navButton",
        _showIds: "#introImg, #navButton",
        _hideIds: "#nameForm",
        targetSection: ""
      },
      
      follower_bind: "Q_My_Name",
      follower_suffix: " - !{Header_Follower}",
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower"]
      
    }]]>
  </Component> 

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      id: "introImg", isHidden: true,
      orientation: "vertical",
      header: "!{TeamName_ImgHeader}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{TeamName_Img}",  alt: "!{}" ,
          position: "right", style: "",
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{TeamName_Img}",  alt: "!{}" ,
          isHiddenWhenSmall: false, 
          src_vert: "!{}"
        },
        position: "up down",
        animate: "fadeInUp",
        title: "!{}",
        body: "!{}"
      }      
    }]]>
  </Component>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-0s",
      id: "navButton",
      isHidden: true, _showDelay: "3000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_start}",
          tooltip: "!{Navigation_start_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>
  


  <Include name="Init_Score"></Include>  

  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{GD_TeamName}"/> 
  </Voting> 


</Action>