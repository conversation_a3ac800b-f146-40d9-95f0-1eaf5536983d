
<div class="container {{?it.animate}}animate__animated animate__{{=it.animate}}{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}}>

<div class="row {{?it.aligned}}{{=it.aligned}}{{??}}left{{?}}">

    <div class="card countDownTimerHolder client-colors-text text-font2">
        <i class="small material-icons left">av_timer</i>
        {{?it.leadText}}
            <span class="countDownleadText">{{=it.leadText}}</span>
        {{?}}
        
        <span class="countDownTimer" data-name="{{?it.timeUpText}}{{=it.timeUpText}}{{??}}time is up!{{?}}" data-timeinms="{{=it.timeInMs}}">{{=it.time}}</span>
    </div>

</div>
{{?it.actionEmbed}}
<div class="embeddingWrapper" id="embedding-timer" data-embed></div>
{{?}} 
</div>