﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', './datatables.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var Table = function () {
        this.type = 'Table';
        this.level = 1;
        
        this.langDefaults = {
            sProcessing:     '',
            sLengthMenu:     '',
            sZeroRecords:    '',
            sEmptyTable:     '',
            sInfo:           '',
            sInfoEmpty:      '',
            sInfoFiltered:   '',
            sInfoPostFix:    '',
            sSearch:         '',
            sUrl:            '',
            sInfoThousands:  '',
            sLoadingRecords: '',
            oPaginate: {
                sFirst:    '',
                sLast:     '',
                sNext:     '<i class="material-icons">arrow_forward</i>',
                sPrevious: '<i class="material-icons">arrow_back</i>'
            },
            oAria: {
                sSortAscending:  '',
                sSortDescending: ''
            }
        }
        
    };

    Table.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.votesBeforeUpdate = [];

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Table.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };


    Table.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            

            // ********** DATA-TABLES ********************
            if (self.wizletInfo.isDataTables) {
                var dataTablesLang = self.wizletInfo.dataTablesLang ? self.wizletInfo.dataTablesLang : {};

                var language = $.extend(true, {}, self.langDefaults, dataTablesLang);


                var order = self.wizletInfo.dataTablesOrder ? self.wizletInfo.dataTablesOrder : [[0, "asc"]];
                var noorder = self.wizletInfo.dataTablesNoOrder ? self.wizletInfo.dataTablesNoOrder : [0,1,2];
                var length = self.wizletInfo.lengthMenu ? self.wizletInfo.lengthMenu : [[-1], [dataTablesLang.lengthAll]];
                
                var DT = self.wizletContext.find('.dataTables').DataTable( {
                    order: order,
                    columnDefs: [
                        {
                            targets: noorder,   //avatar & name
                            orderable: false
                        }
                    ],
                                                
                    language: language,

                    _lengthMenu: [[5, 10, 25, 50, 100, -1], [5, 10, 25, 50, 100, dataTablesLang.lengthAll]],
                    
                    lengthMenu: length

                } );
                
                //Keep first column (position) always the same order from 1 to..
                if (self.wizletInfo.dataTablesFixOrder)
                DT.on( 'order.dt search.dt', function () {
                    DT.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
                        cell.innerHTML = i+1;
                    } );
                } ).draw();
                
                
                if (! self.wizletInfo.lengthMenu) {
                    self.wizletContext.find('.dataTables_wrapper .dataTables_length').hide();
                }
                
                if (! self.wizletInfo.dataTablesHideControls) {
                    self.wizletContext.find('.dataTables_wrapper select').val('5');
                    //self.wizletContext.find('.dataTables_wrapper select').addClass("browser-default");
                    self.wizletContext.find('.dataTables_wrapper select').attr('single',true);
                    self.wizletContext.find('.dataTables_wrapper select').formSelect();
                }

            }

            return true;
        })
        .fail(this.wizerApi.showError);
    };


    
    Table.getRegistration = function () {
        return new Table();
    };

    return Table;

});