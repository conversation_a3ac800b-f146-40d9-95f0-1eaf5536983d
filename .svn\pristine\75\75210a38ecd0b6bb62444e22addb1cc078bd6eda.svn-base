.wizlet.wizletHCWebChart .header.with-label{position:relative;padding-left:2.5rem;margin-bottom:1rem}.wizlet.wizletHCWebChart .header.with-label .badge.header-label{position:absolute;left:0;top:0;background-color:#00aab4;min-width:2rem;min-height:2rem;font-size:1.25rem;font-weight:bold;border-radius:50%;line-height:2rem;margin:0;margin-top:-5px}.wizlet.wizletHCWebChart .card .card-content{padding:6px 12px}.wizlet.wizletHCWebChart .card .card-content .row{margin:0}.wizlet.wizletHCWebChart .highcharts-container>svg{width:100% !important}.wizlet.wizletHCWebChart .highcharts-container .highcharts-label,.wizlet.wizletHCWebChart .highcharts-container .highcharts-axis-labels{color:initial;fill:initial}.wizlet.wizletHCWebChart .highcharts-container .highcharts-xaxis .highcharts-axis-line{stroke-width:0}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-0{fill:#33993c;stroke:#33993c}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-1{fill:#00aab4;stroke:#00aab4}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-2{fill:#5a97f9;stroke:#5a97f9}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-3{fill:#ff6e00;stroke:#ff6e00}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-4{fill:#c7538f;stroke:#c7538f}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-5{fill:#d22f17;stroke:#d22f17}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-6{fill:#33993c;stroke:#33993c}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-7{fill:#00aab4;stroke:#00aab4}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-8{fill:#5a97f9;stroke:#5a97f9}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-9{fill:#ff6e00;stroke:#ff6e00}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-10{fill:#c7538f;stroke:#c7538f}.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-11{fill:#d22f17;stroke:#d22f17}.wizlet.wizletHCWebChart .highcharts-container .highcharts-tooltip table tr,.wizlet.wizletHCWebChart .highcharts-container .highcharts-tooltip table td{padding:0}.wizlet.wizletHCWebChart .highcharts-container .highcharts-area{fill-opacity:.5}.wizlet.wizletHCWebChart .answers.card-panel{padding:8px 12px}.wizlet.wizletHCWebChart .answers.card-panel .btn-text{display:inline-block;vertical-align:sub}