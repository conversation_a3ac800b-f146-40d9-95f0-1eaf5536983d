@charset "UTF-8";

/****      DATATABLES CSS      ****/
@import url("datatables.min.css");

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";



.wizlet.wizletVanilla .container.table,
.wizlet.wizletTable .container.table {
    
    $color: color("client-colors", "border");
    $color2: rgba(color("client-colors", "secondary"), 0.15);
    
    .card {
        
        //applied in the global _card.scss
        //background-color: color("client-colors", "card_background");

        .card-image, .card-content {
            background-color: transparent;
        }

        .card-content {

            @include card-title; 
            .card-title {
                margin-bottom: 20px;
            }

            table {
                
                border-collapse: collapse;
                border: none;
                
                &.striped {
                    tbody {
                        tr:nth-child(odd) {
                            background-color: $color2;
                        }
                    }
                }

                thead {
                    tr {
                        border-bottom: 2px solid $color;
                    }
                    tr.empty {
                        border: none;
                    }

                    &.headerColor {
                        tr th {
                            border-radius: 20px 20px 0 0;
                            border: 2px white solid;
                        }
                    }
                    th {
                        padding: 5px 5px;
                        text-align: center;
                    }
                }
                tbody {
                    tr {
                        pointer-events: none;
                        &> td:not(:first-child) {
                            border-left: 2px solid $color;
                        }
                        td {
                            padding: 5px 5px;
                            vertical-align: top;
                            ol,ul {
                                padding: 0 0 0 20px;
                                font-size: 90%;
                                text-align: left;
                                li span {
                                    display: inline-table;
                                    margin-left: -10px;
                                }
                            }
                        }
                    }
                    tr td{
                        @media #{$small-and-down} {
                            font-size: 90%;
                        }
                    }
                }
                caption {
                    display: inline-block;
                    margin-top: 10px;
                    text-align: left;
                    font-family: clientLight;
                    font-size: 80%;
                }
                

                &.bigHeaders thead {
                    th {
                        @media #{$medium-and-up} {
                            font-size: 120%;
                        }
                    }
                }

                &.smallHeaders thead {
                    th {
                        font-size: 90%;
                    }
                }

                &.responsive-table {
                    @media #{$medium-and-down} {
                        thead tr {
                            border-bottom: none;
                            border-right: 2px solid $color;                            
                        }
                        tbody td {
                            font-size: 90%;
                            border-left: none !important;                              
                        }
                    }
                }

                &.fixed {
                    table-layout: fixed;
                    tbody > tr > td > img {
                        width: 24px;
                        margin-right: 5px;
                    }
                }

                &.firstColumnBold {
                    tbody {
                        tr {
                            &> td:first-child {
                                vertical-align: middle;
                                font-weight: bold;
                                font-size: 110%;
                                // padding: 10px;
                            }
                        }
                    }
                }
                &.align-center {
                    tbody {
                        tr {
                            > td {
                                vertical-align: middle;
                                > img {
                                    margin-bottom: -5px;
                                }
                            }
                        }
                    }
                }

            }


            @include data-table-style;            

        }

    }
}
  