﻿define(['jquery', 'Q', 'WizerModel', 'calcBinderCache', 'connection-status', 'caching', 'eventDefinition', 'logger', 'actionTool', 'votesite', 'textContentEncoder', 'jquery-ui', 'scope', 'moment-timezone'],
    function($, Q, WizerModel, CalcBinderCache, connectionStatus, Caching, EventDefinition, logger, actionTool, votesite, TextContentEncoder, jqueryUI, Scope, moment) {


        Wizer.Api = (function() {

            var Api = function(options) {
                this.context = options.context;
                this.connectionStatus = new Wizer.ConnectionStatus();
                this.offlineRunningResponseId = 0;
                this.history = [];
                this.calcBinderCache = new CalcBinderCache(this);
                this.caching = new Caching(this);
                window.wizerApi = this; // for use from console
                this.progressBars = {};
                this.eventDefinition = new EventDefinition(this);
                this.actionIdWhenLastJumpBack = -1;
                this.qIds = {};
                this.questions = {};
                this.dimensions = {};
                this.ignoreTryNumber = false;
                this.wizerModel = WizerModel.getInstance() || new WizerModel({ wizerApi: this });
            };

            Api.prototype.eventName = function() {
                return Wizer.EventName;
            };

            // does nothing in online mode; in offline mode loads dummy data (wizerDesktop spike)
            Api.prototype.bootstrap = function(offlineEventNameUsedInTest) {
                var self = this;
                if (self.isOffline()) {
                    return Q.fcall(function() {
                            wizerApi.session = self.eventDefinition.authenticate();
                            return self.eventDefinition.loadEvent(offlineEventNameUsedInTest);
                        })
                        .then(function() {
                            return self.eventDefinition.loadWizlets();
                        })
                        // TODO Offline work: Should processor initialisation be done here - after loadWizlet promise??
                        .fail(Wizer.Api.showError);
                } else {
                    $(document).bind('wizer:wizerapi:command:LoadAllQuestions', function() {
                        self.loadQuestions();
                    })
                    $(document).bind('wizer:wizerapi:command:ResetEventConfiguration', function(e, options) {
                        self.resetEventConfiguration(options.eventId);
                    })
                    $(document).bind("wizer:action:QR", function(e, options) {
                        //This event is just for testing sake
                        self.readQROptions(JSON.parse(options));
                    });
                    $(document).bind("wizer:action:submitted", function(e, options) {
                        self.submitAction(options);
                    });
                    return self.loadQuestions()
                        .then(function() {
                            return self.loadDimensions();
                        });
                }
            };

            Api.prototype.loadPublishedAssets = function() {
                var deferred = Q.defer();
                if (Wizer.publishedActionsUrl && !Wizer.actions && !Wizer.editMode) {
                    fetchPublishingDetails().then(function() {
                        deferred.resolve(true);
                    });

                } else {
                    deferred.resolve(true);
                }
                return deferred.promise.then(function() {
                    if (!Wizer.actions) {
                        Wizer.actions = {};
                        return false;
                    }
                    return true;
                });
            };


            Api.prototype.reloadPublishedAssets = function(options) {
                var self = this;
                reloading = Q.defer();
                if (options.cdnId != "false") {
                    Wizer.publishedActionsUrl = Wizer.publishedActionsUrl.replace(new RegExp('(\/events\/' + Wizer.EventName + '-).[0-9]*\/'), '$1' + options.cdnId + '/');
                    logger.log(true, 'New publishedActionsUrl: ' + Wizer.publishedActionsUrl);
                    fetchPublishingDetails().then(function() {
                        logger.log(true, "loaded the new actions from " + Wizer.publishedActionsUrl);
                        reloading.resolve(true);
                    });
                } else {
                    if (Wizer.publishedActionsUrl) {
                        require.undef(Wizer.publishedActionsUrl);
                        fetchPublishingDetails().then(function() {
                            logger.log(true, "loaded the new actions from " + Wizer.publishedActionsUrl);
                            reloading.resolve(true);
                        });
                    } else { // not turning on publishing for users who is running without publishn
                        logger.log(true, "new stuff published, but I am running without publishing, so don't know the url (cannot guess language)");
                        Wizer.actions = {};
                        reloading.resolve(false);
                    }
                }
                reloading.promise.then(function(loadet) {
                    if (loadet) {
                        self.qIds = {};
                        self.questions = {};
                        self.dimensions = {};
                        self.indexQuestions(Wizer.questions);
                        self.indexDimensions(Wizer.dimensions);
                    }
                })
            };

            var fetchPublishingDetails = function() {
                var getting = Q.defer();
                Wizer.loadingPublishedAssests = getting;

                $.getJSON(Wizer.publishedActionsUrl, function(json) {
                    Wizer.actions = json.actions;
                    Wizer.questions = json.questions;
                    Wizer.dimensions = json.dimensions;
                    Wizer.loadingPublishedAssests = null;
                    getting.resolve(true);
                });

                return getting.promise;
            };

            Api.prototype.loadQuestions = function() {
                var self = this;

                if (self.allQuestionsPromise) {
                    return self.allQuestionsPromise;
                }

                self.allQuestionsPromise =
                    self.loadPublishedAssets()
                    .then(function() {
                        if (Wizer.questions) {
                            return Q.fcall(function() { return Wizer.questions; });
                        } else {
                            return Q(AjaxGetJson("Registration", "GetAllQuestions"));
                        }
                    })
                    .then(function(questions) {
                        self.indexQuestions(questions);
                    });
                return self.allQuestionsPromise;
            };

            Api.prototype.loadDimensions = function() {
                var self = this;
                if (self.allDimensionsPromise) {
                    return self.allDimensionsPromise;
                }
                self.allDimensionsPromise =
                    Wizer.dimensions ?
                    Q.fcall(function() { return { dimensions: Wizer.dimensions }; }) :
                    Q(AjaxGetJson("VoteAPI", "GetAllDimensions"));
                self.allDimensionsPromise.then(function(result) {
                    self.indexDimensions(result.dimensions);
                });
                return self.allDimensionsPromise;
            };

            Api.prototype.indexDimensions = function(dimensions) {
                var self = this;
                if (dimensions) {
                    for (var i = 0; i < dimensions.length; ++i) {
                        var d = dimensions[i];
                        self.dimensions[d.key] = d;
                    }
                }
            };

            Api.prototype.loadQuestionsForSubEvent = function(eventName) {
                var self = this;
                var wait = Q.defer();

                if (Wizer.questions) { // published mode already loaded all subevents
                    wait.resolve();
                    return wait.promise;
                }

                if (!self.loadSubEventQuestionsPromise) {
                    self.loadSubEventQuestionsPromise = {};
                }
                self.loadSubEventQuestionsPromise[eventName] = wait.promise;
                var waitingpromise = Q(AjaxGetJson("Registration", "GetAllQuestionsForSubEvents", { eventName: eventName }));
                waitingpromise.then(function(questions) {
                        self.indexQuestions(questions);
                        wait.resolve();
                    })
                    .fail(self.showError)
                    .done();
                return wait.promise;
            };

            Api.prototype.indexQuestions = function(questions) {
                var self = this;
                for (var i = 0; i < questions.length; ++i) {
                    var q = questions[i];
                    self.qIds[q.ShortName] = q.Id;
                    self.qIds[q.ShortName.toLowerCase()] = q.Id;
                    self.questions[q.Id] = q;
                }
                //console.log("indexed questions " + questions.length + " -> " + Object.keys(self.questions).length);
            };

            Api.prototype.selfRegister = function(emailId, name, password, votes, isNew) {
                window.stopPoll = true;
                var ajaxRegister = Q(AjaxGetJson("EventAPI", "SelfRegister", { emailId: emailId, name: name, password: password, votes: votes, isNew: isNew }));
                return ajaxRegister;
            }

            Api.prototype.addComment = function(body) {
                var comment = Q(AjaxGetJson("Comment", "Comment", { body: body }));
                return comment;
            }

            Api.prototype.getComments = function() {
                var comment = Q(AjaxGetJson("Comment", "Comment", {}, null, null, null, "GET"));
                return comment;
            }

            Api.prototype.cancelComment = function(commentId) {
                var comment = Q(AjaxGetJson("Comment", "CancelComment", { commentId: commentId }));
                return comment;
            }

            Api.prototype.getCOComments = function(contentObjectName) {
                var contentObject = Q(AjaxGetJson("Comment", "ContentObjectComments", { contentObjectName: contentObjectName }, null, null, null, "GET"));
                return contentObject;
            }

            Api.prototype.saveCOComments = function(body, status, COId, replyOn) {
                var requestBody = { contentObjectId: COId, replyOnCommentId: replyOn };
                if (body != null)
                    requestBody.body = body;
                if (status != null)
                    requestBody.status = status;

                //{body: inputValue, status: status,  }
                var contentObject = Q(AjaxGetJson("Comment", "SaveContentObjectComment", requestBody, null, null, null, "POST"));
                return contentObject;
            }

            Api.prototype.getAssessmentConfiguration = function(eventId, moduleId) {
                var configuration = Q(AjaxGetJson("Assessment", "GetByEventId", { eventId: eventId, moduleId: moduleId }));
                return configuration;
            }

            Api.prototype.getBrowserConfiguration = function() {
                return Q(AjaxGetJson("ComputerTest", "BrowserConfig", {}, null, null, null, "GET"));
            }
            Api.prototype.getAssessmentMatrix = function(participationId, roundValues, module) {
                var ajaxAssessmentMatrix = Q(AjaxGetJson("Assessment", "AssessmentMatrix", { participationId: participationId, roundValues: roundValues, module: (module ? module.name : null) }));
                return ajaxAssessmentMatrix;
            }

            Api.prototype.getPhoneBankMatrix = function(participationId, entryQuestionName, callForm) {
                return Q(AjaxGetJson("Assessment", "PhoneBankMatrix", { participationId: participationId || Wizer.ParticipationId, entryQuestionName: entryQuestionName, callForm: callForm }));
            }

            Api.prototype.switchEvent = function(eventName, sectionName) {
                window.stopPoll = true;
                var ajaxSwitched = Q(AjaxGetJson("EventAPI", "SwitchEvent", { eventName: eventName, sectionName: (sectionName ? sectionName : null) }));
                var promise = ajaxSwitched.then(function(response) {
                    if (response && response.Success) {
                        //reload votesite.js
                        //console.log('calling reload for votesite');
                        $(document).trigger("wizer:action:votesiteReload");
                    } else {
                        throw "Error while switching event:" + response.message;
                        window.stopPoll = false;
                    }
                });
                ajaxSwitched.fail(function(err) {
                    window.stopPoll = false;
                    var errorText = err;
                    if (err.toString() === "[object Object]") {
                        errorText = JSON.stringify(err);
                    }
                    Wizer.Api.showError(err);
                });
            }

            //        // ToDo: not ready, only use in spike (wizerDesktop)
            //        Api.prototype.authenticate = function () {
            //            if (this.isOffline()) {
            //                //render is invoked with Q.fcall, which turns it into a promise - so render does not need to return a promise itself
            //                return this.session = { participation: { id: 4711} };
            //            } else {
            //                // ...
            //            }
            //        };

            var capitalizeFirstLetter = function(s) {
                return s[0].toUpperCase() + s.substr(1);
            };

            // ToDo: fix recursive dependency between votesite and wizer-api, current solved with votesite.setWizerApi
            Api.prototype.renderShots = function(action, wizerApi) {
                var self = this;
                var voteUrl = '/Wizer/Pages/Voting/Show.aspx?customParameter=' + action.name; // ToDo: consider a different module name, like actionHtml?scriptName=

                // Note: define the html that votesite will go ahead and require from voteUrl
                // ToDo: consider having eventDefinition.js do this, plus set action.voteUrl
                // ToDo: consider using a dot template for this
                var mainWrapper = $('<div></div>').addClass('mainWrapper').addClass(action.name).attr('rel', action.name);
                var actionConfig = {
                    moduleName: action.moduleName,
                    templateName: action.templateName,
                    wizletTypes: action.wizletTypes
                };
                var actionConfigScript = $('<script></script>').attr('type', 'application/json').attr('id', 'actionConfig').text(JSON.stringify(actionConfig));
                actionConfigScript.appendTo(mainWrapper);

                $.each(action.shots, function(shotIndex, shot) {

                    var shotHtml = $('<div></div>').attr('id', 'wizlet' + shot.wizletType + shotIndex).attr('data-wizlet-id', shot.shotId).addClass('wizlet').addClass('wizlet' + shot.wizletType).addClass(shot.wizletType);
                    if (shot.wizletName) {
                        shotHtml.attr('data-wizlet-name', shot.wizletName);
                    }
                    var wizletInfo = shot.wizletInfo;
                    if (typeof wizletInfo === "string") {
                        // Note: this is the case for Data wizlet
                        wizletInfo = JSON.parse(shot.wizletInfo);
                    }
                    var wizletInfoScript = $('<script type="application/json" class="wizletInfo" data-wizlettype="' + shot.wizletType + '"></script>').text(JSON.stringify(wizletInfo)); // Note: fixed string because it is being pattern matched in simulation-data-parser.js
                    wizletInfoScript.appendTo(shotHtml);

                    var wizletConstructor = Wizer.Wizlet[capitalizeFirstLetter(shot.wizletType)];
                    if (wizletConstructor) {
                        wizlet = wizletConstructor.getRegistration();
                        if (wizlet.renderServerPart) {
                            // Note: some legacy wizlets still render HTML server side. When offline we need to emulate that here
                            wizlet.renderServerPart(shotHtml, shot.wizletInfo);
                        }
                    }

                    shotHtml.appendTo(mainWrapper);
                });
                var actionHtml = $('<div></div>').append(mainWrapper).html();
                define('text!' + voteUrl, actionHtml);

                votesite.changeSlide(self.context, 'votingSlide', {
                    scriptName: action.name,
                    voteUrl: voteUrl,
                    isAmd: true,
                    isOffline: true
                });
            };

            // ToDo: not ready, only use in spike (wizerDesktop)
            Api.prototype.navigate = function(action) {
                var self = this;
                if (self.isOffline()) {
                    self.storage.setCurrentActionId(self.event.title, action.id);
                    self.session.participation.currentAction = action;
                    if (self.context.find('#votingSlideDiv').length == 0) {
                        self.context.html('<div id="votingSlideDiv"></div>');
                    }
                    return self.renderShots(action, self);
                } else {
                    return window.jump(action);
                }
            };

            Api.prototype.storage = {
                clear: function(eventName) {
                    if (typeof(Storage) !== "undefined") {
                        if (eventName) {
                            localStorage.removeItem(eventName);
                        }
                    }
                },
                getEventState: function(eventName) {
                    var eventState;
                    if (localStorage[eventName]) {
                        eventState = $.parseJSON(localStorage[eventName]);
                    } else {
                        eventState = {};
                    }
                    return eventState;
                },
                setEventState: function(eventName, eventState) {
                    localStorage[eventName] = JSON.stringify(eventState);
                },
                setCurrentActionId: function(eventName, actionId) {
                    if (typeof(Storage) !== "undefined") {
                        var eventState = Api.prototype.storage.getEventState(eventName);
                        eventState.currentActionId = actionId;
                        Api.prototype.storage.setEventState(eventName, eventState);
                    }
                },
                getCurrentActionId: function(eventName) {
                    if (typeof(Storage) !== "undefined" && localStorage[eventName]) {
                        var eventState = Api.prototype.storage.getEventState(eventName);
                        return eventState.currentActionId;
                    }
                    return null;
                },
                getVotes: function(eventName, participantId) {
                    var eventState = Api.prototype.storage.getEventState(eventName);
                    if (eventState.votes) {
                        return eventState.votes[participantId];
                    } else {
                        return null;
                    }
                },
                setVotes: function(eventName, participantId, myVotes) {
                    var eventState = Api.prototype.storage.getEventState(eventName);
                    if (!eventState.votes) eventState.votes = {};
                    eventState.votes[participantId] = myVotes;
                    Api.prototype.storage.setEventState(eventName, eventState);
                }
            }

            Api.prototype.navigateToInitialAction = function() {
                var self = this;
                return Q.fcall(function() {
                        var currentId = self.storage.getCurrentActionId(self.event.title);
                        if (currentId) {
                            var action = self.event.actions[currentId];
                            if (action) {
                                var myVotes = self.storage.getVotes(self.event.title, self.session.participation.id);
                                if (myVotes) {
                                    self.event.votes[self.session.participation.id] = myVotes;
                                }
                                return self.navigate(action);
                            }
                        }
                        var initialAction = null;
                        // take first action in the defines map
                        for (var firstAction in self.event.actions) {
                            initialAction = firstAction;
                            break;
                        }
                        return self.navigate(self.event.actions[initialAction]);
                    })
                    .fail(Wizer.Api.showError);
            };


            // ???? 
            Api.prototype.jumpBack = function(currentActionId) {
                var self = this;
                // Note: not asynchronous
                if (self.isOffline()) {
                    var actionId = self.history.pop();
                    self.jump(actionId);

                } else {
                    // consider this too dangerous if client is out of sync with server, and the problem  it tries to solve is (hopefully) prevented with double click protection
                    //if (currentActionId && self.actionIdWhenLastJumpBack == currentActionId) {
                    //    logger.log("jumpBack will not jump since it have not moved since last jumpback");
                    //    return Q.fcall(function () { return false });
                    //}
                    self.actionIdWhenLastJumpBack = currentActionId;

                    var jumping = Q(AjaxGetJson("Vote", "NavigateBack"));
                    var updatingSlideframe = jumping.then(function(response) {
                        if (!Wizer.usePush) { // Todo: Look at the push part
                            logger.log("jump will updateSlideFrame");
                            updateSlideFrame();
                        }
                        //lastNavigationClick = null;
                        return response;
                    });
                    updatingSlideframe.fail(Wizer.Api.showError);
                    return updatingSlideframe;
                }
            }


            Api.prototype.jump = function(sectionNameOrActionId, pushToHistory, setCurrentAction) {
                var self = this;
                self.actionIdWhenLastJumpBack = -1;

                return self.lookingUpActionIdIfNotNumeric(sectionNameOrActionId)
                    .then(function(actionId) {
                        if (self.isOffline()) {
                            var action = self.getAction(actionId);
                            if (pushToHistory) {
                                self.history.push(self.session.participation.currentAction.id);
                            }
                            return self.navigate(action);

                        } else {
                            var args = "actionId=" + actionId;
                            if (pushToHistory) args += "&pushToHistory=true";
                            if (setCurrentAction) args += "&setCurrentAction=true";
                            var ajaxing = Q(AjaxGetJson("Vote", "SetIndividualCurrentActionId", args));
                            return ajaxing.then(function(response) {
                                if (!Wizer.usePush) { // Todo: Look at the push part
                                    logger.log("jump will updateSlideFrame");
                                    updateSlideFrame();
                                }
                                lastNavigationClick = null;
                                return response;
                            });
                        }

                    })
                    .fail(this.showError);
            };

            Api.prototype.jumpToModule = function(moduleName) {
                var self = this;
                self.actionIdWhenLastJumpBack = -1;
                // Note: not asynchronous
                var jumping = Q.defer();
                self.lookingUpSectionByName('JumpToMyModule')
                    .then(function() {
                        // now all actions is read from server
                        // now we have a sorted list of actions - jump to first action with the actual module name
                        var numericActionId = -1;
                        if (!self.allActionSortedAfterSequence) {
                            // this should not be possible - lookingUpSectionByName should wait intil all actions is rerad from the server
                            jumping.reject("List of actions/sections is empty");
                            return jumping.promise;
                        }
                        $.each(self.allActionSortedAfterSequence, function(index, action) {
                            if (action.moduleName == moduleName) {
                                numericActionId = action.id;
                                return false; // break loop
                            }
                        });

                        if (numericActionId >= 0) {
                            var args = "actionId=" + numericActionId;
                            var ajaxing = Q(AjaxGetJson("Vote", "SetIndividualCurrentActionId", args));
                            var updating = ajaxing.then(function(response) {
                                if (!Wizer.usePush) { // Todo: Look at the push part
                                    logger.log("jump will updateSlideFrame");
                                    updateSlideFrame();
                                }
                                jumping.resolve(response);
                            });
                        } else {
                            jumping.reject("User have unknown module or module without actions: " + moduleName);
                        }
                    })
                    .fail(this.showError)
                    .done();
                return jumping.promise;
            }

            Api.prototype.lookingUpActionIdIfNotNumeric = function(sectionNameOrActionId) {
                var self = this;
                if ($.isNumeric(sectionNameOrActionId)) {
                    return Q.fcall(function() { return sectionNameOrActionId });
                } else if (self.isOffline()) {
                    return Q.fcall(function() {
                        var actionId = 0;
                        $.each(self.event.sections, function(key, section) {
                            if (sectionNameOrActionId == section.title) {
                                actionId = section.actions[0];
                                return true; // break loop
                            }
                        });
                        return actionId;
                    })
                } else {
                    var numericActionId = new Q.defer();
                    if (!self.gettingAllActions) {
                        self.gettingAllActions = Q.defer();
                        Q(AjaxGetJson("Vote", "GetAllActions"))
                            .then(function(jsonArray) {
                                var result = {};
                                $.each(jsonArray, function(index, action) {
                                    if (!result[action.section]) {
                                        result[action.section] = action.id;
                                    }
                                });
                                self.gettingAllActions.resolve(result);
                            });
                    }
                    self.gettingAllActions.promise.then(function(map) {
                            if (map[sectionNameOrActionId]) {
                                numericActionId.resolve(map[sectionNameOrActionId]);
                            } else {
                                numericActionId.reject(new Error("cannot jump to unknown section named '" + sectionNameOrActionId + "'"));
                            }
                        })
                        .fail(self.showError);
                    return numericActionId.promise;
                }
            }

            Api.prototype.jumpToSection = function(sectionName, pushToHistory) {
                var self = this;
                self.actionIdWhenLastJumpBack = -1;
                // Note: not asynchronous
                if (self.isOffline()) {
                    var action = self.getAction(sectionName);
                    if (pushToHistory) {
                        self.history.push(self.session.participation.currentAction.id);
                    }
                    return self.navigate(action);

                } else {
                    var jumping = Q.defer();
                    self.lookingUpSectionByName(sectionName)
                        .then(function(numericActionId) {
                            var args = "actionId=" + numericActionId;
                            if (pushToHistory) args += "&pushToHistory=true";
                            var ajaxing = Q(AjaxGetJson("Vote", "SetIndividualCurrentActionId", args));
                            var updating = ajaxing.then(function(response) {
                                if (!Wizer.usePush) { // Todo: Look at the push part
                                    logger.log("jump will updateSlideFrame");
                                    updateSlideFrame();
                                }
                                jumping.resolve(response);
                            });
                        })
                        .fail(this.showError)
                        .done();
                    return jumping.promise;
                }
            };

            Api.prototype.lookingUpSectionByName = function(sectionName) {
                var numericActionId = new Q.defer();
                var self = this;
                if (!self.gettingAllActions) {
                    self.gettingAllActions = Q.defer();
                    Q(AjaxGetJson("Vote", "GetAllActions"))
                        .then(function(jsonArray) {
                            var result = {};
                            $.each(jsonArray, function(index, action) {
                                if (!result[action.section]) {
                                    result[action.section] = action.id;
                                }
                            });
                            self.allActionSortedAfterSequence = jsonArray;
                            self.gettingAllActions.resolve(result);
                        });
                }
                self.gettingAllActions.promise.then(function(map) {
                        if (map[sectionName]) {
                            numericActionId.resolve(map[sectionName]);
                        } else {
                            numericActionId.reject(new Error("cannot jump to unknown section named '" + sectionName + "'"));
                        }
                    })
                    .fail(self.showError);
                return numericActionId.promise;
            }

            Api.prototype.autoNext = function() {
                if (Wizer.preview) {
                    return Q.fcall(function() { return true; });
                }

                var self = this;
                // Note: not asynchronous
                if (self.isOffline()) {
                    self.jump(self.session.participation.currentAction.id + 1);
                } else {
                    if (window.currentAction.participantCurrentActionControl == "Individual") {
                        var jump = Q(AjaxGetJson("Vote", "NextIndividualAction"));
                        jump.then(function() {
                            if (!Wizer.usePush) { // Todo: Look at the push part
                                logger.log("push will updateSlideFrame");
                                updateSlideFrame();
                            }
                        });
                        jump.fail(Wizer.Api.showError);
                        return jump;
                    }
                }
            };

            // ToDo: not ready, only use in spike (wizerDesktop)
            // NB: only use in a loadQuestions().then context, (or bootstrapping.then, like votesite.js does before calling loadHandler on wizlets)
            Api.prototype.getQuestion = function(questionId) {
                // Note: not asynchronous
                if (this.isOffline()) {
                    return this.event.questions[questionId];
                } else {
                    return this.questions[questionId];
                }
            };

            Api.prototype.getDimension = function(dimensionKey) {
                if (this.isOffline()) {
                    logger.log(true, "WizerApi.getDimension: Not implemented for offline");
                } else {
                    return this.dimensions[dimensionKey];
                }
            };

            // NB: only use in a loadQuestions().then context, (or bootstrapping.then, like votesite.js does before calling loadHandler on wizlets)
            Api.prototype.getQuestionIdByName = function(name) {
                if (!name) return null;
                var self = this;
                if (name.indexOf('ASSESS.') === 0) {
                    name = name.substr(name.indexOf('.') + 1, name.length);
                }
                if (name.indexOf(".") > -1) { //This is refering to a child event
                    var eventName = name.substr(0, name.indexOf("."));
                    if (this.isOffline()) {
                        //not implemented. Need to check how to get data offline
                        logger.log(true, "Wizer-Api getQuestionIdByName: Not implemented for offline when question is from child event");
                    } else {
                        if (self.loadSubEventQuestionsPromise && self.loadSubEventQuestionsPromise[eventName] && self.loadSubEventQuestionsPromise[eventName].isFulfilled()) {
                            //promise is created and completed
                            if (self.qIds[name]) return self.qIds[name];
                            return self.qIds[name.toLowerCase()];
                        } else if (self.loadSubEventQuestionsPromise && self.loadSubEventQuestionsPromise[eventName]) {
                            //promise has been created but not completed
                            var waitForloading = self.loadSubEventQuestionsPromise[eventName];
                            return waitForloading.then(function() {
                                if (self.qIds[name]) return self.qIds[name];
                                return self.qIds[name.toLowerCase()];
                            });
                        } else { //no promise exists yet
                            var waitForloading = self.loadQuestionsForSubEvent(eventName);
                            return waitForloading.then(function() {
                                if (self.qIds[name]) return self.qIds[name];
                                return self.qIds[name.toLowerCase()];
                            });
                        }
                    }
                } else {
                    if (this.isOffline()) {
                        var result = null;
                        $.each(this.event.questions, function(index, question) {
                            if (question.shortName === name) {
                                result = question.id;
                            }
                        });
                        return result;
                    } else {
                        if (self.qIds && (self.qIds[name] || self.qIds[name.toLowerCase()])) {
                            if (self.qIds[name]) return self.qIds[name];
                            return self.qIds[name.toLowerCase()];
                        } else {
                            return self.allQuestionsPromise.then(function() {
                                if (self.qIds[name]) return self.qIds[name];
                                else if (self.qIds[name.toLowerCase()]) return self.qIds[name.toLowerCase()];
                                else {
                                    logger.log(true, "Undefined question found. Please contact developer. Q Name: ", name);
                                    return self.qIds[name];
                                }
                            });
                        }
                    }
                }
            };

            Api.prototype.logMyVotes = function(questionName) {
                var self = this;
                var id = self.getQuestionIdByName(questionName)
                self.getMyVotes([id])
                    .then(function(response) {
                        logger.log(true, JSON.stringify(response));
                    })
                    .done();
            }

            Api.prototype.logReport = function() {
                if (!this.isOffline()) {
                    logger.log(true, "logReport() only works offline; use /Wizer/Vote/Monitor instead");
                    return;
                }
                var self = this;
                var report = "";
                $.each(this.event.participants, function(index, participant) {
                    if (participant.participationId == self.session.participation.id) {
                        report += '"Participant";"' + participant.email + '";"' + participant.name + '";\r\n';
                    }
                });
                var myVotes = self.event.votes[self.session.participation.id];
                $.each(myVotes, function(qid, votes) {
                    var line = qid + ';"';
                    line += self.event.questions[qid].shortName + '";"'
                    var sep = '';
                    $.each(votes, function(index, vote) {
                        line += sep + vote.responseText;
                        sep = '##';
                    })
                    line += '";'
                    report += line + '\r\n';
                });
                logger.log(true, report);
            }

            // NB: only use in a loadQuestions().then context, (or bootstrapping.then, like votesite.js does before calling loadHandler on wizlets)
            Api.prototype.getDisplayResponseText = function(questionId, vote) {
                var question = this.getQuestion(questionId);
                if (question.Options && question.Options.length > 0) {
                    return this.getOptionText(question.Options, vote.responseText);
                }
                return vote.responseText;
            };

            Api.prototype.getOptionsForQuestion = function(questionName) {
                var questionId = this.getQuestionIdByName(questionName);
                var question = this.getQuestion(questionId);

                if (question.Options && question.Options.length > 0) {
                    return question.Options;
                } else {
                    return null;
                }
            };

            Api.prototype.getOptionText = function(options, answer) {
                var result = answer;
                $.each(options, function(index, option) {
                    if (option.KeypadNumber == answer) {
                        result = option.ResponseText;
                    }
                });
                return result;
            };

            // Temporarily needed for a bugfix
            Api.prototype.getSpecificQuestions = function(questionIdList) {
                return Q(AjaxGetJson("Registration", "GetSpecificQuestion", questionIdList));
            };

            //This will get the userwise/trackwise average. For eg refer to BarChartComponent.js
            //arguements are =   shortNames: questionName,   filterQuestionId:filterQuestionId,   filterText:filterTextValue,  showAverage: true,  showVoteCount: true,  seed: Math.random().toString().replace(".", "")
            Api.prototype.getAverageVotes = function(options) {
                if (Wizer.editMode) {
                    $.extend(options, { editMode: true });
                }
                var avgVotes = Q(AjaxGetJson("VoteAPI", "GetBarChartResults", options));
                return avgVotes;
            };

            Api.prototype.getStackedBarChartResults = function(options) {
                var avgVotes = Q(AjaxGetJson("VoteAPI", "GetStackedBarChartResults", options));
                return avgVotes;
            };

            Api.prototype.GetStackedBarChartCorrectAnswer = function(options) {
                var avgVotes = Q(AjaxGetJson("VoteAPI", "GetStackedBarChartCorrectAnswer", options));
                return avgVotes;
            };

            Api.prototype.getVotePercentage = function(options) {
                var avgVotes = Q(AjaxGetJson("VoteAPI", "GetVoteCountPercentage", options));
                return avgVotes;
            };

            Api.prototype.getSumChartVotePercentage = function(options) {
                var avgVotes = Q(AjaxGetJson("VoteAPI", "GetSumChartVotePercentage", options));
                return avgVotes;
            };

            Api.prototype.getRankingResults = function(options) {
                var response = Q(AjaxGetJson("VoteAPI", "GetRankingResults", options));
                return response;
            };

            Api.prototype.getForemanId = function(questionId, isDelivery) {
                return this.wizerModel.getForemanId(questionId, isDelivery);
            }
            Api.prototype.getForemanIdAPI = function(questionId, isDelivery) {
                var completed = new Q.defer();

                var ajaxForemanId = Q(AjaxGetJson("VoteAPI", "GetForemanId", { questionId: questionId, isDelivery: isDelivery }));
                var promise = ajaxForemanId.then(function(response) {
                    if (response && response.success) {
                        completed.resolve(response.foremanId);
                    } else {
                        //throw "Problem finding track foreman: " + response.message;
                        Wizer.Api.showError("Problem finding track foreman: " + response.message);
                        completed.resolve(null);
                    }
                });
                ajaxForemanId.fail(function(err) {
                    var errorText = err;
                    if (err.toString() === "[object Object]") {
                        errorText = JSON.stringify(err);
                    }
                    if (errorText.indexOf("need as track foreman question") == -1) {
                        Wizer.Api.showError("Track foreman question missing")
                    } else {
                        Wizer.Api.showError(err);
                    }
                });
                return completed.promise;
                //return Q(AjaxGetJson("VoteApi", "GetForemanId"));
            };

            Api.prototype.getTrackParticipants = function(trackQuestionId) {
                var result = { participants: {} };
                var ajaxparticpantIds = Q(AjaxGetJson("Vote", "GetTrackParticipantIds", { trackQuestionId: trackQuestionId }));
                var promise = ajaxparticpantIds.then(function(response) {
                    if (response && response.success && response.success == true) {
                        for (var i = 0; i < response.participants.length; i++) {
                            var participant = response.participants[i];
                            result.participants[participant.Id] = { Id: participant.Id, Name: participant.Name };
                        }
                        result.foremanId = response.foremanId;
                        return result;
                    } else {
                        Wizer.Api.showError(response.message);
                    }
                });
                ajaxparticpantIds.fail(function(err) {
                    Wizer.Api.showError(err);
                });
                return promise;
            };

            // {questionIds:[1,2,3]} -> { votes : [{ participationName: "name", responseText: [ "responseA", "respB" ] }, { participationName: "nameA", responseText: [ "responseAA", "respAB" ] }] }
            Api.prototype.getVotes = function(questionObject) {
                if (this.isOffline()) {
                    var completed = new Q.defer();
                    completed.reject(new Error("not implemented yet"));
                    return completed.promise;
                } else {
                    var ajaxVotes = Q(AjaxGetJson("Vote", "GetVotes", questionObject));
                    var filtered = ajaxVotes.then(function(votes) {
                        var response = [];
                        $.each(votes, function(index, vote) {
                            response.push({
                                questionId: vote.QuestionId,
                                participantName: vote.ParticipantName,
                                responseText: vote.ResponseText,
                                participantIsAdmin: vote.IsAdmin,
                                sequence: vote.Sequence,
                                universalId: vote.UniversalId,
                                responseId: vote.Id,
                                dataValues: vote.DataValues,
                                participantId: vote.ParticipantId,
                                participantEmail: vote.ParticipantEmail,
                                tryNumber: vote.TryNumber
                            });
                        });
                        return { votes: response };
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            //questionIds:[1,2,3] & filterText:'25' -> { votes : [{ participationName: "name", responseText: [ "25" ] }, { participationName: "nameA", responseText: [ "25" ] }] }
            Api.prototype.getSpecificVotes = function(questionIds, specificText) {
                if (this.isOffline()) {
                    var completed = new Q.defer();
                    completed.reject(new Error("not implemented yet"));
                    return completed.promise;
                } else {
                    var ajaxVotes = Q(AjaxGetJson("Vote", "GetVotes", { questionIds: questionIds, specificText: specificText }));
                    var filtered = ajaxVotes.then(function(votes) {
                        var response = [];
                        $.each(votes, function(index, vote) {
                            response.push({ questionId: vote.QuestionId, participantName: vote.ParticipantName, responseText: vote.ResponseText, participantIsAdmin: vote.IsAdmin, sequence: vote.Sequence });
                        });
                        return { votes: response };
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            //questionIds:[1,2,3] & filterText:'name' -> { votes : [{ participationName: "name", responseText: [ "my name is neha" ] }, { participationName: "nameA", responseText: [ "name is to entered" ] }] }
            Api.prototype.getSimilarVotes = function(questionIds, text) {
                if (this.isOffline()) {
                    var completed = new Q.defer();
                    completed.reject(new Error("not implemented yet"));
                    return completed.promise;
                } else {
                    var ajaxVotes = Q(AjaxGetJson("Vote", "GetVotes", { questionIds: questionIds, similarText: text }));
                    var filtered = ajaxVotes.then(function(votes) {
                        var response = [];
                        $.each(votes, function(index, vote) {
                            response.push({ questionId: vote.QuestionId, participantName: vote.ParticipantName, responseText: vote.ResponseText, participantIsAdmin: vote.IsAdmin, sequence: vote.Sequence });
                        });
                        return { votes: response };
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.getVotesByCategory = function(questionIds, filterQuestionId, filterText) {
                if (this.isOffline()) {
                    var completed = new Q.defer();
                    completed.reject(new Error("not implemented yet"));
                    return completed.promise;
                } else {
                    var ajaxVotes = Q(AjaxGetJson("Vote", "GetVotes", { questionIds: questionIds, filterQuestionId: filterQuestionId, filterText: filterText }));
                    var filtered = ajaxVotes.then(function(votes) {
                        var response = [];
                        $.each(votes, function(index, vote) {
                            response.push({ questionId: vote.QuestionId, participantName: vote.ParticipantName, responseText: vote.ResponseText, participantIsAdmin: vote.IsAdmin, sequence: vote.Sequence });
                        });
                        return { votes: response };
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.getVotesByGroup = function(questionIds, groupQuestionId) {
                var self = this;
                if (this.isOffline()) {
                    var completed = new Q.defer();
                    completed.reject(new Error("not implemented yet"));
                    return completed.promise;
                } else {
                    return self.getTrackParticipants(groupQuestionId).then(function(respParticipants) {
                        var participants = [];
                        $.each(respParticipants.participants, function(indx, participant) {
                            participants.push(participant.Id);
                        });
                        var ajaxVotes = Q(AjaxGetJson("Vote", "GetVotes", { questionIds: questionIds, participantIds: participants, groupQuestionId: groupQuestionId }));
                        ajaxVotes.fail(Wizer.Api.showError);
                        return ajaxVotes.then(function(votes) {
                            var response = [];
                            $.each(votes, function(index, vote) {
                                response.push({ questionId: vote.QuestionId, participantName: vote.ParticipantName, responseText: vote.ResponseText, participantIsAdmin: vote.IsAdmin, sequence: vote.Sequence });
                            });
                            return { votes: response };
                        });
                    });
                }
            }

            //{trackQuestionId:1,questionIds:[1,2,3]}
            Api.prototype.getForemanVotes = function(trackQuestionId, questionIds, isDelivery) {
                var self = this;
                var foremanIdPromise = self.getForemanId(trackQuestionId, isDelivery);
                return foremanIdPromise.then(function(foremanId) {
                    var votes = self.getVotesByParticipant(questionIds, foremanId);
                    return votes.then(function(response) {
                        response.foremanId = foremanId;
                        return response;
                    });
                });
            }

            Api.prototype.getVotesByQuestions = function(questionObject) {
                var allVotesPromise = this.getVotes(questionObject)
                    .then(function(allVotes) {
                        var votes = {};
                        $.each(allVotes.votes, function sortingVotes(index, vote) {
                            if (!votes[vote.questionId]) {
                                votes[vote.questionId] = [];
                            }
                            votes[vote.questionId].push({ participantName: vote.participantName, responseText: vote.responseText, participantIsAdmin: vote.participantIsAdmin, universalId: vote.universalId, participantId: vote.participantId });
                        });
                        return votes;
                    });
                return allVotesPromise;
            }

            Api.prototype.getVotesInRankRange = function(rankquestionId, myRank, showNear, isDelivery) {
                if (this.isOffline()) {
                    var completed = new Q.defer();
                    completed.reject(new Error("not implemented yet"));
                    return completed.promise;
                } else {
                    var ajaxVotes = Q(AjaxGetJson("VoteAPI", "GetVotesInRankRange", { rankQuestionId: rankquestionId, myRank: myRank, range: showNear, isDelivery: isDelivery }));
                    ajaxVotes.fail(Wizer.Api.showError);
                    return ajaxVotes.then(function(result) {
                        var allvotes = result.votes;
                        var response = [];
                        var tempVote = {};
                        $.each(allvotes, function(index, vote) {
                            //filtering votes (if there are multiple users with the same rank only 1 random should be displayed)
                            if ((tempVote[vote.ResponseText] == undefined) || tempVote[vote.ResponseText] != vote.IsAdmin) {
                                response.push({ questionId: vote.QuestionId, participantName: vote.ParticipantName, responseText: vote.ResponseText, participantIsAdmin: vote.IsAdmin, sequence: vote.Sequence });
                                tempVote[vote.ResponseText] = vote.IsAdmin;
                            }
                        });
                        var votes = {};
                        $.each(response, function sortingVotes(index, vote) {
                            if (!votes[vote.questionId]) {
                                votes[vote.questionId] = [];
                            }
                            votes[vote.questionId].push({ participantName: vote.participantName, responseText: vote.responseText, participantIsAdmin: vote.participantIsAdmin, sequence: vote.sequence });
                        });
                        return votes;
                    });
                }
            }

            // questionId* x participantId -> { votes : (questionId -map-> string*) }
            Api.prototype.getMyVotes = function(questionIds) {
                return this.getVotesByParticipant(questionIds, null);
            };

            Api.prototype.getVotesByParticipant = function(questionIds, participantId) {
                //return this.getVotesByParticipantAPI(questionIds, participantId);
                return this.wizerModel.getVotesByParticipant(questionIds, participantId);
            };

            Api.prototype.getVotesByParticipantAPI = function(questionIds, participantId) {
                var self = this;
                var response = { votes: {}, responses: {} };
                for (var i = 0; i < questionIds.length; i++) {
                    response.votes[questionIds[i]] = [];
                    response.responses[questionIds[i]] = [];
                }
                if (this.isOffline()) {
                    var completed = new Q.defer();
                    var votes = this.event.votes[participantId];
                    if (votes) {
                        for (var i = 0; i < questionIds.length; i++) {
                            var replies = votes[questionIds[i]];
                            if (replies) {
                                var reply = replies[0];
                                if (response.votes[reply.questionId]) {
                                    response.votes[reply.questionId].push(reply.responseText);
                                }
                            }
                        }
                    }
                    completed.resolve(response);
                    return completed.promise;
                } else {
                    var ajaxVotes;

                    if (participantId) {
                        ajaxVotes = Q(AjaxGetJson("VoteAPI", "GetVotesByParticipant", { questionIds: questionIds, participationId: participantId }));
                    } else {
                        ajaxVotes = Q(AjaxGetJson("VoteAPI", "GetMyVotes", { questionIds: questionIds, ignoreTryNumber: self.ignoreTryNumber }));
                    }
                    var filtered = ajaxVotes.then(function(result) {
                        if (result && !result.isDisabled) {
                            for (var i = 0; result.votes && i < result.votes.length; i++) {
                                var vote = result.votes[i];
                                if (response.votes[vote.QuestionId]) {
                                    response.votes[vote.QuestionId].push(vote.ResponseText);
                                    response.responses[vote.QuestionId].push({
                                        questionId: vote.QuestionId,
                                        responseId: vote.Id,
                                        responseText: vote.ResponseText,
                                        timeStamp: vote.Timestamp
                                    });
                                }
                            }
                            response.participantName = result.participantName;
                            response.participantEmail = result.participantEmail;
                            return response;
                        } else {
                            if (result.isDisabled) logout();
                        }
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            };


            Api.prototype.getMyFriendEventVotes = function(friendEventQuestions) {
                var response = { votes: {} };
                /*if (friendEventQuestions.length > 0) {
                    for (var i = 0; i < friendEventQuestions.length; i++) {
                        if (friendEventQuestions[i].questionIds && friendEventQuestions[i].questionIds.length > 0) {
                            for (var j = 0; j < friendEventQuestions[i].questionIds.length; j++) {
                                response.votes[friendEventQuestions[i].questionIds[j]] = [];
                            }
                        }
                    }
                }*/

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getMySubEventVotes, getOffline not implemented")
                } else {
                    var defer = Q.defer();;

                    $.ajax({
                        url: '/Wizer/VoteAPI/GetMyFriendEventVotes',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ friendEventQuestions: friendEventQuestions }),
                        datatype: 'json',
                        success: function(result) {
                            if (result.success) {
                                /*for (var i = 0; result.votes && i < result.votes.length; i++) {
                                    var vote = result.votes[i];
                                    if (response.votes[vote.QuestionId]) {
                                        response.votes[vote.QuestionId].push(vote.ResponseText);
                                    }
                                }*/
                                defer.resolve(result.votes);
                            } else {
                                defer.reject(result);
                            }
                        },
                        error: function(response) {
                            defer.reject(response);
                        }
                    });

                    return defer.promise;
                }
            };

            Api.prototype.getVoteCount = function(questionIds, trackQuestionId) {
                var response = { counts: {} };
                if (this.isOffline()) {
                    // do nothing
                } else {
                    var ajaxVotes = Q(AjaxGetJson("VoteAPI", "GetVoteCount", { questionIds: questionIds, trackQuestionId: trackQuestionId }));
                    var filtered = ajaxVotes.then(function(counts) {
                        if (counts.success == false) {
                            // console.log(counts.message);
                            Wizer.Api.showError(counts.message);
                        } else {
                            for (var i = 0; i < counts.counts.length; i++) {
                                var count = counts.counts[i];
                                response.counts[count.QuestionId] = count.VoteCount;
                            }
                            return response;
                        }
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            };

            Api.prototype.addVote = function(questionId, resultText, options) {
                if (this.isOffline()) {
                    //to be implemented later
                } else {
                    return this.wizerModel.addVote(questionId, resultText, options);
                }
            }

            Api.prototype.addVoteAPI = function(questionId, resultText, options) {
                var self = this;
                if (this.isOffline()) {
                    //to be implemented later
                } else {
                    logger(true, "wizer-api.addVote calling Vote/VoteSpecific");
                    var submitting = Q(AjaxGetJson("Vote", "VoteSpecific", { questionId: questionId, resultText: (options && options.dontEncode) ? resultText : encodeURIComponent(resultText) }));
                    var finalizing = submitting.then(function(response) {
                            var success = eval(response) == true;
                            if (success) {
                                logger(true, "wizer-api.addVote calling Vote/VoteSpecific - success");
                                $(document).trigger("wizer:action:newVoteAdded");
                                return success;
                            } else {
                                logger(true, "wizer-api.addVote calling Vote/VoteSpecific - failure");
                                Wizer.Api.showError("saving vote failed");
                            }
                        })
                        .fail(function() {
                            logger(true, "wizer-api.addVote calling Vote/VoteSpecific - fail");
                            Wizer.Api.showError();
                        });
                    return finalizing;
                }
            };

            Api.prototype.cachedVotesToPushWhenOnline = [];

            Api.prototype.addVotes = function(voteStruct) {
                if (this.isOffline()) {
                    return this.addVotesAPI(voteStruct);
                } else {
                    return this.wizerModel.addVotes(voteStruct);
                }
            }

            Api.prototype.addVotesAPI = function(voteStruct) {
                var self = this;
                if (!voteStruct.votes) {
                    return { votes: [{ failure: "addVotes expects an array structure with votes" }] };
                }
                var missingQuid = false;
                $.each(voteStruct.votes, function(index, vote) {
                    if (!vote.questionId) missingQuid = true;
                });
                if (missingQuid) {
                    return { votes: [{ failure: "addVotes expects all votes to have a questionId" }] };
                }
                if (this.isOffline()) {
                    var results = [];
                    if (!self.event.votes[self.session.participation.id]) {
                        self.event.votes[self.session.participation.id] = {};
                    }
                    $.each(voteStruct.votes, function(index, vote) {
                        self.cachedVotesToPushWhenOnline.push(vote);
                        if (!vote.questionId) {
                            results.push({ failure: "vote without questionId" });
                            return true; //continue
                        }
                        if (!self.event.questions[vote.questionId]) {
                            results.push({ failure: "vote for non-existing question " + vote.questionId });
                            return true; //continue
                        }

                        var createdVote;
                        if (vote.responseText) {
                            createdVote = [{ responseText: vote.responseText, questionId: vote.questionId, participationId: self.session.participation.id }];
                        } else if (vote.responseDelta) {
                            var was = 0;
                            try {
                                was = parseFloat(self.event.votes[self.session.participation.id][vote.questionId][0].responseText);
                            } catch (exp) {}
                            var delta = parseFloat(vote.responseDelta);
                            createdVote = [{ responseText: delta + was, questionId: vote.questionId, participationId: self.session.participation.id }];
                        } else if (vote.responseTotal) {
                            var result = 0;
                            $.each(vote.responseTotal.questionIds, function(j, q) {
                                result += parseFloat(self.event.votes[self.session.participation.id][q][0].responseText);
                            });
                            createdVote = [{ responseText: result, questionId: vote.questionId, participationId: self.session.participation.id }];
                        } else if (vote.nextValue) {
                            var max = null;
                            $.each(self.event.votes, function(pId, votes) {
                                if (votes[vote.questionId]) {
                                    $.each(votes[vote.questionId], function(index, resp) {
                                        var val = parseFloat(resp.responseText);
                                        max = Math.max(max, val);
                                    });
                                }
                            });
                            if (max == null) max = 0;
                            createdVote = [{ responseText: max + 1, questionId: vote.questionId, participationId: self.session.participation.id }];
                        } else if (vote.responseRange) {
                            var boundaries = vote.responseRange.boundaries;
                            boundaries.sort();
                            var responseValue = parseFloat(self.event.votes[self.session.participation.id][vote.responseRange.questionId][0].responseText);
                            var rangeValue = boundaries.length + 1;
                            $.each(boundaries, function(boundaryIndex, boundaryValue) {
                                if (responseValue < boundaryValue) {
                                    rangeValue = boundaryIndex + 1;
                                    return false;
                                }
                            });
                            createdVote = [{ responseText: rangeValue, questionId: vote.questionId, participationId: self.session.participation.id }];
                        } else {
                            results.push({ failure: "vote without response/responseDelta/responseTotal/responseRange" });
                            return true;
                        }
                        self.event.votes[self.session.participation.id][vote.questionId] = createdVote;
                        results.push({ responseId: --self.offlineRunningResponseId });
                    });

                    var myVotes = self.event.votes[self.session.participation.id];
                    self.storage.setVotes(self.event.title, self.session.participation.id, myVotes);
                    //invoke vote added event
                    $(document).trigger("wizer:action:newVoteAdded");
                    return { votes: results };
                } else {
                    var submitting = Q(AjaxGetJson("VoteAPI", "VoteManyQuestionsFromJson", { votesJson: JSON.stringify(voteStruct), ignoreTryNumber: self.ignoreTryNumber }));
                    var formatting = submitting.then(function(response) {
                            if (response.success) {
                                var results = [];
                                $.each(response.votes, function(index, vote) {
                                    results.push({ responseId: vote.Id });
                                });
                                $(document).trigger("wizer:action:newVoteAdded");
                                return results;
                            } else {
                                Wizer.Api.showError("saving votes failed with " + response);
                            }
                        })
                        .fail(Wizer.Api.showError);
                    return formatting;
                }
            };

            Api.prototype.removeVotes = function(voteStruct) {
                return this.wizerModel.removeVotes(voteStruct);
            }

            Api.prototype.removeVotesByQuestions = function(questionNames) {
                return Q(AjaxGetJson("Vote", "RemoveVotesByQuestions", { questionNames: questionNames })).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            };

            Api.prototype.removeVotesAPI = function(voteStruct) {
                var self = this;
                if (!voteStruct.votes) {
                    return Q.fcall(function() { return { votes: [{ failure: "addVotes expects an array structure with votes" }] }; });
                }
                if (this.isOffline()) {
                    $.each(voteStruct.votes, function(index, vote) {
                        if (vote) {
                            self.event.votes[self.session.participation.id][vote.questionId] = null;
                        }
                    });
                    return { success: true };
                } else {
                    var promises = [];
                    $.each(voteStruct.votes, function(index, vote) {
                        logger(true, "wizer-api.removeVotes calling Vote/VoteSpecific");
                        promises.push(Q(AjaxGetJson("Vote", "RemoveVoteSpecific", { questionId: vote.questionId })));
                    });
                    return Q.all(promises).then(function(results) {
                        logger(true, "wizer-api.removeVotes calling Vote/VoteSpecific - returned");
                        var result = { success: true };
                        $.each(results, function(index, response) {
                            if (!response.success) {
                                logger(true, "wizer-api.removeVotes calling Vote/VoteSpecific - unsuccesful");
                                result = response;
                            }
                            if (response) {
                                $(document).trigger("wizer:action:voteRemoved");
                            }
                        });

                        return result;
                    });
                }
            };

            Api.prototype.setStateCache = function(modelName, state, options) {
                if (this.isOffline()) {
                    return Q.fcall(function() { return null; });
                } else {
                    modelName = 'modelState:' + modelName;
                    return this.caching.setValue(modelName, state, options);
                }
            };

            Api.prototype.getStateCache = function(modelName) {
                if (this.isOffline()) {
                    return Q.fcall(function() { return [null, null]; });
                } else {
                    return [this.caching.getValue('modelState:' + modelName), this.caching.getValue('modelState:historical:' + modelName)];
                }
            };

            Api.prototype.getXapiCache = function() {
                if (this.isOffline()) {
                    return Q.fcall(function() { return [null, null]; });
                } else {
                    return this.caching.getValue('jsonForTracking');
                }
            };

            Api.prototype.setXapiCache = function(trackingJson) {
                if (this.isOffline()) {
                    return Q.fcall(function() { return null; });
                } else {
                    return this.caching.setValue('jsonForTracking', trackingJson);
                }
            };

            Api.prototype.updateCalcServerCache = function(model, options) {
                var state = model.getState();
                // console.log("updateServerCache will save state: " + JSON.stringify(state));
                this.setStateCache(model.modelName, state, { immediately: (options && options.bigChange) });
                if (options && options.bigChange) {
                    var historical = model.getHistoricalState();
                    // console.log("updateServerCache will save historical state: " + JSON.stringify(historical));
                    this.setStateCache("historical:" + model.modelName, historical, { immediately: true });
                };
            }

            Api.prototype.onNavigate = function() {
                this.caching.setStateOnServer();
            }

            Api.prototype.getEntry = function(entryQuestion, entryResponseId) {
                return Q(AjaxGetJson("VoteAPI", "GetEntry", { entryQuestion: entryQuestion, entryResponseId: entryResponseId })).then(function(response) {
                    if (!response.success) throw response.message;
                    return response.responses;
                });
            };

            Api.prototype.getRandomEntryList = function(options) {
                if (Wizer.editMode) {
                    $.extend(options, { editMode: true });
                }
                return Q(AjaxGetJson("VoteAPI", "GetRandomEntryList", options)).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            }

            Api.prototype.getEntryList = function(options) {
                if (Wizer.editMode) {
                    $.extend(options, { editMode: true });
                }
                return Q(AjaxGetJson("VoteAPI", "GetEntryList", options)).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            }

            Api.prototype.dimensionKeysAlone = function(dimensions) {
                var keys = [];
                if (dimensions) {
                    $.each(dimensions, function(indx, dim) {
                        keys.push(dim.key);
                    });
                }
                return keys;
            }

            Api.prototype.dimensionKeysSeparate = function(dimensions) {
                var keys = [];
                if (dimensions) {
                    $.each(dimensions, function(indx, dim) {
                        keys.push(dim);
                    });
                }
                return keys;
            }

            Api.prototype.getActionHtml = function(actionName, bindingParticipant) {
                var forceOrig = location.hash.indexOf("orig") >= 0; // used for testing purposes - add #orig to the url to see the original
                var usePublished = Wizer.publishedActionsUrl && Wizer.actions;
                if (usePublished && (!Wizer.actions[actionName.toLowerCase()] || forceOrig)) {
                    logger.log(true, (forceOrig ? 'forced original ' : 'no published ') + actionName);
                    usePublished = false;
                }
                if (!usePublished || Wizer.editMode || Wizer.resolveParticipantTimezone) {
                    return Q(AjaxGetText("Pages/Voting", "Show.aspx?customParameter=" + actionName + (Wizer.editMode ? "&editMode=true&preview=true" : "") + (bindingParticipant ? "&bindingParticipantId=" + bindingParticipant : "") + (Wizer.resolveParticipantTimezone ? "&resolveParticipantTimezone=true" : "")));
                } else {
                    logger.log(true, 'using published ' + actionName);
                    return Q.fcall(function() { return Wizer.actions[actionName.toLowerCase()]; });
                }
            }

            //This Api replaces the getWizletInfo and handles multiple components within an action. 
            Api.prototype.getWizletInfos = function(actionName, bindingParticipant) {
                var self = this;
                return this.getActionHtml(actionName, bindingParticipant).then(function(html) {
                    var wrapper = $('<div></div>');
                    wrapper.html(html);
                    var wizletMainWrapper = wrapper.find('.mainWrapper');
                    if (wizletMainWrapper.length == 0 && wrapper.find('form#aspnetForm[action="Login"]').length > 0) {
                        // gosh, I think we are logged out
                        window.location.reload();
                    }
                    if (wizletMainWrapper.length == 0) wizletMainWrapper = $(html);
                    var result = {
                        wizletServerHtml: wizletMainWrapper.html()
                    }
                    var actionScript = wizletMainWrapper.find('#actionConfig');
                    var wizletInfoScript = wizletMainWrapper.find('script.wizletInfo');
                    var wizletContainers = wizletMainWrapper.find('div.wizlet');
                    var actionJson = null;
                    if (actionScript.length > 0) {
                        actionJson = $.parseJSON($(actionScript).html());
                    }

                    if (actionJson && actionJson.ignoreTryNumber == 'true') {
                        self.ignoreTryNumber = true;
                    } else {
                        self.ignoreTryNumber = false;
                    }

                    if (actionJson && actionJson.wizletTypes) {
                        for (var i = 0, ii = actionJson.wizletTypes.length; i < ii; i++) {
                            var wizletType = actionJson.wizletTypes[i];
                            if (actionJson.WizletCustomJS && actionJson.WizletCustomJS[i] != '') {
                                //requirements.push('wizer/' + currentAction.WizletCustomJS[i]);
                                result[i] = {};
                                result[i].customJS = actionJson.WizletCustomJS[i];
                            }
                        }
                    }
                    //check if they have customeJS
                    if (wizletInfoScript.length > 0) {
                        $.each(wizletInfoScript, function(indx, infoScript) {
                            // Note: modern wizlets have a script.wizletInfo and render all(?) their HTML client side
                            if (!result[indx]) {
                                result[indx] = {};
                            }
                            result[indx].wizletType = $(infoScript).data('wizlettype')
                            result[indx].wizletInfo = votesite.getWizletInfoFromInfoElement($(infoScript));
                            result[indx].element = '#' + wizletContainers[indx].id;
                        });

                    } else {
                        // Note: legacy wizlets don't have script.wizletInfo and render some(?) of their HTML server side
                        result.wizletType = wizletMainWrapper.attr('rel');
                        result.wizletInfo = null;
                    }
                    // ToDo: this only works for actions with a single wizlet
                    return result;
                });
            };

            Api.prototype.parseDataBinderAndTrackQuestions = function(wizletInfo) {
                var self = this;
                var trackQuestionId = null;
                if (wizletInfo && wizletInfo.bindings) {
                    $.each(wizletInfo.bindings, function(index, bindingConfig) {
                        if (!bindingConfig.bind) {
                            throw 'missing "bind" property on binding with name ' + index;
                        }
                        self.addBindQuestionToScope(wizletInfo, bindingConfig.bind);
                        if (bindingConfig.trackQuestion) {
                            trackQuestionId = self.getQuestionIdByName(bindingConfig.trackQuestion);
                            self.addBindQuestionToScope(wizletInfo, bindingConfig.trackQuestion);
                        }
                    });
                }
                if (wizletInfo && wizletInfo.bindings && wizletInfo.trackQuestion) {
                    trackQuestionId = self.getQuestionIdByName(wizletInfo.trackQuestion);
                    self.addBindQuestionToScope(wizletInfo, wizletInfo.trackQuestion);
                }
                return trackQuestionId;
            };


            Api.prototype.addBindQuestionToScope = function(wizletInfo, binderQuestion) {
                var scopeHasQuestion = false;
                if (binderQuestion.indexOf(':') > 0) {
                    var splitted = binderQuestion.split(':');
                    if (splitted[0].toLowerCase() == 'db') {
                        binderQuestion = splitted[1];
                    } else {
                        binderQuestion = null;
                    }
                }
                if (binderQuestion != null) {
                    if (wizletInfo.scope) {
                        $.each(wizletInfo.scope, function(index, q) {
                            if (q == binderQuestion) {
                                scopeHasQuestion = true;
                            }
                        });
                    } else {
                        wizletInfo.scope = [];
                    }
                    if (!scopeHasQuestion) {
                        wizletInfo.scope.push(binderQuestion);
                    }
                }
            };

            //DEPRECATED
            //Use the new API getWizletInfos if you need infos of multiple comps in an xml.
            //Currently not modifying this because this has been used at many places and should not break
            Api.prototype.getWizletInfo = function(actionName) {
                return this.getActionHtml(actionName, null).then(function(html) {
                    var wrapper = $('<div></div>');
                    wrapper.html(html);
                    var wizletMainWrapper = wrapper.find('.mainWrapper');
                    if (wizletMainWrapper.length == 0) wizletMainWrapper = $(html);

                    var result = {
                        wizletServerHtml: wizletMainWrapper.html()
                    };
                    var wizletInfoScript = wizletMainWrapper.find('script.wizletInfo');
                    if (wizletInfoScript.length > 0) {
                        // Note: modern wizlets have a script.wizletInfo and render all(?) their HTML client side
                        result.wizletType = wizletInfoScript.attr('data-wizlettype');
                        // result.wizletInfo = JSON.parse(wizletInfoScript.text());
                        result.wizletInfo = $.parseJSON(wizletInfoScript.html());
                    } else {
                        // Note: legacy wizlets don't have script.wizletInfo and render some(?) of their HTML server side
                        result.wizletType = wizletMainWrapper.attr('rel');
                        result.wizletInfo = null;
                    }
                    // ToDo: this only works for actions with a single wizlet
                    return [result];
                });
            };
            var getLayoutPath = function(currentAction, self) {
                var paths = [];
                if (currentAction.layoutPath) {
                    paths.push('doT!' + currentAction.layoutPath + '.dot');
                    paths.push('css!' + currentAction.layoutPath + '.css');
                } else {
                    paths.push('doT!events/' + self.eventName() + '/layout/' + currentAction.layoutInEvent + '.dot');
                    paths.push('css!events/' + self.eventName() + '/layout/' + currentAction.layoutInEvent + '.css')
                }
                return paths;
            }

            Api.prototype.loadActionInContainer = function(actionXMLName, unusedContext, context, container, extraWizletInfo, addShieldCallback) {
                //addShieldCallback is used in 
                var self = this;
                //clean existing tab content
                container.html('');
                var tabWizletInfo = [];
                var containerInfo = [];
                var loading = new Q.defer();
                var wizletModulesPromiseArr = [];
                var it;
                extraWizletInfo = extraWizletInfo || {}; // Note: by default no extra wizletInfo properties added

                var layoutDefer = new Q.defer();
                wizletModulesPromiseArr.push(layoutDefer.promise);

                self.getWizletInfos(actionXMLName, extraWizletInfo.bindingParticipant).then(function(wizletInfos) {
                    var optionalScript = jQuery.parseJSON($(wizletInfos.wizletServerHtml)[0].innerHTML);
                    container.append(wizletInfos.wizletServerHtml); // Note: first render any server side HTML
                    var context = container;
                    context.find('.appliedLayout').remove();

                    if (optionalScript != null) {


                        if (optionalScript.layoutInEvent || optionalScript.layoutPath) { //if optional layout 
                            var layoutPaths = getLayoutPath(optionalScript, self);

                            require(layoutPaths, function(template) {

                                var placeholderForComponent = function(wizletDiv) {
                                    return '<div id="move-' + $(wizletDiv).attr('id') + '" class="placeholder forComponent"></div>';
                                }

                                var wizlets = container.find('.wizlet');
                                var named = {};
                                var unnamed = [];
                                $.each(wizlets, function(i, wiz) {
                                    var info = votesite.getWizletInfoFromInfoElement($(wiz).find('script.wizletInfo'));
                                    if (info && info.wizletName) {
                                        named[info.wizletName] = placeholderForComponent(wiz);
                                    } else {
                                        unnamed.push(placeholderForComponent(wiz))
                                    }
                                });

                                var buttons = context.find('#sendContainer');
                                it = {
                                    components: $.map(wizlets, function(n) { return [placeholderForComponent(n)] }),
                                    buttons: $.map(buttons, function(button, i) { return '<div id="move-button-' + i + '" class="placeholder forButton"></div>' }),
                                    namedComponents: named,
                                    unnamedComponents: unnamed
                                }
                                html = template(it);
                                $('<div class="appliedLayout"></div>').append(html).insertBefore(wizlets.first());
                                $.each(wizlets, function(i, wiz) {
                                    var id = $(wiz).attr('id');
                                    context.find('#' + id).insertBefore('#move-' + id);
                                });

                                var layoutHasButtons = context.find('.placeholder.forButton').length > 0;
                                $.each(buttons, function(i, button) {
                                    $(button).insertBefore('#move-button-' + i);
                                });
                                context.find('.placeholder').remove();

                                layoutDefer.resolve(true);
                            });
                        } else {
                            layoutDefer.resolve(true); // nothing to defer so we resolve the deferred promise
                        }

                    } //end if optional layout
                    else {
                        layoutDefer.resolve(true); // nothing to defer so we resolve the deferred promise
                    }


                    $.each(wizletInfos, function(index, wizletInfo) {
                        var wizletType = wizletInfo.wizletType;
                        if (wizletType) {
                            if (extraWizletInfo.holdData) {
                                var ideaId = $(container).attr('data-idea-container');
                                var filterQuestion = extraWizletInfo.questions.filter(function(que) {
                                    return que.questionName === ideaId;
                                })[0];
                                tabWizletInfo.push($.extend({}, wizletInfo.wizletInfo, filterQuestion));
                            } else {
                                tabWizletInfo.push($.extend({}, wizletInfo.wizletInfo, extraWizletInfo));
                            }

                            containerInfo.push(container.find(wizletInfo.element));
                            if (typeof(wizletsReady) !== "undefined") {
                                wizletsReady(); // Note: to decorate any buttons inside the embedded wizlet (like Undo on QuestionSingleChoice)
                            }
                            if (wizletInfo.customJS) {
                                wizletModulesPromiseArr.push(wizerApi.getJS(wizletInfo.customJS));
                            } else {
                                wizletModulesPromiseArr.push(wizerApi.getWizletModule(wizletType));
                            }


                        }
                    });

                    var scopePromises = [];
                    var allWizlets = [];

                    //wait for all modules getting loaded
                    //var WizerModel = require("WizerModel");
                    Q.all(wizletModulesPromiseArr).done(function(results) {

                        results.splice(0, 1); // removing the promise for layout loading
                        $.each(results, function(indx, result) {
                            var tabWizlet = result.wizletModule.getRegistration(); // Note: instantiate the wizlet
                            result.wizletInstance = tabWizlet; // Note: return the instance, so its unloadHandler can be invoked later
                            var result = true;
                            tabWizlet.tempIndx = indx;

                            var wizletInfo = tabWizletInfo[indx]
                            var trackQuestionId = self.parseDataBinderAndTrackQuestions(wizletInfo);
                            var scope = new Scope({
                                questions: wizletInfo.scope,
                                meta: wizletInfo.meta,
                                wizerApi: self,
                                model: wizletInfo.model,
                                fields: wizletInfo.fields,
                                trackQuestionId: trackQuestionId,
                                bindingParticipant: wizletInfo.bindingParticipant,
                                isDelivery: wizletInfo.isDelivery
                            });
                            tabWizlet.localScope = scope;
                            scopePromises.push(scope.eval(false, true, actionXMLName));

                            allWizlets.push(tabWizlet);


                        });
                        //var singletonWizerModel = WizerModel.getInstance() || new WizerModel({ wizerApi: wizerApi });
                        var wizletsLoading = [];
                        var cssLoading = [];
                        Q.all(scopePromises).done(function() {
                            //all are loaded. maybe do something later
                            self.wizerModel.getData(true).then(function() {
                                var wizletCount = allWizlets.length;
                                for (i = 0, ii = wizletCount; i < ii; i++) {
                                    tabWizlet = allWizlets[i];
                                    var indx = tabWizlet.tempIndx;

                                    if (tabWizletInfo[indx] && tabWizletInfo[indx].Theme && tabWizletInfo[indx].ThemePath && !tabWizletInfo[indx].preventDefaultCss) {
                                        tabWizletInfo[indx].templatePath = tabWizletInfo[indx].ThemePath.substring(0, tabWizletInfo[indx].ThemePath.lastIndexOf('/') + 1) + 'html/';
                                        cssLoading.push(require(['css!' + tabWizletInfo[indx].ThemePath])); //Load the css for the wizlet
                                    }

                                }

                                //loading.resolve(results);
                                Q.all(cssLoading).done(function() {
                                    for (i = 0, ii = wizletCount; i < ii; i++) {
                                        tabWizlet = allWizlets[i];
                                        var indx = tabWizlet.tempIndx;
                                        if (tabWizlet.loadHandler) {
                                            var wizletInfo = tabWizletInfo[indx];
                                            var loadHandlerResult = tabWizlet.loadHandler(context, wizletInfo, containerInfo[indx], wizerApi); // Note: now render any client side HTML
                                            var wizletLoading = Q(loadHandlerResult); // Note: loadHandler may not return a Q.promise
                                            wizletLoading.then(function(res) {
                                                self.makeEditableTextEditable(containerInfo[indx]);
                                                if (addShieldCallback != null)
                                                    addShieldCallback(container);
                                            });
                                            wizletsLoading.push(wizletLoading);
                                        }

                                    };

                                    Q.all(wizletsLoading).done(function() {
                                        //all are loaded. maybe do something later
                                        loading.resolve(results);
                                    });
                                });
                            });
                        });
                    });



                });
                return loading.promise;
            };

            Api.prototype.getJS = function(path) {
                var wizletPath = path;
                var wizletLoading = new Q.defer();
                require([wizletPath], function(wizletModule) {
                    wizletLoading.resolve({
                        wizletModule: wizletModule
                    });
                });
                return wizletLoading.promise;
            };

            Api.prototype.getWizletModule = function(wizletType) {
                var wizletModulePath = 'wizletresources/' + wizletType + '/js/' + wizletType[0].toLowerCase() + wizletType.substr(1);
                var wizletModuleLoading = new Q.defer();
                require([wizletModulePath], function(wizletModule) {
                    wizletModuleLoading.resolve({
                        wizletModule: wizletModule
                    });
                });
                return wizletModuleLoading.promise;
            };

            Api.prototype.updateEntry = function(options) {
                return Q(AjaxGetJson("VoteAPI", "UpdateEntry", options)).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            };

            Api.prototype.createEntry = function(options) {
                return Q(AjaxGetJson("VoteAPI", "CreateEntry", options)).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            };

            Api.prototype.updateDimensions = function(options) {
                return Q(AjaxGetJson("VoteAPI", "UpdateDimensions", options)).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            };

            Api.prototype.makeEditableTextEditable = function(wizlet) {
                    var self = this;
                    if (!Wizer.editMode) {
                        return;
                    }
                    var $wizlet = $(wizlet);
                    if (!$wizlet.html()) {
                        return;
                    }

                    require(['css!../styles/editableText'], function(editableTextCss) {
                        require(['froala', 'css!fontAwesomev4', 'css!lib/froala/css/froala_editor.min.css', 'css!lib/froala/css/froala_style.min.css'], function(FroalaEditor, FontAwesomeCss, FroalaEditorCss, FroalaStyleCss) {
                            require(['lib/froala/js/plugins/font_size.min', 'lib/froala/js/plugins/lists.min', 'lib/froala/js/plugins/block_styles.min', 'lib/froala/js/plugins/colors.min'], function(FroalaEditor, FontSizePlugin, BlockStylesPlugin, FroalaColorsPlugin) {
                                $wizlet.find('span.editableText').each(function(index, span) {
                                    self.addEditableTextHandling($(span));
                                });
                                $wizlet.trigger('wizer:editWysiwyg:css');
                            });
                        });
                    });
                    // backup for .editableText s that are added dynamically - but the above is needed to win over disabled buttons
                    //$(document).on('click', 'span.editableText', self.editTextWithBootstrapWysiwyg); // earlier it were: $wizlet.find... but the confirmDialog is place outside the wizlet 
                    $(document)
                        .off('click', 'span.editableText:not(.froala-box)', self.addEditableTextHandlingOnClick)
                        .on('click', 'span.editableText:not(.froala-box)', self.addEditableTextHandlingOnClick); // earlier it were: $wizlet.find... but the confirmDialog is place outside the wizlet
                    //}
                },

                Api.prototype.addEditableTextHandlingOnClick = function(e) {
                    Api.prototype.addEditableTextHandling($(e.target).closest('.editableText'))
                },

                Api.prototype.addEditableTextHandling = function(span) {
                    var self = this;
                    if (!Wizer.editMode) {
                        return;
                    }
                    if (span) {
                        // console.log("adding editable text handling for span " + span.data('key'));
                        $(span).closest('.disabled').removeClass("disabled");
                        var dataKey = span.data('key');
                        var dataField = span.data('field');
                        var dataBehaviorField = span.data('behaviorfield');
                        var dataBehaviorFieldKey = span.data('behaviorkey');
                        var dataId = span.data('id');

                        var origVal = span.html();

                        span.on('click', function(e) {
                            e.stopPropagation();

                            // clean up if tokens have warnings 
                            var content = span.editable('getHTML');
                            var cleanedTokens = decodeURIComponent(content.replace(/\${([^${}]*) ?!!![^{}]*!!! ?([^{}]*)}/g, function(all, before, after) { return '${' + before + after + '}'; }));
                            if (content !== cleanedTokens) {
                                span.editable('setHTML', cleanedTokens);
                                origVal = cleanedTokens;
                            }
                        });

                        var saveText = function() {

                            var newVal = span.editable('getHTML');
                            newVal = decodeURIComponent(newVal.replace(/(\r\n|\n|\r)/gm, ""));

                            if (newVal == origVal) {
                                return;
                            }



                            self.saveTextContent(dataKey, newVal, dataField, dataId, dataBehaviorField, dataBehaviorFieldKey)
                                .then(function() {
                                    logger.log('saved: ' + dataKey);
                                    try {
                                        origVal = decodeURIComponent(span.editable('getHTML'));
                                    } catch (e) {
                                        logger.log('Cannot getHTML after save; navigated away?');
                                    }
                                    // triggering works better in the inbox case because we also need to change non-visible text
                                    //$('.editableText:not(.froala-box)[data-key="' + dataKey + '"]').each(function (index, div) {
                                    //    if ($(div).html() != newVal) {
                                    //        console.log("I would change '"+  $(div).html() + "' to '" + newVal + "'");
                                    //    }
                                    //});
                                    var clone = span.clone();
                                    clone.html(newVal);
                                    var element = $('<div/>');
                                    element.append(clone);
                                    $(document).trigger('wizer:updated:textvalue', [dataId, dataField, element.html()]);
                                })
                                .fail(function(error) {
                                    logger.log(true, "could not save text: " + error);
                                    span.editable('setHTML', origVal, false);
                                })
                                .done();
                        }
                        self.renderEditor(span, saveText);
                        span.trigger('wizer:editWysiwyg:loadet');
                    }
                }


            Api.prototype.renderEditor = function(span, saveText, options, bindingParticipantId) {

                // check if user has Froala editor access;
                var eventId = $('#eventId').html() * 1;
                var buttons = [
                    'bold', 'italic', 'underline', 'strikeThrough', //'subscript', 'superscript', 'fontFamily', 
                    'fontSize', 'color', //'formatBlock',
                    'blockStyle', 'inlineStyle', 'align', 'insertOrderedList', 'insertUnorderedList', //'outdent', 'indent', 
                    'selectAll', 'createLink', 'insertImage', //'insertVideo', 'table', 
                    'undo', 'redo', 'html', 'customSave', // 'save', //'insertHorizontalRule', 'uploadFile',
                    'removeFormat'
                ]
                if (!!eventId && !!top.document.querySelector('[data-eventid="' + eventId + '"] span') && top.document.querySelector('[data-eventid="' + eventId + '"] span').getAttribute('disabled')) {
                    buttons = []
                }
                var defaultOptions = {
                    inlineMode: true,
                    paragraphy: false,
                    initOnClick: true,
                    placeholder: '',
                    imageLink: false,
                    spellcheck: true,

                    key: Wizer.FroalaLicenseKey ? Wizer.FroalaLicenseKey : 'vmdbB1pH-9x==', //default key is for bts.com. Check web.config for other domains
                    buttons: buttons,
                    customButtons: customButtons,
                    // Not used - custom save to ensure encoding
                    //saveURL: '/Wizer/TextContent/Save',
                    //saveParam: 'value',
                    //saveParams: {
                    //    key: dataKey,
                    //    languageKey: 'en',
                    //    eventName: Wizer.EventName
                    //}
                    // Set the image upload parameter.
                    imageUploadParam: 'file',

                    // Set the image upload URL.
                    imageUploadURL: '/Wizer/UserAdmin/UploadImage',

                    // Additional upload params.
                    imageUploadParams: {
                        eventTitle: this.eventName(),
                        folder: 'textContent',
                        bindingParticipantId: bindingParticipantId
                    },

                    // Set request type.
                    imageUploadMethod: 'POST',

                    // Set max image size to 5MB.
                    imageMaxSize: 5 * 1024 * 1024,

                    // Allow to upload PNG and JPG.
                    imageAllowedTypes: ['jpeg', 'jpg', 'png']
                };

                if (options) {
                    $.extend(defaultOptions, options);
                }
                //console.log("setting froala on " + span.data('key'));
                var customButtons = {};
                if (saveText) {
                    customButtons = {
                        // Alert button with Font Awesome icon.
                        customSave: {
                            title: 'Save',
                            icon: {
                                type: 'font',

                                // Font Awesome icon class fa fa-*.
                                value: 'fa fa-save'
                            },
                            callback: function() {
                                saveText();
                            },
                            refresh: function() {}
                        }
                    }
                }
                if (!span.editable) {
                    return;
                }
                span.editable(defaultOptions)
                    .on('editable.beforeImageUpload', function(e, editor, images) {
                        // Return false if you want to stop the image upload.
                        if (images) {
                            var allowedFileTypes = 'jpeg, jpg, png';
                            var fileExtension = images[0].name.split('.').pop();
                            if (allowedFileTypes.indexOf(fileExtension.toLowerCase()) == -1) {
                                alert(fileExtension + ' is not supported file format.\nPlease upload only ' + allowedFileTypes + ' file format.');
                                return false;
                            }
                        }
                    })
                    .on('editable.afterImageUpload editable.imageAltSet editable.imageInserted editable.imageReplaced editable.afterRemoveImage editable.afterUploadPastedImage editable.imageFloatedLeft editable.imageFloatedNone editable.imageFloatedRight editable.imageInserted editable.imageLinkInserted editable.imageLinkRemoved editable.imageReplaced editable.imageResizeEnd',
                        function(e, editor, $img, response) {
                            var isDevelopmentPlan = $(e.currentTarget).hasClass('dpe-textArea__textarea');
                            if (!isDevelopmentPlan) {
                                // Image changes in the editor, force save
                                saveText();
                            }
                        }
                    )
                    .on('editable.imageError', function(e, editor, error, response) {
                        // not sure about error codes, they are from a later version of froala /Morten
                        if (error.code == 1) { logger.log('editable.image.error' + error.code) } // Bad link.
                        else if (error.code == 2) { logger.log('editable.image.error' + error.code) } // No link in upload response.
                        else if (error.code == 3) { logger.log('editable.image.error' + error.code) } // Error during image upload.
                        else if (error.code == 4) { logger.log('editable.image.error' + error.code) } // Parsing response failed.
                        else if (error.code == 5) { logger.log('editable.image.error' + error.code) } // Image too text-large.
                        else if (error.code == 6) { logger.log('editable.image.error' + error.code) } // Invalid image type.
                        else if (error.code == 7) { logger.log('editable.image.error' + error.code) } // Image can be uploaded only to same domain in IE 8 and IE 9.
                        else { logger.log('editable.image.error' + error.code) }
                        // Response contains the original server response to the request if available.
                    });
                // Not used - custom save to ensure encoding    
                //.on('editable.afterSave', function (e, editor, data) {
                //    if (data.success) {
                //        logger.log('saved: ' + dataKey);
                //        origVal = span.editable('getHTML');
                //        //span.editable('destroy');
                //    } else {
                //        logger.log(true, "could not save text: " + data.message);
                //        span.editable('setHTML', origVal, false);
                //    }
                //})
                //.on('editable.saveError', function (e, editor, error) {
                //    logger.log(true, "could not save text: " + error);
                //    span.editable('setHTML', origVal, false);
                //})
                if (saveText) {
                    span.on('editable.blur', function() {
                        //span.editable('save');
                        saveText();
                    });
                }
            }

            var IsValidJSONString = function(str) {
                try {
                    JSON.parse(str);
                } catch (e) {
                    return false;
                }
                return true;
            };
            Api.prototype.editText = function(span) {
                //    function editText(e) {
                //    var self = this;
                //    var span = $(e.target).closest('.editableText');
                var self = this;
                var key = span.data('key');
                var origVal = span.cleanHtml();
                var inp = $('<textarea />').val(origVal);
                inp.width(Math.max(Math.min(span.width() * 1.5 + 50, $('body').width() - 100), 300));
                inp.height(Math.max(Math.min(span.height() * 1.5 + 10, $('body').height() - 300), 50));
                span.html('');
                span.after(inp);
                var space = $('<p>&nbsp;</p>');
                inp.after(space);
                inp.focus();
                inp.on('change', function() {
                    self.saveTextContent(key, inp.val())
                        .fail(function(error) {
                            var message = error.message ? error.message : error.statusText;
                            logger.log(true, "could not save text: " + message);
                            span.html(origVal);
                        })
                        .done();
                });
                inp.on('blur', function() {
                    span.html(inp.val());
                    inp.remove();
                    space.remove();
                });
            }

            // ToDo: not ready
            Api.prototype.getAction = function(aId) {
                // Note: not asynchronous
                if (this.isOffline()) {
                    return this.event.actions[aId];
                } else {
                    // ...
                }
            };

            Api.prototype.isOffline = function() {
                // Note: not asynchronous
                return this.connectionStatus.isOffline();
            };

            Api.prototype.historyIsEmpty = function() {
                return (this.isOffline() && this.history.length == 0);
            }

            Api.prototype.forceOffline = function() {
                this.connectionStatus.forceOffline();
            }

            Api.prototype.resetConnectionStatus = function() {
                this.connectionStatus.reset();
            }

            Api.prototype.saveTextContent = function(key, val, field, eveId, behaviorField, behaviorFieldKey) {

                var toSave = TextContentEncoder.encode(val);
                var module = self.wizletRegistration;

                var postData = { key: key, value: toSave, eventName: this.eventName, module: module.type, field: field, id: eveId, behaviorName: behaviorField, behaviorFieldKey: behaviorFieldKey };

                return Q(AjaxGetJson("TextContent", "Save", postData))
                    .then(function(response) {
                        if (!response.success) {
                            throw new Error(response.message || response.Message);
                        }
                    });
            }

            Api.prototype.resetVotes = function() {
                return Q(AjaxGetJson("EventAPI", "ResetMyVotes", {}))
                    .then(function(returnValue) {
                        if (!returnValue) throw new Error("no response from ResetMyVotes");
                        if (!returnValue.Success) throw new Error("ResetMyVotes failed, response: " + JSON.stringify(returnValue));
                        return returnValue.Success;
                    });
            }

            /*****Please use individual functions eg.copyMyModelVotesToDb, copyVotesFromForeman, copyMyVotesToModel, copyTrackParticipantVotesToMy  if u want spliited behaviour of copying and restoring
             This function will copy all data from your model to your DB
             trackQuestion is specified & it will copy data to your track participants and sync them as well
             qIds => [{Id:1, calcBind: 'TlSource'},{Id: 2, calcBind: 'TlSource2'}]
             trackQuestion='My_TRACK'
             model = 'model/PfizerEcoSim.js'****/
            Api.prototype.syncModelToDatabase = function(qIds, trackQuestion) {
                var self = this;
                var wait = self.copyMyModelVotesToDb(qIds, model);
                // Q.all(promisearray, function () {
                return wait.then(function() {
                    return self.copyTrackParticipantVotesToMy(trackQuestion, qIds).then(function() {
                        return self.copyMyVotesToModel(qIds, model);
                    });
                });
            }

            /*****Please use individual functions eg.copyMyModelVotesToDb, copyVotesFromForeman, copyMyVotesToModel, copyTrackParticipantVotesToMy  if u want spliited behaviour of copying and restoring
            This function will copy all data from your DB to your model
            trackQuestion is specified & it will copy data to your track participants and sync them as well
            qIds => [{Id:1, calcBind: 'TlSource'},{Id: 2, calcBind: 'TlSource2'}]
            trackQuestion='My_TRACK'
            model = 'model/PfizerEcoSim.js'*/
            Api.prototype.syncDatabaseToModel = function(qIds, trackQuestion, model) {
                var self = this;
                var wait = self.copyMyVotesToModel(qIds, model);
                // Q.all(promisearray, function () {
                return wait.then(function() {
                    return self.copyTrackParticipantVotesToMy(trackQuestion, qIds).then(function() {
                        return self.copyMyVotesToModel(qIds, model);
                    });
                });

            }

            //qIds => [{Id:1, calcBind: 'TlSource'},{Id: 2, calcBind: 'TlSource2'}]
            //model = 'model/PfizerEcoSim.js'
            Api.prototype.copyMyModelVotesToDb = function(qIds, model) {
                var self = this;
                var wait = new Q.defer();
                var promisearr = [];
                $.each(qIds, function(indx, q) {
                    var calcBind = q.calcBind;
                    //fetch the data from the model
                    var promise = self.calcBinderCache.getCalcValue(model, calcBind).then(function(value) {
                        if (value) {
                            value = Number(value);
                            self.addVote(q.Id, value);
                        }
                    });
                    promisearr.push(promise);
                });

                Q.all(promisearr).done(function(values) {
                    wait.resolve();
                });
                return wait.promise;
            }

            //qIds => [1,2]
            //trackQuestion='My_TRACK'
            Api.prototype.copyVotesFromForeman = function(qIds, trackQuestion) {
                var self = this;
                var copying = new Q.defer();
                var foremanQuestionId = self.getQuestionIdByName(trackQuestion);
                self.getForemanId(foremanQuestionId, self.isDelivery)
                    .then(function(foremanId) {
                        //console.log("foremanId::>"+foremanId);
                        return self.getVotesByParticipant(qIds, foremanId);
                    })
                    .then(function(votes) {
                        if (votes) {
                            var arrVotes = votes.votes;
                            for (var vote in arrVotes) {
                                var tmpScore = arrVotes[vote][0];
                                tmpScore = Number(tmpScore);
                                self.addVote(vote, tmpScore);
                            }
                        }
                        copying.resolve();
                    })
                    .fail(function(err) {
                        wizerApi.showError("Something was failing and you should handle it: " + err.message);
                    })
                    .done();
                return copying.promise;
            };

            //qIds => [{Id:1, calcBind: 'TlSource'},{Id: 2, calcBind: 'TlSource2'}]
            //model = 'model/PfizerEcoSim.js'
            Api.prototype.copyMyVotesToModel = function(qIds, model) {
                var self = this;
                var wait = new Q.defer();
                var questionIds = [];
                $.each(qIds, function(indx, qId) {
                    questionIds.push(qId.Id);
                });

                var gettingVotes = self.getVotesByParticipant(questionIds, Wizer.ParticipationId);

                gettingVotes.then(function(votes) {
                    var vote = votes.votes
                    $.each(qIds, function(indx, qId) {
                        if (vote[qId.Id]) {
                            if (vote[qId.Id][0]) {
                                var set1 = self.calcBinderCache.setCalcValue(model, qId.calcBind, vote[qId.Id][0]);
                                // promisearray.push(set1);
                                //console.log('setting in model tlInputPayerRd1E1 to ' + vote[qId1][0]);
                            }
                        }
                    });
                    wait.resolve();
                });
                return wait.promise;
            }

            //qIds => [{Id:1, calcBind: 'TlSource'},{Id: 2, calcBind: 'TlSource2'}]
            Api.prototype.copyTrackParticipantVotesToMy = function(trackQuestion, qIds) {
                var copying = new Q.defer();
                var trackQid = self.wizerApi.getQuestionIdByName(trackQuestion);
                var tracks = self.wizerApi.getTrackParticipants(trackQid);

                var questionIds = [];
                $.each(qIds, function(indx, qId) {
                    questionIds.push(qId.Id);
                });
                tracks.then(function(participants) {
                    $.each(participants.participants, function(index, part) {
                        if (part.Id != Wizer.ParticipationId) {
                            //get vote of this particpant and save to mine
                            var get = self.wizerApi.getVotesByParticipant(questionIds, part.Id);
                            get.then(function(myVotes) {
                                    if (myVotes) {
                                        var arrVotes = myVotes.votes;
                                        for (var vote in arrVotes) {
                                            var tmpScore = arrVotes[vote][0];
                                            if (tmpScore) {
                                                tmpScore = Number(tmpScore);
                                                self.wizerApi.addVote(vote, tmpScore);
                                            }
                                        }
                                    }
                                })
                                .fail(function(err) {
                                    wizerApi.showError("Something was failing and you should handle it: " + err.message);
                                })
                                .done();
                        }
                    });
                    copying.resolve();
                });
                return copying.promise;
            };

            Api.prototype.changeVoter = function(options) {
                return Q(AjaxGetJson("VoteAPI", "ChangeVoter", options));
            };

            Api.showError = function(error) {
                // console.log(error);
                if (error.status && error.status == "500") {
                    // console.log(error);
                } else if (error.stack) {
                    logger.logExceptionOnServer(error);
                } else {
                    logger.logErrorOnServer(error, JSON.stringify(error));
                }
            }
            Api.prototype.getParticipants = function(trackQuestionId) {
                return Q(AjaxGetJson("Vote", "GetParticipants", { trackQuestionId: trackQuestionId }));
            };

            Api.prototype.getParticipantIdByEmail = function(email) {
                return Q(AjaxGetJson("Vote", "GetParticipantId", { email: email }));
            };
            //input is the actionAxml Name 
            // (used for navigation component)
            //output is the Id of that action to which the actionXML belongs to
            Api.prototype.getActionIdByName = function(actionXMLName) {
                return Q(AjaxGetJson("Vote", "GetActionIdByName", { actionXMLName: actionXMLName }));
            };

            //input is the actionId 
            // (used for navigation component)
            //output is the name of the actionXML of next action
            Api.prototype.getNextAction = function(actionId) {
                return Q(AjaxGetJson("Vote", "GetNextAction", { actionId: actionId }));
            }

            //input is the actionScriptName 
            //output is the name of the actionXML of next action
            Api.prototype.getNextAutonextAction = function(actionXmlName) {
                return Q(AjaxGetJson("Vote", "GetNextAutonextAction", { scriptName: actionXmlName }));
            }

            //input is the actionId 
            // (used for navigation component)
            //output is the name of the actionXML of next action
            Api.prototype.getPreviousAction = function(actionId) {
                return Q(AjaxGetJson("Vote", "GetPreviousAction", { actionId: actionId }));
            }

            Api.prototype.getCurrentAction = function() {
                return Q(AjaxGetJson("VoteAPI", "GetCurrentAction")).then(function(response) {
                    if (!response.success && response.isDisabled) logout();
                    return response;
                });;
            };

            Api.prototype.getLanguage = function() {
                return Q(AjaxGetJson("EventApi", "GetLanguage"));
            }

            Api.prototype.setLanguage = function(languageKey) {
                return Q(AjaxGetJson("EventApi", "ChangeLanguage", { "languageKey": languageKey }));
            }

            Api.prototype.getLanguages = function(languageKey) {
                return Q(AjaxGetJson("EventApi", "GetLanguages"));
            }

            Api.prototype.getAllPulseEmails = function(participationid, moduleId) {
                return Q(AjaxGetJson("PulseEmail", "GetAll", { participationid: participationid, moduleId: moduleId })).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            }

            Api.prototype.getAllPulseEmailTemplates = function(fictiveStartTime) {
                return Q(AjaxGetJson("PulseEmail", "GetAllTemplates", { fictiveStartTime: fictiveStartTime.toISOString() })).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            }

            Api.prototype.updateEmailStatus = function(emailId, statusId) {
                return Q(AjaxGetJson("PulseEmail", "UpdateStatus", { emailId: emailId, statusId: statusId })).then(function(response) {
                    if (!response.success) throw response.message;
                    return response;
                });
            }

            Api.prototype.sendPulseEmail = function(email) {
                return $.ajax({
                    url: '/Wizer/PulseEmail/Send',
                    data: email,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function(response) {
                        return response;
                    },
                    error: function(response) {
                        return response;
                    }
                });
            }

            Api.prototype.saveDraftPulseEmail = function(email) {
                return $.ajax({
                    url: '/Wizer/PulseEmail/SaveDraft',
                    data: email,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function(response) {
                        return response;
                    },
                    error: function(response) {
                        return response;
                    }
                });
            }

            Api.prototype.CreatePulseCalendarEntry = function(calEntry) {
                return Q(AjaxGetJson("PulseCalendar", "Create", calEntry))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.UpdatePulseCalendarEntry = function(calEntry) {
                return Q(AjaxGetJson("PulseCalendar", "Update", calEntry))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.DeletePulseCalendarEntry = function(calEntryId) {
                return Q(AjaxGetJson("PulseCalendar", "Delete", { calId: calEntryId }))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.getPulseCalendar = function(participationid) {
                return Q(AjaxGetJson("PulseCalendar", "GetAll", { participationId: participationid }))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.getPulseCalendarTemplates = function(moduleId) {
                return Q(AjaxGetJson("PulseCalendar", "Templates", { moduleId: moduleId, withKeys: true }, null, null, null, "GET"))
                    .then(function(response) {
                        //if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.getContactLayout = function(name) {
                return Q(AjaxGetJson("PulseContact", "GetLayout", { name: name }))
                    .then(function(response) {
                        if (!response.success) {
                            logger.log(true, 'Problem getting layout ' + name + ':' + response.message);
                            throw response.message;
                        }
                        return response;
                    });
            }

            Api.prototype.getContacts = function(participationId, workOnTemplates, layoutid, assessmentModule) {

                if (assessmentModule == null)
                    assessmentModule = 0;

                return Q(AjaxGetJson("PulseContact", "GetAll", { participationId: participationId, workOnTemplates: workOnTemplates, layoutid: layoutid, moduleid: assessmentModule }))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.saveContact = function(contact) {
                return Q(AjaxGetJson("PulseContact", "Save", contact))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.deleteNote = function(noteId) {
                return Q(AjaxGetJson("PulseContact", "DeleteNote", { noteId: noteId }))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.saveNote = function(note) {
                return Q(AjaxGetJson("PulseContact", "SaveNote", note))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.getContactNotes = function(participationId, contactId) {
                return Q(AjaxGetJson("PulseContact", "GetNotes", { participationId: participationId, contactId: contactId }))
                    .then(function(response) {
                        if (!response.success) throw response.message;
                        return response;
                    });
            }

            Api.prototype.readQROptions = function(options) {
                var self = this;
                if (options.participantEmail) {
                    self.getParticipantIdByEmail(options.participantEmail).then(function(id) {
                        if (id == 0) {
                            self.showError('User with email id ' + options.participantEmail + ' not found in this session');
                        } else {
                            self.openQRAction(options, id, options.participantEmail);
                        }
                    });
                } else {
                    self.openQRAction(options, null, null);
                }
            };

            //This is called when the QR action is performed and a url is hit
            Api.prototype.openQRAction = function(options, participantId, participantEmail) {
                var self = this;
                if (options.actionName) {
                    var actionXMLName = options.actionName;
                    var self = this;
                    //we will add it to the embeddedwizlet container
                    var profileContainer = $(document).find('#embeddedWizletContainer');
                    var extraWizletInfo = {
                        bindingParticipant: participantId,
                        bindingParticipantEmail: participantEmail,
                        editable: participantId == Wizer.ParticipationId
                    };
                    self.showActionAsPopUp(actionXMLName, profileContainer, profileContainer, 'QRnavigationpopup', extraWizletInfo);
                } else {
                    //check if it is a valid url and if yes open a popup
                    if (self.isValidURL(options)) {
                        if (options.toLowerCase().indexOf('vote/assess') != -1) {
                            //This is an assessment call and hence we send ajax request from inside wizer
                            return $.ajax({
                                url: options,
                                cache: false,
                                contentType: false,
                                processData: false,
                                type: 'GET',
                                success: function(response) {
                                    if (response.success) {
                                        alert('success');
                                    } else {
                                        alert(response.message);
                                    }
                                },
                                error: function(response) {
                                    alert(response.message);
                                }
                            });
                        } else {
                            window.open(options, '_system');
                        }
                    }
                }
            };
            Api.prototype.isValidURL = function(str) {
                var urlregex = new RegExp(
                    "^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\'\\\+&amp;%\$#\=~_\-]+))*$");
                return urlregex.test(str);
            }
            Api.prototype.showActionAsPopUp = function(actionXML, context, elem, dialogClass, extraWizletInfo) {
                var self = this;
                if (actionXML != 'undefined' && actionXML != undefined && actionXML != '') {
                    //check if instance is created
                    var diag = elem.dialog("instance");
                    if (diag) {
                        elem.dialog("destroy");
                    }
                    elem.html('');
                    self.loadActionInContainer(actionXML, null, context, elem, extraWizletInfo).then(function(wizletModules) {
                        elem.dialog({
                            draggable: false,
                            width: '99%',
                            dialogClass: dialogClass,
                            //hide: { effect: "explode", duration: 1000 },
                            //show: { effect: "blind", duration: 800 },
                            modal: true,
                            //position: { my: "left top", at: "left bottom", of: "#main"},
                            resizable: false,
                            close: function(event, ui) {
                                if (wizletModules && wizletModules.length > 0) {
                                    $.each(wizletModules, function(index, module) {
                                        if (module.wizletInstance.unloadHandler) {
                                            module.wizletInstance.unloadHandler();
                                        }
                                    });
                                    if (diag) {
                                        elem.dialog("destroy");
                                        elem.html('');
                                        //elem.remove();
                                    }
                                }

                                $(document).trigger('wizer:DevelopmentPlanList:refresh', ['close']);
                            },
                            create: function(event, ui) {
                                $("body").css({ overflow: 'hidden' })
                            },
                            beforeClose: function(event, ui) {
                                $("body").css({ overflow: 'inherit' })
                            }
                        });
                    }).then(function() {
                        $(document).trigger("wizer:action:init", actionXML);
                    });
                }
            };

            Api.prototype.getCallForms = function(entryQuestion, participantEmail, myOwn) {
                return Q(AjaxGetJson("Vote", "GetCallForms", { entryQuestion: entryQuestion, participant: participantEmail, myOwn: myOwn }));
            };

            Api.prototype.clearAssessment = function(question, email, participantId, callFormEntryResponseId) {
                if (question.indexOf('.') > 0) {
                    question = question.substr(question.indexOf('.') + 1, question.length);
                }
                var emailData = email ? email : participantId.toString();
                return Q(AjaxGetJson("Vote", "ClearAssessment", { questionName: question, email: emailData, callFormEntryResponseId: callFormEntryResponseId }));
            };


            Api.prototype.addAssessment = function(question, responseText, email, participantId, callFormEntryResponseId) {
                return this.wizerModel.addAssessment(question, responseText, email, participantId, callFormEntryResponseId);
            };

            Api.prototype.addAssessmentAPI = function(question, responseText, email, participantId, callFormEntryResponseId) {
                var self = this;
                if (responseText == "") {
                    return self.clearAssessment(question, email, participantId, callFormEntryResponseId);
                }
                if (question.indexOf('.') > 0) {
                    question = question.substr(question.indexOf('.') + 1, question.length);
                }
                var emailData = email ? email : participantId.toString();
                var adding = Q(AjaxGetJson("Vote", "AddAssessment", { question: question, vote: responseText, email: emailData, entryResponseId: callFormEntryResponseId }));
                return adding;
            };

            Api.prototype.addAssessmentEntry = function(question, responseText, email, participantId) {
                if (question.indexOf('.') > 0) {
                    question = question.substr(question.indexOf('.') + 1, question.length);
                }
                var emailData = email ? email : participantId.toString();
                var adding = Q(AjaxGetJson("Vote", "AddAssessmentEntry", { question: question, vote: responseText, email: emailData }));
                return adding;
            };

            Api.prototype.getAssessment = function(question, email, participationId, isEvalForm) {
                return this.wizerModel.getAssessment(question, email, participationId, isEvalForm);
            }

            //This will get all stuff that i have assessed as an assesor/user
            Api.prototype.getAssessmentAPI = function(question, email, participantId, isEvalForm) {
                //other piece
                var self = this;
                var response = { votes: {} };
                var list = '';
                var listId = '';
                $.each(question, function(index, q) {
                    if (q.indexOf('.') > 0) {
                        q = q.substr(q.indexOf('.') + 1, q.length);
                    }
                    var qid = self.getQuestionIdByName(q);
                    if (!isNaN(qid)) {
                        list = list + q + ",";
                        listId = listId + qid + ",";
                    }
                    response.votes[qid] = [];
                });

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: GetMyAssessed, getOffline not implemented")
                } else {
                    var ajaxVotes = null;
                    if (!isEvalForm && !participantId)
                        ajaxVotes = Q(AjaxGetJson("Vote", "GetMyAssessed?question=" + listId));
                    else // we get the assessments of other assesor for given participant
                        ajaxVotes = Q(AjaxGetJson("Vote", "GetMyAssessed?question=" + listId + "&participantId=" + participantId + "&isEvalForm=" + isEvalForm));
                    var filtered = ajaxVotes.then(function(resp) {
                        if (resp.success) {
                            for (var i = 0; resp.results && i < resp.results.length; i++) {
                                var voteArr = resp.results[i];
                                //this is the main arra whose qId = 
                                var qId = voteArr.Id;
                                var assesments = voteArr.assessments;
                                $.each(assesments, function(index, assessment) {
                                    if (assessment.assesseeId == participantId) {
                                        if (!response.votes[qId]) {
                                            logger.log(true, "strange that getAssessment got a vote for question id that we did not ask for: " + qId);
                                            response.votes[qId] = [];
                                        }
                                        response.votes[qId].push(assessment);
                                    }
                                });
                            }
                            return response;
                        } else {
                            ajaxVotes.fail(Wizer.Api.showError);
                        }

                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            };

            Api.prototype.getMyNetworks = function(question) {
                var self = this;
                var list = '';
                $.each(question, function(index, q) {
                    if (q.indexOf('.') > 0) {
                        q = q.substr(q.indexOf('.') + 1, q.length);
                    }
                    list = list + q + ",";
                    var qid = self.getQuestionIdByName(q);
                });
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getAssessed, getOffline not implemented")
                } else {
                    var ajaxVotes = Q(AjaxGetJson("Vote", "GetMyNetworks?question=" + list));
                    var filtered = ajaxVotes.then(function(resp) {
                        if (resp.success) {
                            return resp;
                        } else {
                            ajaxVotes.fail(Wizer.Api.showError);
                        }

                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            };

            Api.prototype.getAssessmentReports = function(participantId, moduleId, actionName) {
                return Q(AjaxGetJson("ReportingAPI", "GetAssessmentReportByParticipationId?participantId=" + participantId + "&moduleId=" + moduleId + "&actionName=" + actionName));
            };


            Api.prototype.getCapability = function(capabilityName, participantId, module) {
                return Q(AjaxGetJson("AssessmentRest", "GetAssessmentCapabilityByName", { capabilityName: capabilityName, eventId: Wizer.EventId, participantId: participantId, module: (module ? module.name : null) }));
            };

            Api.prototype.showError = Api.showError;

            Api.prototype.getUrlParameter = function getUrlParameter(sParam) { // http://www.jquerybyexample.net/2012/06/get-url-parameters-using-jquery.html
                var sPageURL = decodeURIComponent(window.location.search.substring(1)),
                    sURLVariables = sPageURL.split('&'),
                    sParameterName,
                    i;

                for (i = 0; i < sURLVariables.length; i++) {
                    sParameterName = sURLVariables[i].split('=');

                    if (sParameterName[0] === sParam) {
                        return sParameterName[1] === undefined ? true : sParameterName[1];
                    }
                }
            };
            Api.prototype.getLeaderCode = function(qId, trackQuestion) {
                var self = this;
                var trackQuestionId = self.getQuestionIdByName(trackQuestion);
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getLeaderCode, getOffline not implemented")
                } else {
                    var ajaxVotes = Q(AjaxGetJson("Vote", "GetLeaderCode", { qId: qId, trackQuestionId: trackQuestionId }));
                    var filtered = ajaxVotes.then(function(resp) {
                        if (resp.success) {
                            return resp.code;
                        } else {
                            ajaxVotes.fail(Wizer.Api.showError);
                        }

                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            };

            Api.prototype.checkLeaderCode = function(qId, code) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: checkLeaderCode, getOffline not implemented")
                } else {
                    var ajaxVotes = Q(AjaxGetJson("Vote", "CheckLeaderCodeForParticipant", { foremanQuestionId: qId, code: code }));
                    var filtered = ajaxVotes.then(function(resp) {
                        return resp;
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.checkExercises = function(participantId, moduleid) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: checkExercises, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Assessment", "CheckExercises", { participantId: participantId, moduleid: moduleid }, null, null, null, "GET"));
                    var filtered = promise.then(function(resp) {
                        return resp;
                    });
                    promise.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.toggleEvaluation = function(participantId, evalFormQuestionId, exerciseId, complete) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: toggleEvaluation, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Assessment", "EvalForm", { participantId: participantId, evalFormQuestionId: evalFormQuestionId, exerciseId: exerciseId, complete: complete }));
                    var filtered = promise.then(function(resp) {
                        return resp;
                    });
                    promise.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.publishCallForm = function(participantId, callFormPublishQuestionId, publish, entryQuestionResponseId, voteToClearId) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: publishCallForm, getOffline not implemented")
                } else {
                    return Q(AjaxGetJson("Assessment", "PublishCallForm", { participantId: participantId, callFormPublishQuestionId: callFormPublishQuestionId, publish: publish, callFormEntryResponseId: entryQuestionResponseId, voteToClearId: voteToClearId }))
                        .fail(Wizer.Api.showError);
                }
            }

            Api.prototype.getCapabilityOverview = function(participantId, module) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getCapabilityOverview, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Assessment", "GetCapabilityOverview", { participantId: participantId, module: (module ? module.name : null) }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.getAvailableSessions = function(startDate, moduleName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getAvailableSessions, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("AssessmentRegistration", "GetAvailableSessions", { startdate: startDate, moduleName: moduleName }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.getMonthwiseAvailableSessions = function(startDate, moduleName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getMonthwiseAvailableSessions, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("AssessmentRegistration", "GetMonthWiseAvailableSessions", { startDate: startDate, moduleName: moduleName }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.registerAssessmentSessionParticipant = function(sessionId, moduleName, timezoneOffset, timezoneName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: registerAssessmentSessionParticipant, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("AssessmentRegistration", "SelfRegister", { sessionId: sessionId, moduleName: moduleName, timezoneOffset: timezoneOffset, timezoneName: timezoneName }, null, null, null, "POST"));
                    return promise;
                }
            }

            Api.prototype.deRegisterAssessmentSessionParticipant = function(sessionId, moduleName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: registerAssessmentSessionParticipant, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("AssessmentRegistration", "SelfRegister", { sessionId: sessionId, moduleName: moduleName }, null, null, null, "DELETE"));
                    return promise;
                }
            }

            Api.prototype.sendSelfRegistrationDetails = function(sessionId, title, description, module) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: registerAssessmentSessionParticipant, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("AssessmentRegistration", "SendSelfRegistrationDetails", { sessionId: sessionId, title: title, description: description, module: module }, null, null, null, "POST"));
                    return promise;
                }
            }

            Api.prototype.getLanguagesFromVote = function() {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getLanguages, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Vote", "GetLanguages", {}, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.saveContentObjectState = function(coName, sectionName, rootElement) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: saveContentObjectState, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Gateway", "SaveState", {
                        name: coName,
                        sectionName: sectionName,
                        userLocation: moment.tz.guess(),
                        codeName: navigator.appCodeName,
                        browsername: navigator.appName,
                        version: navigator.appVersion,
                        browserPlatform: navigator.platform,
                        userAgentHeader: navigator.userAgent,
                        cookiesEnabled: navigator.cookieEnabled,
                        rootElement: rootElement
                    }, null, null, null, "POST"));
                    return promise;
                }
            }

            Api.prototype.getContentObjectDetails = function(currentCO) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getContentObjectDetails, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Gateway", "GetHierarchyDetails", { path: currentCO }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.deleteDevelopmentFeedback = function(param) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: deleteDevelopmentFeedback, getOffline not implemented")
                } else {
                    var defer = Q.defer();;

                    $.ajax({
                        url: '/Wizer/Vote/DeleteDevelopmentFeedback',
                        type: 'POST',
                        contentType: 'application/json', // This is important
                        data: JSON.stringify({ param: param }),
                        datatype: 'json',
                        success: function(response) {
                            if (response.success) {
                                defer.resolve(response);
                            } else {
                                defer.reject(response);
                            }
                        },
                        error: function(response) {
                            defer.reject(response);
                        }
                    });

                    return defer.promise;
                }
            }

            Api.prototype.saveDevelopmentFeedback = function(param) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: saveDevelopmentFeedback, getOffline not implemented")
                } else {
                    var defer = Q.defer();;

                    $.ajax({
                        url: '/Wizer/Vote/SaveDevelopmentFeedback',
                        type: 'POST',
                        contentType: 'application/json', // This is important
                        data: JSON.stringify({ param: param }),
                        datatype: 'json',
                        success: function(response) {
                            if (response.success) {
                                defer.resolve(response);
                            } else {
                                defer.reject(response);
                            }
                        },
                        error: function(response) {
                            defer.reject(response);
                        }
                    });

                    return defer.promise;
                }
            }

            Api.prototype.getDevelopmentFeedbacks = function(formQuestionId, participantId) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getDevelopmentFeedbacks, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Vote", "GetDevelopmentFeedbacks", { formQuestionId: formQuestionId, participantId: participantId }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.nextContentObjectSection = function(parentCoName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: nextContentObjectSection, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Gateway", "GetNextContentObject", { parentContentObject: parentCoName }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.checkAssignedAction = function(participantId) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: checkAssignedAction, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Vote", "DynamicAction", { participantId: participantId }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.setAssignedAction = function(participantId, action, moduleId) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: setAssignedAction, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Vote", "DynamicAction", { participantId: participantId, action: action, moduleId: moduleId }, null, null, null, "POST"));
                    return promise;
                }
            }

            Api.prototype.clearModelCache = function(participantId) {
                return Q(AjaxGetJson("UserAdmin", "ClearModelCache", { participantId: participantId }));
            };

            Api.prototype.getMyModulesAndEvalForms = function() {
                //return Q.fcall(function () { return [{ id: 11, title: 'Module A', children: [{ title: 'MPM', actionXML: 'MPM_EvalForm' }] }, { id: 12, title: 'Module B', children: [] }] });
                return Q(AjaxGetJson("Assessment", "MyModulesAndEvalForms", {}));
            }

            Api.prototype.resetEventConfiguration = function(eventId) {
                return Q(AjaxGetJson("EventAdmin", "ResetEventConfiguration", { eventId: eventId }));
            }

            Api.prototype.submitAction = function(options) {
                return Q(AjaxGetJson("Vote", "SubmitAction", { component: options.component }));
            }

            Api.prototype.getBroadcastingCredentials = function(broadcastId) {
                return Q(AjaxGetJson("WebRTC", "Broadcast/Setup", { id: broadcastId }, null, null, null, "GET"));
            }

            Api.prototype.multipleTry = function(add, subtract, set, contentObject) {
                return Q(AjaxGetJson("Vote", "MultipleTry", { add: add, subtract: subtract, set: set, contentObject: contentObject }));
            }

            Api.prototype.getFollowersComments = function(questionName, meetingCode, latestCount, trackQuestion, isMeeting) {
                return Q(AjaxGetJson('VoteAPI', 'GetFollowersVote', { questionShortName: questionName, meetingCode: meetingCode, latestCount: latestCount, trackQuestion: trackQuestion, isMeeting: isMeeting }));
            }

            Api.prototype.changeAction = function(meetingCode) {
                return $.ajax({
                    url: 'Wizer/Meeting/ChangeAction?meetingCode=' + meetingCode,
                    contentType: 'application/json',
                    type: 'GET',
                    datatype: 'json',
                    success: function(response) {
                        return response;
                    },
                    error: function(response) {
                        return response;
                    }
                });
            }

            Api.prototype.uploadFiles = function(data) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: UploadFiles, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: '/Wizer/Vote/UploadFiles',
                        data: data,
                        cache: false,
                        contentType: false,
                        processData: false,
                        type: 'POST',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.getMeetings = function(actionName, isLeader) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: GetMeetings, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/GetAll?actionName=' + actionName + '&isLeader=' + isLeader,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.getParticipationInfo = function(meetingCode) {
                return $.ajax({
                    url: 'Wizer/Meeting/ParticipantInfo?meetingCode=' + meetingCode,
                    contentType: 'application/json',
                    type: 'GET',
                    datatype: 'json',
                    success: function(response) {
                        return response;
                    },
                    error: function(response) {
                        return response;
                    }
                });
            }

            Api.prototype.openMeeting = function(foremanQuestion, title, description, planDate) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: openMeeting, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/Open',
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: JSON.stringify({ foremanQuestion: foremanQuestion, title: title, description: description, planDate: planDate }),
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.joinMeeting = function(foremanQuestion, code) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: joinMeeting, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/Join',
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: JSON.stringify({ foremanQuestion: foremanQuestion, code: code }),
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.changeMeetingState = function(status, actionId, meetingId) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: changeMeetingState, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/ChangeState',
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: JSON.stringify({ meetingId: meetingId, status: status, actionId: actionId }),
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.getMeetingCounts = function(eventId, meetingCode, actionQuestionName) {
                var self = this;
                actionQuestionName = actionQuestionName == undefined ? '' : actionQuestionName;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getMeetingCounts, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/GetCounts?eventId=' + eventId + '&meetingCode=' + meetingCode + '&actionQuestionName=' + actionQuestionName,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }

            }

            Api.prototype.getMeetingProgressReport = function(eventId, questionsArray, meetingCode, ignoreTryQuestionsArray, actionQuestion) {
                var self = this;

                ignoreTryQuestionsArray = ignoreTryQuestionsArray == undefined ? "" : ignoreTryQuestionsArray
                actionQuestion = actionQuestion == undefined ? '' : actionQuestion;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getMeetingProgressReport, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/GetMeetingProgressReport?eventId=' + eventId + '&question=' + questionsArray + '&meetingCode=' + meetingCode + "&ignoreTryQuestions=" + ignoreTryQuestionsArray + "&actionQuestion=" + actionQuestion,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.getCascadeMeetingResults = function(eventId, questionArray, filterArray, meetingCode) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getCascadeMeetingResults, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/GetCascadeMeetingResults?eventId=' + eventId + '&questions=' + questionArray + '&filters=' + JSON.stringify(filterArray) + '&meetingCode=' + meetingCode,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.getPendingParticipantList = function(meetingCode) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getPendingParticipantList, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/GetPendingParticipantList?meetingCode=' + meetingCode,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }


            Api.prototype.getSubmissionStatusTime = function(eventId, questionArray, filterArray, meetingCode) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getSubmissionStatusTime, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/GetSubmissionStatusTime?eventId=' + eventId + '&questions=' + questionArray + '&filters=' + JSON.stringify(filterArray) + '&meetingCode=' + meetingCode,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.getAllMeetingList = function(eventId) {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: GetAllMeetingList, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: 'Wizer/Meeting/GetAllMeetingList?eventId=' + eventId,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }



            Api.prototype.getAttachments = function(participantId) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getAttachments, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Vote", "GetAttachments", { participantId: participantId }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.removeAttachment = function(participantId, attachmentId, fileName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: removeAttachment, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Vote", "RemoveAttachment", { participantId: participantId, attachmentId: attachmentId, fileName: fileName }, null, null, null, "POST"));
                    return promise;
                }
            }



            Api.prototype.getMyTrackVote = function(trackQuestion) {
                var self = this;
                var fetching = Q.defer();
                var trackQuestionId = self.getQuestionIdByName(trackQuestion);
                var gettingMyVoteOnTrack = self.getMyVotes([trackQuestionId]);

                gettingMyVoteOnTrack.then(function(votes) {
                    var myVoteonTrack = votes.votes[trackQuestionId];
                    if (myVoteonTrack) {
                        fetching.resolve(myVoteonTrack);
                    } else {
                        fetching.resolve(null);
                        Wizer.Api.showError("No Votes of this user on trackQuestion: " + trackQuestion);
                    }
                });

                return fetching.promise;
            }

            Api.prototype.getVotesByQuestionName = function(questions, trackQuestion, sorting, maxLength, isDelivery) {
                return this.wizerModel.getVotesByQuestionName(questions, trackQuestion, sorting, maxLength, isDelivery);
            }

            Api.prototype.getMyVotesByQuestionName = function(questions) {
                return this.wizerModel.getMyVotesByQuestionName(questions);
            }

            Api.prototype.logout = function() {
                document.location.href = "/Wizer/Wizer/Logout";
            }

            Api.prototype.setCookie = function(cookieName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: setCookie, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Vote", "SetCookie", { name: cookieName }, null, null, null, "POST"));
                    return promise;
                }
            }

            Api.prototype.addEventTracking = function(verbId, definitionType, objectype, platform, score, contextId, elementId, questionName, attemptCount, attemptType, attemptDuration, attemptEfficiency, moduleName, route, extraTrackingParams, objectId) {
                if (extraTrackingParams) {
                    extraTrackingParams = JSON.stringify(extraTrackingParams);
                }
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: addEventTracking, getOffline not implemented")
                } else {

                    return Q(AjaxGetJson("Xapi", "SendActionStatements", {
                        verbId: verbId,
                        definitionType: definitionType,
                        objectype: objectype,
                        platform: platform,
                        userLocation: moment.tz.guess(),
                        codeName: navigator.appCodeName,
                        name: navigator.appName,
                        version: navigator.appVersion,
                        browserPlatform: navigator.platform,
                        userAgentHeader: navigator.userAgent,
                        cookiesEnabled: navigator.cookieEnabled,
                        score: score,
                        contextId: contextId,
                        elementId: elementId,
                        questionName: questionName,
                        attemptCount: attemptCount,
                        attemptType: attemptType,
                        attemptDuration: attemptDuration,
                        attemptEfficiency: attemptEfficiency,
                        moduleName: moduleName,
                        route: route,
                        extraTrackingParams: extraTrackingParams,
                        objectId: objectId
                    }, null, null, null, "POST"));
                }


            }

            Api.prototype.getLastVisitedObjectDetails = function() {
                var self = this;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getLastVisitedObjectDetails, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: 'Wizer/Gateway/RecentHistory',
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: {},
                        success: function(response) {
                            return response.GetcontentObjectDetails;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }

            }

            Api.prototype.saveResponseObjectComment = function(responseId, body, status, participationId, trackQuestion, isMeeting, maxCount) {
                var self = this;
                var url = 'Wizer/Comment/SaveResponseObjectComment';

                if (isMeeting != undefined && isMeeting != null && isMeeting != 'undefined')
                    url += '?isMeeting=' + isMeeting;

                if (maxCount != undefined && maxCount != null && maxCount != 'undefined')
                    url += '&maxCount=' + maxCount;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: saveResponseObjectComment, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: url,
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: JSON.stringify({ responseId: responseId, body: body, status: status, participationId: participationId, trackQuestion: trackQuestion }),
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.getResponseObjectComments = function(eventId, filterArray, meetingCode, questions, isMeeting, maxLikeCount, maxMarkImpCount, sortBy, sortOrder) {
                var self = this;
                var url = 'Wizer/Comment/GetResponseObjectComments?eventId=' + eventId + '&filters=' + JSON.stringify(filterArray) + '&meetingCode=' + meetingCode;

                if (questions != undefined && questions != null && questions != 'undefined')
                    url += '&questions=' + questions;

                if (isMeeting != undefined && isMeeting != null && isMeeting != 'undefined')
                    url += '&isMeeting=' + isMeeting;

                if (maxLikeCount != undefined && maxLikeCount != null && maxLikeCount != 'undefined')
                    url += '&maxLikeCount=' + maxLikeCount;

                if (maxMarkImpCount != undefined && maxMarkImpCount != null && maxMarkImpCount != 'undefined')
                    url += '&maxMarkImpCount=' + maxMarkImpCount;

                if (sortBy != undefined && sortBy != null && sortBy != 'undefined')
                    url += '&sortBy=' + sortBy;

                if (sortOrder != undefined && sortOrder != null && sortOrder != 'undefined')
                    url += '&sortOrder=' + sortOrder;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: GetResponseObjectComments, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: url,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.setCacheValue = function(keyname, cachedata) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: setCacheValue, getOffline not implemented")
                } else {
                    var ajaxVotes = Q(AjaxGetJson("CachingAPI", "SetValue", { key: keyname, value: cachedata }));
                    var filtered = ajaxVotes.then(function(resp) {
                        return resp;
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.getCacheValue = function(keyname) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getCacheValue, getOffline not implemented")
                } else {
                    var ajaxVotes = Q(AjaxGetJson("CachingAPI", "GetValue", { key: keyname }));
                    var filtered = ajaxVotes.then(function(resp) {
                        return resp;
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.clearCacheKey = function(keyname) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: clearCacheKey, getOffline not implemented")
                } else {
                    var ajaxVotes = Q(AjaxGetJson("CachingAPI", "ClearCacheKey", { p: null, key: keyname }));
                    var filtered = ajaxVotes.then(function(resp) {
                        return resp;
                    });
                    ajaxVotes.fail(Wizer.Api.showError);
                    return filtered;
                }
            }

            Api.prototype.saveQuestion = function(questionName) {
                return Q(AjaxGetJson('VoteAPI', 'CreateQuestion', { name: questionName, questionText: questionName, isProtected: false, isImportant: false, isMultiVote: false, responseOptions: {} }));
            }

            Api.prototype.renewVoteOffsetNotifications = function() {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: renewVoteOffsetNotifications, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: '/Wizer/Notification/UpdateVoteOffsetNotifications',
                        cache: false,
                        contentType: false,
                        processData: false,
                        type: 'POST',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }

            Api.prototype.analyseWebrtcVideo = function(archiveId, questionName, progressMsg) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: analyseWebrtcVideo, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: '/Wizer/WebRTC/videoCallanalytics',
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: JSON.stringify({ archiveId: archiveId, questionName: questionName, progressMsg: progressMsg }),
                        success: function(response) {
                            return response
                        },
                        error: function(response) {
                            return response
                        }
                    });
                }
            }

            Api.prototype.getIdeas = function(trackQuestion, value, getAll, isMeeting) {
                var self = this;

                if (self.isOffline()) {
                    logger.log(true, "wizer-api: getIdeas, getOffline not implemented")
                } else {
                    var promise;
                    if (getAll != undefined && getAll != null && getAll != 'undefined') {
                        promise = Q(AjaxGetJson("IdeaRating", "GetIdeasByTrackQuestion", { trackQuestion: trackQuestion, trackQuestionValue: value, getAllValues: getAll, isMeeting: isMeeting }, null, null, null, "GET"));
                    } else {
                        promise = Q(AjaxGetJson("IdeaRating", "GetIdeasByTrackQuestion", { trackQuestion: trackQuestion, trackQuestionValue: value, isMeeting: isMeeting }, null, null, null, "GET"));
                    }
                    return promise;
                }
            }

            Api.prototype.getIdeaRatings = function(trackQuestion, value, isMeeting) {
                var self = this;
                if (self.isOffline()) {
                    logger.log(true, "wizer-api: getIdeaRatings, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("IdeaRating", "GetIdeaRatings", { trackQuestion: trackQuestion, trackQuestionValue: value, isMeeting: isMeeting }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.getIdeaRatingsBySliders = function(trackQuestion, value, sliderArray, isMeeting) {
                var self = this;
                if (self.isOffline()) {
                    logger.log(true, "wizer-api: GetIdeaRatingsBySliders, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("IdeaRating", "GetIdeaRatingsBySliders", { trackQuestion: trackQuestion, trackQuestionValue: value, sliderArray: sliderArray, isMeeting: isMeeting }, null, null, null, "GET"));
                    return promise;
                }
            }


            Api.prototype.getIdeaRatingsByParticipant = function(participantId) {
                var self = this;
                if (self.isOffline()) {
                    logger.log(true, "wizer-api: getIdeaRatings, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("IdeaRating", "GetIdeaRatingsByParticipant", { participantId: participantId }, null, null, null, "GET"));
                    return promise;
                }
            }



            Api.prototype.saveIdeaRating = function(ideaRatingsArray) {
                var self = this;
                if (self.isOffline()) {
                    logger.log(true, "wizer-api: saveIdeaRating, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: 'Wizer/IdeaRating/IdeaRating',
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: JSON.stringify({ ideaRatingJson: JSON.stringify(ideaRatingsArray) }),
                        success: function(response) {
                            return response
                        },
                        error: function(response) {
                            return response
                        }
                    });
                }
            }

            Api.prototype.deletevote = function(responseid) {

                var self = this;
                if (self.isOffline()) {
                    logger.log(true, "wizer-api: deletevote, getOffline not implemented")
                } else {
                    //AjaxGetText('Vote', 'RemoveVote', 'responseId=' + responseId, function () {
                    //    OnSaved();
                    //});
                    var promise = Q(AjaxGetJson("Vote", "RemoveVote", { responseId: responseid }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.getJWTToken = function() {
                var url = Wizer.PulseServiceUrl + '/Authenticate.svc/GenerateJWT';
                return new Promise(function(resolve, reject) {
                    $.ajax({
                        type: 'POST',
                        url: url,
                        success: function(x) {
                            if (x && x.GenerateJWTResult) {
                                resolve(x.GenerateJWTResult.successMessage)
                            } else {
                                resolve(false)
                            }
                        }
                    })
                })
            }

            Api.prototype.saveIdea = function(ideaText, participantId, additionalInfo, trackQuestion, isMeeting, questionArray) {
                var self = this;
                if (self.isOffline()) {

                } else {
                    return $.ajax({
                        url: 'Wizer/IdeaRating/Idea',
                        contentType: 'application/JSON',
                        type: 'POST',
                        datatype: 'JSON',
                        data: JSON.stringify({ ideaText: ideaText, participantId: participantId, additionalInfo: JSON.stringify(additionalInfo), trackQuestion: trackQuestion, isMeeting: isMeeting, questionArray: questionArray }),
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    })
                }
            }

            Api.prototype.getIdeaRatingVotes = function(questionArray) {
                var self = this;
                if (self.isOffline()) {
                    logger.log(true, "wizer-api: getIdeaRatings, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("IdeaRating", "GetVotes", { questionArray: questionArray }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.getQuestionObject = function(questionName) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getQuestionObject, getOffline not implemented")
                } else {
                    return $.ajax({
                        url: '/Wizer/Vote/GetQuestionByName',
                        contentType: 'application/json',
                        type: 'POST',
                        datatype: 'json',
                        data: JSON.stringify({ questionName: questionName }),
                        success: function(response) {
                            return response
                        },
                        error: function(response) {
                            return response
                        }
                    });
                }
            }

            Api.prototype.getIdeaObjectComments = function(status, trackQuestion, isMeeting, maxLikeCount) {
                var self = this;

                if (self.isOffline()) {
                    logger.log(true, "wizer-api: getIdeaObjectComments, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Comment", "getIdeaObjectComments", { status: status, trackQuestion: trackQuestion, isMeeting: isMeeting, maxLikeCount: maxLikeCount }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.removeSelectedIdeas = function(participantId) {
                var self = this;
                if (self.isOffline()) {
                    logger.log(true, "wizer-api: DeleteSelectedIdeas, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Comment", "DeleteSelectedIdeas", { participantId: participantId }, null, null, null, "GET"));
                    return promise;
                }
            }

            Api.prototype.saveIdeaObjectComments = function(ideaIds, status, participationId, isMeeting, maxCount) {
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: saveIdeaObjectComments, getOffline not implemented")
                } else {
                    var promise = Q(AjaxGetJson("Comment", "SaveIdeaObjectComments", { ideaIds: ideaIds, status: status, participationId: participationId, isMeeting: isMeeting, maxCount: maxCount }, null, null, null, "POST"));
                    return promise;
                }
            }


            Api.prototype.RunAggregator = function(actionName, isSessionNotSave, participant) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: RunAggregator, getOffline not implemented")
                } else {
                    participant = participant != undefined && participant != null && participant != 'undefined' ? participant : null;
                    isSessionNotSave = isSessionNotSave != undefined && isSessionNotSave != null && isSessionNotSave != 'undefined' ? isSessionNotSave : false;
                    var promise = Q(AjaxGetJson("Vote", "RunAggregator", { scriptName: actionName, isSessionNotSave: isSessionNotSave, participantId: participant }));
                    var filtered = promise.then(function(resp) {
                        return resp;
                    });
                    promise.fail(Wizer.Api.showError);
                    return filtered;
                }
            }


            Api.prototype.resetAction = function() {
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: resetAction, getOffline not implemented")
                } else {
                    return Q(AjaxGetJson("Vote", "ResetActionByTryNumber", null, null, null, null, "GET"));
                }
            }

            Api.prototype.saveActionPlan = function(actionPlanForm, trackQuestion, isMeeting) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: saveActionPlan, getOffline not implemented")
                } else {
                    var defer = Q.defer();;
                    $.ajax({
                        url: '/Wizer/Vote/SaveActionPlan',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ actionPlanForm: actionPlanForm, trackQuestion: trackQuestion, isMeeting: isMeeting }),
                        datatype: 'json',
                        success: function(response) {
                            if (response.success) {
                                defer.resolve(response);
                            } else {
                                defer.reject(response);
                            }
                        },
                        error: function(response) {
                            defer.reject(response);
                        }
                    });
                    return defer.promise;
                }
            }


            Api.prototype.deleteActionPlan = function(actionPlan) {
                var self = this;
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: deleteActionPlan, getOffline not implemented")
                } else {
                    var defer = Q.defer();;

                    $.ajax({
                        url: '/Wizer/Vote/DeleteActionPlan',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ actionPlan: actionPlan }),
                        datatype: 'json',
                        success: function(response) {
                            if (response.success) {
                                defer.resolve(response);
                            } else {
                                defer.reject(response);
                            }
                        },
                        error: function(response) {
                            defer.reject(response);
                        }
                    });
                    return defer.promise;
                }
            }

            Api.prototype.getActionPlan = function(formQuestionId, participantId, meetingCode, trackQuestion, isMeeting, getALL, filterArray, onlyCurrentParticipantData) {

                filterArray = filterArray == undefined ? [] : filterArray;
                onlyCurrentParticipantData == undefined ? false : onlyCurrentParticipantData;

                if (this.isOffline()) {
                    logger.log(true, "wizer-api: getActionPlan, getOffline not implemented");
                } else {
                    return $.ajax({
                        url: '/Wizer/Vote/GetActionPlan?formQuestionId=' + formQuestionId + ' &participantId=' + participantId + '&meetingCode=' + meetingCode + '&trackQuestion= ' + trackQuestion + '&isMeeting=' + isMeeting + '&getALL=' + getALL + '&filters=' + JSON.stringify(filterArray) + '&onlyCurrentParticipantData=' + onlyCurrentParticipantData,
                        contentType: 'application/json',
                        type: 'GET',
                        datatype: 'json',
                        success: function(response) {
                            return response;
                        },
                        error: function(response) {
                            return response;
                        }
                    });
                }
            }


            Api.prototype.updateConsent = function() {
                if (this.isOffline()) {
                    logger.log(true, "wizer-api: updateConsent, getOffline not implemented")
                } else {
                    return Q(AjaxGetJson("Authentication", "UserAgreement", null, null, null, null, "GET"));
                }
            }

            return Api;

        })();

        return Wizer.Api;

    });