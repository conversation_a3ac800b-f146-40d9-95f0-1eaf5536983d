﻿<div {{?it.id}}id="{{=it.id}}"{{?}} class="container {{?it.valign}}valign-container{{?}} {{?it.animate}}animate__animated animate__{{=it.animate}}{{?}}"> 
    <div class="row {{?it.valign}}valign-wrapper{{?}}">
        <div>
        {{?it.header}}<h4 class="header {{?it.valign}}center{{?}} title">{{=it.header}}</h4>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        <div class="card hoverable {{?it.size}}{{=it.size}}{{?}} {{?it.orientation}}{{=it.orientation}}{{?}}">
            
            {{?it.content.img || it.content.video}}
            <div class="card-image hide-on-small-and-down">
                {{?it.content.img}}
                <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img.src}}" alt="{{=it.content.img.alt}}"/>
                <span class="card-title">{{=it.content.title}}</span>
                {{?}}
                {{?it.content.video}}
                <video autoplay loop muted
                    {{?it.content.video.poster}}poster="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.video.poster}}"{{?}}>
                    <source src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.video.src}}" type="video/mp4">
                </video>
                {{?}}
            </div>
            {{?}}

            <div class="card-content">
                
                {{?it.content.title}}<span class="card-title">{{=it.content.title}}</span>{{?}}
                <p class="flow-text">{{=it.content.body}}</p><br>

                <div class="row">
                    <form class="col s12" novalidate autocomplete="off">
                        
                        {{~it.inputs :input :idx}}
                        <div class="input-field col {{=input.grid}}" {{?input.hidden}}hidden{{?}}>

                            {{?input.icon}}<i class="material-icons prefix">{{=input.icon}}</i>{{?}}
                                
                            {{?input.type==="select"}}
                                <select single name="{{=input.name}}" data-bind="{{=input.bind}}" {{?input.bindVal}}data-bindval="{{=input.bindVal}}{{?}}"
                                        {{?input.linkedSelect}}data-linkedselect="{{=input.linkedSelect}}"{{?}} 
                                        {{?input.required}}required{{?}} 
                                        {{?it.DB[it.isFollower]>0}}disabled{{?}}
                                        {{?input.disabled}}disabled{{??}}class="validate {{?input.icons}}with-icons{{?}}"{{?}} 
                                        >
                                    <option value="" disabled selected>{{=input.title}}</option>
                                    {{?input.options}}
                                    {{~input.options :option :opt}}
                                    <option value="{{=option.val}}" 
                                        {{?input.icons}}data-icon="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=input.icons}}{{=option.val}}"{{?}}>
                                        {{=option.name}}
                                    </option>
                                    {{~}}
                                    {{?}}
                                </select>

                            {{??}}

                                <input name="{{=input.name}}" type="{{=input.type}}"
                                        {{?input.type==="email" && input.loadMyMail}}value="{{=Wizer.ParticipantEmail}}"{{?}}
                                        data-bind="{{=input.bind}}" data-length="{{=input.length}}" maxlength="{{=input.length}}"
                                        {{?input.required}}required{{?}} {{?input.hideRemaining}}no-remaining{{??}}remaining{{?}}
                                        {{?it.DB[it.isFollower]>0}}disabled{{?}}
                                        {{?input.disabled}}disabled{{??}}class="validate"{{?}} />
                            {{?}}
                            
                            <label {{?input.type==="email" && input.loadMyMail}}class="active"{{?}} for="{{=input.name}}">
                                    {{?input.required}}* {{?}}{{=input.label}}
                            </label>
                            {{?input.helper}}
                            <span class="helper-text" data-error="{{=it.inputWrong}}" data-success="{{=it.inputRight}}"></span>
                            {{?}}


                        </div>
                        {{~}}

                    </form>
                </div>
            

            </div>           
        </div>

        
        <div class="row submit">
            {{?it.closeBtn}}
            <a id="closeBtn" class="btn left client-colors dark-grey"
                {{?it.DB[it.isFollower]>0}}disabled{{?}}
                {{?it.submitBtn.targetSection}}data-action="{{=it.submitBtn.targetSection}}"{{?}}>
                <i class="medium material-icons right">close</i>{{=it.closeBtn.label}}
            </a>
            {{?}}
            {{?it.clearBtn}}
            <a id="clearBtn" class="btn client-colors red"
                {{?it.DB[it.isFollower]>0}}disabled{{?}}>
                <i class="medium material-icons right">clear</i>{{=it.clearBtn.label}}
            </a>
            {{?}}
            {{?it.submitBtn}}
            <a id="submitBtn" class="btn pulse client-colors button"
                {{?it.submitBtn.disabled || it.DB[it.isFollower]>0}}disabled{{?}}
                {{?it.submitBtn.targetSection}}data-action="{{=it.submitBtn.targetSection}}"{{?}}>
                <i class="medium material-icons right">save</i>{{=it.submitBtn.label}}
            </a>
            {{?}}
        </div>


    </div>
    </div>
</div>
