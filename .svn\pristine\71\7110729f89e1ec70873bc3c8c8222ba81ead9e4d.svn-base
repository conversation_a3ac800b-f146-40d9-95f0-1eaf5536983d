'use strict';

define(['jquery', 'Q'], function($, Q) {

    var currentScript;

    $(document).bind('wizer:action:init', function(e, currentAction) {

        currentScript = currentAction.scriptName;

        if (currentScript) {

            console.log('Current scene: ' + currentScript);

            // This hides the address bar:
            window.scrollTo(0, 1);

            // Hide any sideBar overlay (when screen changes while side menu is open)
            $('.sidenav-overlay').hide();

            
            // Hide any opened modal (when screen changes while a modal is open)
            $('.modal-overlay').hide();
            $('.modal').hide();

            
            //UNBLOCK blocked gauges when new screen
            $('span.gauge-blocked').removeClass('gauge-blocked').trigger('updateModelSpan')

            

            // **************************************** //
            // RESIZE & HEIGHT UPDATE IN IBIDER EMBED SIM
            // **************************************** //
            //var maincontainer = $(".mainAreaContainer")[0];   

            // Get the current <html> height and send it to the parent window
            // function sendHeight(setToTop){
            //     var contentHeight = maincontainer.offsetHeight;
            //     var height = document.querySelector('html').offsetHeight;
            //     window.parent.postMessage({
            //         pulseHeight: height,
            //         contentHeight: contentHeight,
            //         setToTop: setToTop
            //     },'*');
            // }            
            // Send height when the DOM is loaded
            // sendHeight(true);
            
            // Send height whenever the size of the window changes
            // let resizeObserver = new ResizeObserver(function() {
            //     setTimeout(function() { sendHeight(false); });
            // });         
            // resizeObserver.observe(maincontainer); 
        }

          

    });

});
