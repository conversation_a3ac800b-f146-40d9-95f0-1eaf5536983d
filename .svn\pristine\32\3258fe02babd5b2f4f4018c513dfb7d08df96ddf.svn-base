@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";


.page-footer {
    clear: both;
    min-height: 5vh;
    padding-top: 0px;
    padding-bottom: 2px;
    border-top: 1px solid color("client-colors", "white");
    
    .footer-banner {
        width: 100vw;
        height: 5px;
        background-size: contain;
    }

    .footer-title {
        margin: 5px;
    }

    #logo-container img {
        width: 100px;
        height: auto;
    }

    .footer-copyright {
        min-height: 15px;
        padding: 2px 0px;
        font-size: 75%;
        font-family: clientLight, clientRegular, sans-serif, Arial;
        text-align: center;
        background-color: transparent;
    }

    
    &.ibiderEmbed {

        .footer-title {
            // font-size: 16px !important;
            .title, .break {
                display: none !important;
            }

        }

    }
}
