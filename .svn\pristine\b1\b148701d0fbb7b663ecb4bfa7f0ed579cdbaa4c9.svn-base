<?xml version="1.0" encoding="utf-8" ?>
<Action>


  <Component type="Password" customJS="true">
    <![CDATA[{
      templateInEvent: "html/password.dot",
      css: "styles/password.css",
      size: "",
      valign: false,
      header: "!{Navigation_SIM_Start_Header}",
      subheader: "!{Navigation_SIM_Start_Text}",
      instructions: "!{}",
      title: "!{Navigation_SIM_Title}",
      password: 
        {
          subtitle: "!{}",
          name: "password",
          label: "!{Navigation_SIM_Password}",
          length: 3,
          code: "!{Navigation_SIM_Code}",
          right: "!{Navigation_SIM_PasswordRight}",
          wrong: "!{Navigation_SIM_PasswordWrong}"
        },
      submitBtn: {
        hidden: true,
        label: "!{Navigation_SIM_Start_Btn}",
        toast: "!{}",
        action: "",
        jump: "",
        load: "SIM_R3_Start"
      },
      scope: [""]
    }]]>
  </Component> 


</Action>