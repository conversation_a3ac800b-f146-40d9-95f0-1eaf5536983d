<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro2">
  <Include name="Header_Intro"></Include>
  

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    position: "",
    header: "!{TeamName_Table_InfoTitle}",
    userHeader: "!{TeamName_Table_User}",
    me: "",
    boundName: "",
    avatar: " ",
    defaultAvatar: "!{defaultAvatar}",
    boundAvatar: "Q_My_Avatar",
    isDataTables: false,
    class: "_verticalMode_ _myresponsive-table_ noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{TeamName_Table_Name}",
        binding: "Q_My_Name"
      }
    ],
    trackQuestion: "Show_charts",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:1000,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>




</Action>