@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

.wizlet.wizletPassword {

    .card {

        .card-content {
            .card-title { 
                font-family: clientBold, Arial;
                border-bottom: 3px solid color("client-colors", "secondary");
            }
            // p {
            //     text-align: justify;
            // }
            
            .row {
                margin: 0;
            }
            
            form {

                padding:0;

                textarea {
                    font-size: 20px;
                    height: 50px;

                    ~label[for] {
                        transform: translateY(0px);
                        &.active {
                            transform: translateY(-10px);
                        }
                    }
                }

                .subtitle {
                    margin-top: 2rem;
                    color: color("client-colors","secondary");
                    font-family: clientBold, Arial;
                }
            }

        }

    }

    @include row-submit;
    
}