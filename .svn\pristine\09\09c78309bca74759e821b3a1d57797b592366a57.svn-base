<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayout_SIM_R1">
  <Include name="Header_SIM_R1"></Include>
  <Include name="KPIgauges_R1"></Include>


  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "bounceInLeft",
      progress: "1",
      steps: [ "!{SIM_BreadCrumbs_0}",
               "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}" ]
    }]]>
  </Component>




  <Component type="CheckBoxes" customJS="true">
    <![CDATA[{
      templateInEvent: "html/checkBoxes.dot",
      css: "styles/checkBoxes.css",
      id: "", 
      isHidden: false,
      animate: "fadeInRight",
      header: "!{SIM_Initiatives_Header}",
      instructions: "!{SIM_Initiatives_Instructions}",
      title: "!{SIM_Initiatives_Title}",
      titleLabel: "!{}",
      subtitle: "!{}",
      class: "no-separator reduced",
      inputclass: "filled-in",
      minChoices: 2,
      maxChoices: 2,
      maxText: "!{Checkboxes_allOptions}",
      moreInfo: "!{Choice_MoreInfo}",
      isCheck: false,
      noBlockAnswer: false,
      preloadSaved: true,
      saved: "Q_SIM_R1_Initiatives",
      savedWithLabels: false,
      savedWithCounter: true,
      options: [
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_1",
          bind: "Q_SIM_R1_Initiatives_1",
          label: "!{Badge_Opt1}",
          title: "!{SIM_Initiatives_Opt1}",
          moreInfo: "!{SIM_Initiatives_Des1}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_2",
          bind: "Q_SIM_R1_Initiatives_2",
          label: "!{Badge_Opt2}",
          title: "!{SIM_Initiatives_Opt2}",
          moreInfo: "!{SIM_Initiatives_Des2}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_3",
          bind: "Q_SIM_R1_Initiatives_3",
          label: "!{Badge_Opt3}",
          title: "!{SIM_Initiatives_Opt3}",
          moreInfo: "!{SIM_Initiatives_Des3}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_4",
          bind: "Q_SIM_R1_Initiatives_4",
          label: "!{Badge_Opt4}",
          title: "!{SIM_Initiatives_Opt4}",
          moreInfo: "!{SIM_Initiatives_Des4}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_5",
          bind: "Q_SIM_R1_Initiatives_5",
          label: "!{Badge_Opt5}",
          title: "!{SIM_Initiatives_Opt5}",
          moreInfo: "!{SIM_Initiatives_Des5}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_6",
          bind: "Q_SIM_R1_Initiatives_6",
          label: "!{Badge_Opt6}",
          title: "!{SIM_Initiatives_Opt6}",
          moreInfo: "!{SIM_Initiatives_Des6}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_7",
          bind: "Q_SIM_R1_Initiatives_7",
          label: "!{Badge_Opt7}",
          title: "!{SIM_Initiatives_Opt7}",
          moreInfo: "!{SIM_Initiatives_Des7}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_8",
          bind: "Q_SIM_R1_Initiatives_8",
          label: "!{Badge_Opt8}",
          title: "!{SIM_Initiatives_Opt8}",
          moreInfo: "!{SIM_Initiatives_Des8}",
          isCorrect: false
        },
      
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_10",
          bind: "Q_SIM_R1_Initiatives_10",
          label: "!{Badge_Opt10}",
          title: "!{SIM_Initiatives_Opt10}",
          moreInfo: "!{SIM_Initiatives_Des10}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_11",
          bind: "Q_SIM_R1_Initiatives_11",
          label: "!{Badge_Opt11}",
          title: "!{SIM_Initiatives_Opt11}",
          moreInfo: "!{SIM_Initiatives_Des11}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_12",
          bind: "Q_SIM_R1_Initiatives_12",
          label: "!{Badge_Opt12}",
          title: "!{SIM_Initiatives_Opt12}",
          moreInfo: "!{SIM_Initiatives_Des12}",
          isCorrect: false
        },
        {
          header: "!{}",
          preload: "Q_SIM_Initiatives_13",
          bind: "Q_SIM_R1_Initiatives_13",
          label: "!{Badge_Opt13}",
          title: "!{SIM_Initiatives_Opt13}",
          moreInfo: "!{SIM_Initiatives_Des13}",
          isCorrect: false
        }
      ],

      checkBtn: {
        label: "!{Choice_btnCheck}",
        toast: "!{InputSubmited}",
        animate: "slideInUp",
        idToClick: "navButton",
        idToShowIfCorrect: "",
        idToShowIfSomecorrect: "",
        idToShowIfIncorrect: "",
        scrollToTop: false,
        scrollToDown: false
      },
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower",
              "Q_SIM_Initiatives_1",
              "Q_SIM_Initiatives_2",
              "Q_SIM_Initiatives_3",
              "Q_SIM_Initiatives_4",
              "Q_SIM_Initiatives_5",
              "Q_SIM_Initiatives_6",
              "Q_SIM_Initiatives_7",
              "Q_SIM_Initiatives_8",
              "Q_SIM_Initiatives_10",
              "Q_SIM_Initiatives_11",
              "Q_SIM_Initiatives_12",
              "Q_SIM_Initiatives_13"]  
      
    }]]>
  </Component>



  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          _gdActionEmbed: "SIM_R1_Scenario1_AGG",
          _gdActionTrack: "GD",
          _gdActionSection: "",
          _targetSection: "",
          label: "!{Navigation_feedback}",
          tooltip: "!{Navigation_feedback_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R1_FAC_dashboard_tab2}"/>
  </Voting>





</Action>