<?xml version="1.0" encoding="utf-8" ?>
<Action layout="../../../layout/tabsLayoutModalNav">
  

  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{}",
      swipeable: false,
      tabPanelID: "tabPanelModalNav",
      tabs: [
          "!{GroupDirector_Tab1}",
          "!{GroupDirector_Tab3}",
          "!{GroupDirector_Tab5}"
      ],
      _activeTab: 1,
      activeTab: "Q_FAC_Navigation_Tab",
      activeTabByVal: false,
      scope: ["Q_FAC_Navigation_Tab"]
  }]]></Component>


  <!-- SIM 1 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{}",
      bgColor: "secondary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R1_LandingPage",
          targetSection: "R1_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R1_Welcome",
          targetSection: "R1_Welcome_FAC",
          label: "!{R1_Welcome}",
          icon: "info"
        },
        {
          gdActionEmbed: "SIM_R1_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R1_Start",
          targetSection: "SIM_R1_FAC_dashboard",
          label: "!{GD_SIM_R1_Landing} (SELFPACED MODE)",
          icon: "screen_share"
        },
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R1_Finish_BackDirector2",
          gdActionEmbed: "SIM_R1_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R1_DebriefPage",
          targetSection: "R1_DebriefPage_FAC",
          label: "!{GD_SIM_R1_Finish} (FACILITATED MODE)",
          icon: "cancel"
        },
        {
          gdActionEmbed: "SIM_R1_FULL_AGG",
          gdActionTrack: "GD",
          targetSection: "FAC_Navigation",
          label: "R1 !{GD_Agg}",
          icon: "speed"
        }
      ],
      scope: [  ]
  }]]></Component>


  <!-- SIM 2 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{}",
      bgColor: "secondary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R2_LandingPage",
          targetSection: "R2_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R2_Welcome",
          targetSection: "R2_Welcome_FAC",
          label: "!{R2_Welcome}",
          icon: "info"
        },
        {
          gdActionEmbed: "SIM_R2_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Start",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{GD_SIM_R2_Landing} (SELFPACED MODE)",
          icon: "screen_share"
        },
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R2_Finish_BackDirector2",
          gdActionEmbed: "SIM_R2_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R2_DebriefPage",
          targetSection: "R2_DebriefPage_FAC",
          label: "!{GD_SIM_R2_Finish} (FACILITATED MODE)",
          icon: "cancel"
        },
        {
          gdActionEmbed: "SIM_R2_FULL_AGG",
          gdActionTrack: "GD",
          targetSection: "FAC_Navigation",
          label: "R2 !{GD_Agg}",
          icon: "speed"
        }
      ],
      scope: [  ]
  }]]></Component>


  <!-- SIM 2 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{}",
      bgColor: "secondary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R3_LandingPage",
          targetSection: "R3_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R3_Welcome",
          targetSection: "R3_Welcome_FAC",
          label: "!{R3_Welcome}",
          icon: "info"
        },
        {
          gdActionEmbed: "SIM_R3_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R3_Start",
          targetSection: "SIM_R3_FAC_dashboard",
          label: "!{GD_SIM_R3_Landing} (SELFPACED MODE)",
          icon: "screen_share"
        },
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R3_Finish_BackDirector3",
          gdActionEmbed: "SIM_R3_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R3_DebriefPage",
          targetSection: "R3_DebriefPage_FAC",
          label: "!{GD_SIM_R3_Finish} (FACILITATED MODE)",
          icon: "cancel"
        },
        {
          gdActionEmbed: "SIM_R3_FULL_AGG",
          gdActionTrack: "GD",
          targetSection: "FAC_Navigation",
          label: "R3 !{GD_Agg}",
          icon: "speed"
        }
      ],
      scope: [  ]
  }]]></Component>





</Action>
