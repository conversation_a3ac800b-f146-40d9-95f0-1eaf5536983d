﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var Sortable = function () {
        this.type = 'Sortable';
        this.level = 1;
    };

    Sortable.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Sortable.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };

    Sortable.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
                                   
            options.context.find("#sortable").sortable({});
            options.context.find("#sortable").disableSelection();


            options.context.find('#submitBtn').on('click', function (event) {
                
                // var binds = options.context.find('ul#sortable li')
                //                             .map(function (idx,ele) {
                //                                 return $(ele).data('bind');
                //                             })
                //                             .get();
                
                var binds=[], orders=[];
                var items = options.context.find('ul#sortable li');
                $.each (items, function(idx, item) {
                    binds.push ($(item).data('bind'));
                    orders.push ($(item).data('order'));
                });
                
                self.addVotes(binds);
                
                if (options.wizletInfo.submitBtn.hidden) {
                    options.context.find("#submitBtn").prop('hidden',true);

                    items.addClass('disabled');
                }
                    

                if (options.wizletInfo.submitBtn.toast)
                    M.toast({
                        html: options.wizletInfo.submitBtn.toast,
                        classes: 'rounded'
                    });


                var points=0;
                if (options.wizletInfo.rightOrder)
                $.each (options.wizletInfo.rightOrder, function(idx, order) {
                    let isCorrect = order==orders[idx];
                    $(items[idx]).find('i.check-icon').removeAttr('hidden').
                                                        html(isCorrect ? 'check_circle' : 'cancel').
                                                        removeClass('green-text red-text').
                                                        addClass(isCorrect ? 'green-text' : 'red-text');
                                                        
                    if (options.wizletInfo.score && options.wizletInfo.score.points && isCorrect)
                        points += parseInt(options.wizletInfo.score.points);
                });

                
                if (options.wizletInfo.score && options.wizletInfo.score.question) {                    
                    self.addVote(options.wizletInfo.score.question, points);
                }

                if (options.wizletInfo.submitBtn.idToShow) {
                    $(this).removeClass('pulse');
                    $('#'+options.wizletInfo.submitBtn.idToShow).removeAttr('hidden')
                                                                .find('a:not(.follower)').removeAttr('disabled');
                }
                if (options.wizletInfo.submitBtn.idToClick) {
                    $('#'+options.wizletInfo.submitBtn.idToClick+' a').first().click();
                }


            });

            //Set the current side Height as important to avoid resizing when Drag&Drop elements
            if (options.wizletInfo.sideImage) {
                let elem = options.context.find('.side-image');
                let height = elem.outerHeight();
                elem[0].style.setProperty('height', height+'px', 'important');
            }

            return true;
        })
        .fail(this.wizerApi.showError);
    };

    Sortable.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
    
        return defer.promise;
    };
    
    Sortable.prototype.addVotes = function (questionNames) {
        var self = this; 

        var myVotes = [];
        questionNames.forEach(function(q,pos) { 
            myVotes.push( { 
                questionId: self.wizerApi.getQuestionIdByName(q), 
                responseText: (pos+1) } ); 
        });
        self.wizerApi.addVotes({ votes: myVotes });
    };


    Sortable.getRegistration = function () {
        return new Sortable();
    };

    return Sortable;

});