﻿
<div class="{{? !it.isFloat}}container{{?}} actionbutton {{?it.animate}}animate__animated animate__{{=it.animate}}{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} 
{{?it.isHidden}}
    {{?it.isFollower}} {{?it.DB[it.isFollower]!=2}}hidden{{?}} {{??}}hidden{{?}}
{{?}}
{{?it.isFollower}} {{?it.DB[it.isFollower]==1}}disabled hidden{{?}}{{?}}>

    {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

    <div class="row submit">
        <a class="hide-on-med-and-up btn-floating btn-large {{?it.pulse}}pulse{{?}} client-colors {{?it.color}}{{=it.color}}{{??}}button{{?}} client-colors-text text-font2 {{?it.modal}}modal-trigger{{?}}" 
            {{?it.download}} href="Wizer/Pages/Events/{{=wizerApi.eventName()}}/{{=it.download}}" download{{?}}
            {{?it.onclick}}onclick="{{=it.onclick}}"{{?}}
            {{?it.onClickCustomFunction}}custom-function="{{=it.onClickCustomFunction}}"{{?}} 
            {{?it.modal && it.modal.modalID}}href="#{{=it.modal.modalID}}"{{?}}>
            <i class="medium material-icons right">{{=it.icon}}</i>
        </a>
        <!--  Square button when screen is medium/large (btn-navigation class makes small when medium-screen and large when big-screen)-->
        <a class="hide-on-small-only {{?it.isFloat}}btn-floating btn-large{{??}} btn{{?}} {{?it.pulse}}pulse{{?}} client-colors {{?it.color}}{{=it.color}}{{??}}button{{?}} client-colors-text text-font2 {{?it.modal}}modal-trigger{{?}}" 
                {{?it.download}} href="Wizer/Pages/Events/{{=wizerApi.eventName()}}/{{=it.download}}" download{{?}}
                {{?it.onclick}}onclick="{{=it.onclick}}"{{?}}
                {{?it.onClickCustomFunction}}custom-function="{{=it.onClickCustomFunction}}"{{?}} 
                {{?it.onClickFunction}}data-function="{{=it.onClickFunction}}"{{?}} 
                {{?it.onclickQuestion}}data-question="{{=it.onclickQuestion}}"{{?}} 
                {{?it.onclickQuestion2}}data-question2="{{=it.onclickQuestion2}}"{{?}} 
                {{?it.modal && it.modal.modalID}}href="#{{=it.modal.modalID}}"{{?}}>
                  <i class="medium material-icons right">{{=it.icon}}</i>{{=it.title}}
        </a>
    </div>
</div>


<!-- ********************************** -->
<!-- *********  ACTION-MODAL  ********* -->
<!-- ********************************** -->
{{?it.modal}}
<div id="{{=it.modal.modalID}}" class="modal">
    <div class="modal-content">
        <h4>{{=it.modal.header}}</h4>
        <span class="flow-text">{{=it.modal.text}}</span>
    </div>
    <div class="modal-footer">
        <a class="btn modal-close client-colors button2">
            <i class="small material-icons right">close</i>{{=it.modal.close}}
        </a>
        <a  {{?it.modal.onclick}}onclick="{{=it.modal.onclick}}"{{?}} 
            {{?it.modal.onclickFunction}}data-function="{{=it.modal.onclickFunction}}"{{?}} 
            {{?it.modal.onclickQuestion}}data-question="{{=it.modal.onclickQuestion}}"{{?}} 
            {{?it.modal.onclickQuestion2}}data-question2="{{=it.modal.onclickQuestion2}}"{{?}} 
            class="btn client-colors button">
            <i class="small material-icons right">{{=it.icon}}</i>{{=it.modal.action}}
        </a>
    </div>
</div>
{{?}}      