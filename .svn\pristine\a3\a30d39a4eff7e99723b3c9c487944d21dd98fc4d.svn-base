<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutFAC">
  <Include name="HeaderFAC"></Include>
  

  <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{R1_Introduction_Header} ",
      valign: false,
      animate: "zoomIn",
      transparentBox: false,
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{Asesora_Img4}",  alt: "!{R1_Introduction_Header}" ,
          position: "right tiny",
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        _img: { 
          materialboxed: false, _borders: "top right left bottom", frame: "", nopadding: false,
          src: "!{R1_Introduction_Image}",  alt: "!{R1_Introduction_Header}",
          isHiddenWhenSmall: true, 
          src_vert: "!{}",
          animate: "fadeInUp animate__delay-1s"
        },
        position: "up",
        animate: "fadeInUp animate__delay-1s",
        title: "!{R1_Introduction_Title}",
        body: "!{R1_Introduction_Text}"
      }
    }]]>  
  </Component>


  <Component type="Collapsible" customJS="true">
    <![CDATA[{
      templateInEvent: "html/collapsible.dot",
      css: "styles/collapsible.css",
      animate: "fadeInLeft animate__delay-2s",
      accordion: true,
      popout: false,
        expand_more: "expand_more",
        expand_less: "expand_less",
      items: [
        {
          active: false,
          title: "!{R1_Introduction_FACtit}", 
          text: "!{R1_Introduction_FACtext}",
          _texts: [
            { title: "!{}", text: "!{}" },
            { title: "!{}", text: "!{}" } 
          ]
        }
      ]
    }]]>
  </Component> 


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left clicked",
          gdActionTrack: "GD", gdActionSection: "R1_Introduction",
          icon: "replay"
        },
        {
          type: "target", pulse: false, isFloat: true, isLarge: false, class:"pause left2",
          gdActionTrack: "GD", gdActionSection: "LandingPage_Pause",
          icon: "pause_circle_filled"
        },
        {
          type: "target",
          pulse: false,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "R1_Welcome",
          targetSection: "R1_Welcome_FAC",
          label: "!{R1_Welcome}",
          icon: "info"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list",
          isFloat: false, 
          tooltip: "!{}"
        },
        {
          type: "target",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R1_Start",
          targetSection: "SIM_R1_FAC_dashboard",
          label: "!{GD_SIM_R1_Landing} (SELFPACED MODE)",
          icon: "screen_share"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>