﻿define(['Q', 'jsCalc', 'calcbinder', 'progressBar', 'logger'], function (Q, JsCalc, CalcBinder, ProgressBar, logger) {

    var newCalcBinderCache = function (wizerApi) {
        //logger.log("newCalcBinderCache :: constructor");
        this.calcBinders = {};
        this.modelSaveStatesLoaded = {};
        this.wizerApi = wizerApi;
    };

    newCalcBinderCache.prototype.ensureCalcBinder = function (modelName, progressCallback) {
        //logger.log("newCalcBinderCache :: ensureCalcBinder");
        var self = this;
        //console.log(" Voting.prototype.ensureCalcBinder");
        $(document).bind('wizer:wizerapi:command:InvalidateJsCalcModels', function () {
            self.calcBinders = {};
        })

        if (!modelName) {
            //console.log("cannot save calc: values when no model is set");
            var failAsDefer = Q.defer();
            failAsDefer.reject(new Error("cannot save calc: values when no model is set"));
            return failAsDefer.promise;
        } else {
            var calcBinderPromise = self.calcBinders[modelName];

            if (!calcBinderPromise) {
                //console.log("model loading: " + modelName);
                //logger.log("newCalcBinderCache :: ensureCalcBinder : calcBinderPromise");
                var calcBinderLoading = Q.defer();
                calcBinderPromise = calcBinderLoading.promise;
                self.calcBinders[modelName] = calcBinderPromise;
                if (!self.wizerApi.progressBars[modelName]) {
                    self.wizerApi.progressBars[modelName] = new ProgressBar($('body'), 100, 1, modelName);
                    self.wizerApi.progressBars[modelName].drawProgressBar(1);
                }
                // down here instead of in define to enable mocking
                require(['text!events/' + Wizer.EventName + '/' + modelName, 'events/' + Wizer.EventName + '/model/TRCourseActions'], function (modelJson, trCourseActions) {
                    var model;
                    //logger.log("newCalcBinderCache :: calcBinderPromise : TRCourseActions");
                    model = new JsCalc({
                        model: $.parseJSON(modelJson),
                        customActions: trCourseActions,
                        loadCallback: function jsCalcLoadCallback() {
                            //logger.log("newCalcBinderCache :: calcBinderPromise : TRCourseActions : loadCallback");
                            var savedStatePromises = self.wizerApi.getStateCache(modelName);
                            Q.all(savedStatePromises).then(function (savedStates) {
                                //logger.log("newCalcBinderCache :: calcBinderPromise : TRCourseActions : loadCallback : savedStates");
                                var modelStateCalculatedCallBack = function () {
                                    var modelCalculationPromise = model.completeCalculation();

                                    modelCalculationPromise.then(function () {
                                        //logger.log("newCalcBinderCache :: calcBinderPromise : TRCourseActions : completedCalculationPromise");
                                        if (self.wizerApi.progressBars[modelName] != null) {
                                            self.wizerApi.progressBars[modelName].setValue(99);
                                            self.wizerApi.progressBars[modelName].unLoad();
                                        }
                                        var calcBinder = new CalcBinder(model, self.wizerApi);
                                        calcBinder.modelSaveStateLoaded = true;
                                        //logger.log("newCalcBinderCache :: calcBinderPromise : TRCourseActions : calcBinderLoading : resolve");

                                        calcBinderLoading.resolve(calcBinder);
                                    });
                                };
                                if (savedStates != null && (savedStates[0] || savedStates[1])) {
                                    var savedState = savedStates[0];
                                    var historicalState = savedStates[1];
                                    //console.log("loadCallback using old state: " + JSON.stringify(savedState));
                                    model.setState(savedState, historicalState, modelStateCalculatedCallBack);
                                    //console.log(" Get state from calc" + JSON.stringify(model.getState()));
                                }
                                else {
                                    modelStateCalculatedCallBack();
                                }


                            })
                                .fail(function (err) {
                                    //logger.log("newCalcBinderCache :: calcBinderPromise : Failed");
                                    calcBinderLoading.reject(err);
                                    self.wizerApi.showError(err);
                                })
                                .done();
                        },
                        buildProgressCallback: function (progOb) {
                            //this function is not called for small models like simpleworkbook which builds in 1 cycle
                            self.progressCallback(progOb, modelName);
                        }
                    });
                    model.modelName = modelName;
                });
            }
            return calcBinderPromise;
        }
    };

    newCalcBinderCache.prototype.progressCallback = function (progOb, modelName) {
        var value;
        if (progOb.numTotal != 1) {
            value = Math.round((progOb.numComplete / progOb.numTotal) * 100);
        } else {
            value = progOb.numComplete;
        }
        this.wizerApi.progressBars[modelName].setValue(value);
    }

    newCalcBinderCache.prototype.getCalcValue = function (modelName, range) {
        var getting = Q.defer();
        var ensuring = this.ensureCalcBinder(modelName);
        ensuring.then(function (calcBinder) {

            if (calcBinder) {
                calcBinder.getValue(range, undefined /*period*/, function (value) {
                    // checking for booleans which are often returned from calc (Excel) models
                    if (value == true)
                        result = 1;
                    else if (value == false)
                        result = 0;
                    else if (typeof value == 'string') {
                        result = numeral().unformat(value);
                        if (isNaN(result) || (result == 0 && isNaN(value))) {
                            result = value;  // allow plain strings with no numbers in them to just return the string
                        }
                    } else if (value.errorContext) {
                        if (calcCell.uninitialized) {
                            //logger.log(true, "Uninitialized cells: " + JSON.stringify(calcCell.uninitialized));
                        }
                        //logger.log(true, "Error in js calc model. Cell(" + value.errorContext.address() + ") has data '" + value.data + "', text '" + value.text + "' and formula '" + value.errorContext.formula + "'");
                        result = value.data
                    } else {
                        try {
                            result = JSON.stringify(value);
                        } catch (ex) {
                            getting.reject(new Error("Getting value that I cannot even stringify"));
                            return;
                        }
                    }
                    //logger.log("newCalcBinderCache :: calcBinderPromise : getCalcValue : promise : resolve");
                    getting.resolve(result);
                });
            } else {
                getting.reject(new Error("no calcBinder"));
            }
        }, function (err) {
            getting.reject(err);
        });
        return getting.promise;
    }

    newCalcBinderCache.prototype.setCalcValue = function (modelName, range, value) {
        var self = this;
        var setting = Q.defer();

        var ensuring = this.ensureCalcBinder(modelName);
        ensuring.then(function (calcBinder) {
            if (calcBinder) {
                //console.log("setCalcValue ensuring then calcBinder", range, value);
                calcBinder.setValue(range, value)
                    .then(function calcBinderDidSet() {
                        $(document).trigger("wizer:action:newCalcValue");
                        //logger.log("newCalcBinderCache :: setCalcValue : promise : resolve");
                        setting.resolve();
                    });
            } else {
                setting.reject(new Error("no calcBinder"));
            }
        }, function (err) {
            //console.log("setCalcValue ensuring then branch 2");
            setting.reject(err);
        });
        return setting.promise;
    }

    
    newCalcBinderCache.prototype.setCalcValues = function (modelName, values) {
        var self = this;
        var setting = Q.defer();

        var ensuring = this.ensureCalcBinder(modelName);
        ensuring.then(function (calcBinder) {
            if (calcBinder) {

                var counter = 1;
                var listLength = Object.keys(values).length;
               
                if (listLength > 0) {

                    $.each(values, function (range, value) {
                        
                        if (value) {
                            calcBinder.setValue(range, value)
                                //.then(function calcBinderDidSet() {
                                .then(function () {
                                    //$(document).trigger("wizer:action:newCalcValue");
                                    if (counter == listLength) {
                                        setting.resolve(true);
                                    } else {
                                        counter ++;
                                    }

                                });
                        } else {
                            if (counter == listLength) {
                                setting.resolve(true);
                            } else {
                                counter ++;
                            }
                        }

                    });

                } else {
                    setting.resolve(false);
                }


            } else {
                setting.reject(new Error("no calcBinder"));
            }
        }, function (err) {
            //console.log("setCalcValue ensuring then branch 2");
            setting.reject(err);
        });
        return setting.promise;
    }

    /*
    * Low level access, not for general use.
    */
    newCalcBinderCache.prototype.getCalcModel = function (modelName) {
        var getting = Q.defer();
        var ensuring = this.ensureCalcBinder(modelName);
        ensuring.then(function (calcBinder) {
            //console.log("getCalcValue ensuring then");
            if (calcBinder) {
                //console.log("getCalcValue ensuring then calcBinder");
                calcBinder.getModel(function (model) {
                    //console.log("getCalcValue ensuring then calcBinder got: ", value);
                    //logger.log("newCalcBinderCache :: getCalcModel : promise : resolve");
                    getting.resolve(model);
                    //console.log("getCalcValue ensuring then calcBinder resolved");
                });
            } else {
                getting.reject(new Error("no calcBinder"));
            }
        }, function (err) {
            //console.log("getCalcValue ensuring then branch 2");
            getting.reject(err);
        });
        return getting.promise;
    }

    return newCalcBinderCache;

});

