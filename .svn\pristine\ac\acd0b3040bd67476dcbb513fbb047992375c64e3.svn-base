﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'timerUtil', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT, TimerUtil) {

    var RadioButtons = function () {
        this.type = 'RadioButtons';
        this.level = 1;
        this.id = '';
    };

    RadioButtons.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        var self = this;
        self.wizletInfo = wizletInfo;
        self.wizletContext = wizletContext;
        self.wizerApi = wizerApi;

        if (self.wizletInfo.timer) {
            self.serverCaching = wizerApi.caching;
            var key = 'CountDownTimer' + (self.wizletInfo.timer.key ? '.' + self.wizletInfo.timer.key : '') + '.' + (Wizer.EventName ? Wizer.EventName : "noEvent") + '.' + (Wizer.ParticipationId ? Wizer.ParticipationId : "noPid");
            self.timerUtil = new TimerUtil(key);
            self.serverCachingKey = key + '.timer';
            self.serverCachingEveryXSecs = (self.wizletInfo.timer.saveToServerEvery ? self.wizletInfo.timer.saveToServerEvery : 5);
            self.serverCachingCounter = self.serverCachingEveryXSecs;
        }

        self.templateDefer = Q.defer();
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    RadioButtons.prototype.unloadHandler = function () {
        $('#toast-container').hide();
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        $(document).off("wizer:model:change", this.optionSelected);
        
        if (this.wizletInfo.timer) {
            this.timerUtil.stopTimer();
            if (this.resetCallBack) $(document).unbind("wizer:wizlet:command:countdowntimer:reset", this.resetCallBack);
            if (this.restartCallBack) $(document).unbind("wizer:wizlet:command:countdowntimer:restart."+this.wizletInfo.timer.key, this.restartCallBack);
            if (this.finishCallBack) $(document).unbind("wizer:wizlet:command:countdowntimer:finish."+this.wizletInfo.timer.key, this.finishCallBack);
            if (this.refreshTimer) {
                $(document).off('wizer:action:tilepage:self-registration:refresh', this.refreshTimer)
            }
        }
        WizletBase.unloadHandler({ wizlet: this });
    };

    
    RadioButtons.prototype.reset = function (options) {
        this.timerUtil.stopTimer();
        var self = this;
        setTimeout(function () {
            Wizer.ResetCountDownTimer = false;
            self.serverCaching.setValue(self.serverCachingKey, { "value": "" }, { immediately: true });
            self.timerUtil.clearSavedValueTimerValue();
            self.timerUtil.timerIsRunning = false;
            return self.render(options)
            .then(function () {
                $(document).trigger("wizer:wizlet:command:countdowntimer:reset:done");
            });
        }, 50);
    }


    RadioButtons.prototype.render = function (options) {
        var self = this;

        var preLoad = new Q.defer();

        if (self.wizletInfo.optionsFromVote) {
            var binding = self.wizerApi.getQuestionIdByName(self.wizletInfo.optionsFromVote);
            
            self.wizerApi.getVotes({questionIds: [binding]}).then(function(response){
                
                response.votes = response.votes.reverse();
                var options = [];
                var val;

                var cont=0;
                response.votes.forEach(function(vote,i) {
                    val = vote.responseText;
                    if (self.wizletInfo.includeMyVote || (Wizer.ParticipantEmail!==vote.participantEmail) ) {
                        cont ++;
                        options.push({
                            'value': cont+': '+val,
                            'label': cont,
                            'title': val
                        });
                    }
                });

                self.wizletInfo.options = options;

                if (self.wizletInfo.moreInfoFromVote) {
                    var bindingMore = self.wizerApi.getQuestionIdByName(self.wizletInfo.moreInfoFromVote);
                    self.wizerApi.getVotes({questionIds: [bindingMore]}).then(function(response){

                        response.votes = response.votes.reverse();

                        var cont=0;
                        response.votes.forEach(function(vote,i) {
                            if (self.wizletInfo.includeMyVote || (Wizer.ParticipantEmail!==vote.participantEmail) ) {
                                options[cont].moreInfo = vote.responseText;
                                cont ++;
                            }
                        });
        
                        self.wizletInfo.options = options;
                        preLoad.resolve(true);
                    });
                } else {
                    preLoad.resolve(true);
                }
            });
        } else {
            preLoad.resolve(true);
        }


        return preLoad.promise.then(function () {
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            self.id = options.wizletInfo.id ? options.wizletInfo.id : '';

            //self.init(options.context.find('input[type="radio"]'));
            
            var bind = options.wizletInfo.bind || options.wizletInfo.binding;

            //Check if already played
            //if (!options.wizletInfo.noBlockAnswer)
            if (options.wizletInfo.preloadSaved)
            self.getVote(bind).then(function(value) { 
                    
                //if (options.wizletInfo.id && localStorage.getItem( options.wizletInfo.id ) == 1) {
                if (value) {           
                    //self.getVote(bind).then(function(value) {   
                        self.refreshOptions(self, options.context, options.wizletInfo, value);
                    //});

                } else {
                    
                    
                }

            });


            if (options.wizletInfo.isSolution)
                self.showCorrectOptions(options.context);


            if (options.wizletInfo.moreInfo)
                options.context.find('ul.collapsible').collapsible({
                    onOpenStart : function(current_item) {
                        $(current_item).find('i.expand').html('expand_less');
                    },
                    
                    onCloseStart : function(current_item) {
                        $(current_item).find('i.expand').html('expand_more');                        
                    }
                });

            if (options.wizletInfo.resultsBtn)  
                options.context.find('.modal').modal();
              

            var checkInput = function(input) {
                
                //highlight chosen option and not the not selected options
                $(input).closest('li').addClass('chosen');
                options.context.find('[type="radio"]:not(:checked)').closest('li').removeClass('chosen');

                if (options.wizletInfo.checkBtn && options.wizletInfo.checkBtn.idToHide) 
                    $('#'+options.wizletInfo.checkBtn.idToHide).attr('hidden',true);  

                if (options.wizletInfo.autocheck) {

                    var isCorrect = false;
                    $.each(options.context.find('[type="radio"]'), function (idx, option) {
                        
                        if (!options.wizletInfo.noBlockAnswer) $(option).closest('li').addClass('disabled');
    
                        if ($(option).is(':checked')) {  
                            //Check if the answer is correct
                            $('#toast-container .toast').hide()
                            if (options.wizletInfo.checkBtn)
                            if (options.wizletInfo.checkBtn.toast) {
                                var toast = options.wizletInfo.checkBtn.toast;
                                self.toast ($(option).data('correct'), 
                                            toast,
                                            toast.points ? {score:options.wizletInfo.score.points.ifCorrect , text: toast.points} : null);
                            } else if (options.wizletInfo.checkBtn.toastMsg) {
                                M.toast({
                                    html: options.wizletInfo.checkBtn.toastMsg,
                                    classes: 'rounded'
                                });
                            }

                            //save chosen option
                            var binding = $(input).data('bind') || $(input).data('binding');
                            self.addVote(binding, $(input).val());
                            
                            if (self.wizletInfo.saveInModel)
                                $(input).attr('data-value', $(input).val()).trigger('updateModelInput');  
                                
                            
                            isCorrect = $(input).data('correct');  

                            //save score depending on wheteher if it's correct and update total
                            if (options.wizletInfo.score) {
                                var score = options.wizletInfo.score;
                                self.updateTotal(score.question, 
                                                isCorrect ? score.points.ifCorrect : score.points.ifIncorrect, 
                                                score.questions, score.total, score.total2, score.totals, score.superTotal);
                            }

                            if (options.wizletInfo.checkBtn) {
                                if (isCorrect && options.wizletInfo.checkBtn.idToShowIfCorrect) {
                                    $('#'+options.wizletInfo.checkBtn.idToShowIfCorrect).removeAttr('hidden')
                                                                                    .find('a').removeAttr('disabled');

                                    if (options.wizletInfo.modal_feedbacks) {
                                        M.Modal.getInstance( $(options.wizletInfo.modal_feedbacks.popupID) ).open();
                                        self.showResultModal ( options.wizletInfo.modal_feedbacks.popupID, options.wizletInfo.modal_feedbacks.popup_FB1);
                                    }
                                }
                            
                                if (!isCorrect && options.wizletInfo.checkBtn.idToShowIfIncorrect) {                                    
                                    $('#'+options.wizletInfo.checkBtn.idToShowIfIncorrect).removeAttr('hidden')
                                                                                    .find('a').removeAttr('disabled');   
                                                                                    
                                    if (options.wizletInfo.modal_feedbacks) {
                                        M.Modal.getInstance( $(options.wizletInfo.modal_feedbacks.popupID) ).open();
                                        self.showResultModal ( options.wizletInfo.modal_feedbacks.popupID, options.wizletInfo.modal_feedbacks.popup_FB2);
                                    }
                                                                                    
                                }    
                                
                                if (options.wizletInfo.checkBtn.idsToShow) {
                                    if (isCorrect && options.wizletInfo.checkBtn.newIcon) $(options.wizletInfo.checkBtn.idsToShow+' i.material-icons').html(options.wizletInfo.checkBtn.newIcon);
                                    $(options.wizletInfo.checkBtn.idsToShow).removeAttr('hidden')
                                                                            .find('a:not(.follower)').removeAttr('disabled');
                                }

                                if (options.wizletInfo.checkBtn.idToClick) {
                                    $('#'+options.wizletInfo.checkBtn.idToClick+' a').first().click();
                                }

                                //automatically scroll to the top of the page after confirm
                                if (options.wizletInfo.checkBtn.scrollToTop) {
                                    $('HTML, BODY').animate({ scrollTop: 0 }, 1000);
                                }
                                //automatically scroll to the bottom of the page after confirm
                                if (options.wizletInfo.checkBtn.scrollToDown) {
                                    options.wizletInfo.checkBtn.scrollToDown = false;
                                    $('HTML, BODY').animate({ scrollTop: $('body').height() }, 1000);
                                }
                               
                            }
                            if (options.wizletInfo.turnoffAnswer) $(option).closest('li').removeClass('turnedoff');

                        } else {
                            
                            if (options.wizletInfo.turnoffAnswer) $(option).closest('li').addClass('turnedoff');
                        }
                    });
    
                    if (! options.wizletInfo.checkAtTheEnd)
                        options.context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');

                    
                    if (options.wizletInfo.timer) {
                        options.context.find('.countDownTimerHolder').remove();
                        $(document).trigger("wizer:wizlet:command:countdowntimer:finish."+options.wizletInfo.timer.key);
                    }

                } else {
                    options.context.find("#checkBtn").removeAttr('hidden');
                }
            }



            options.context.find('input[type="radio"]').closest('li>div').on('click', function(e) {

                e.preventDefault();

                var $input = $(this).find('input[type="radio"]');
                
                $input.prop('checked',true);

                checkInput ($input);

            });

            // options.context.find('input[type="radio"]').on('change', function(e) {
                
            //     var $input = $(this);                
            //     checkInput ($input);
                
            // });

            
            
            options.context.find("#checkBtn").on("click", function (e) {

                //if (options.wizletInfo.id) localStorage.setItem(options.wizletInfo.id, 1);  

                if (options.wizletInfo.noBlockAnswer) {
                    $(this).removeClass('pulse');
                } else {
                    $(this).closest('.row').prop('hidden', true);
                    $(this).prop('hidden', true);
                }
                
                var isCorrect = false;
                $.each(options.context.find('[type="radio"]'), function (idx, option) {
                    
                    if (!options.wizletInfo.noBlockAnswer) $(option).closest('li').addClass('disabled');

                    if ($(option).is(':checked')) {

                        //Check if the answer is correct
                        $('#toast-container .toast').hide()
                        if (options.wizletInfo.checkBtn)
                        if (options.wizletInfo.checkBtn.toast) {
                            var toast = options.wizletInfo.checkBtn.toast;
                            self.toast ($(option).data('correct'), 
                                        toast,
                                        toast.points ? {score:options.wizletInfo.score.points.ifCorrect , text: toast.points} : null);
                        } else if (options.wizletInfo.checkBtn.toastMsg) {
                            M.toast({
                                html: options.wizletInfo.checkBtn.toastMsg,
                                classes: 'rounded'
                            });
                        }
                        
                        //save chosen option 
                        var binding = $(this).data('bind') || $(this).data('binding');
                            self.addVote(binding, $(this).val()).then(res=>{
                                console.log("✔",res)
                                if (self.wizletInfo.saveInModel)
                                $(this).attr('data-value', $(this).val()).trigger('updateModelInput');  
    
                                isCorrect = $(this).data('correct');        
    
                                //save score depending on wheteher if it's correct and update total
                                if (options.wizletInfo.score) {
                                    var score = options.wizletInfo.score;
                                    self.updateTotal(score.question, 
                                                    isCorrect ? score.points.ifCorrect : score.points.ifIncorrect, 
                                                    score.questions, score.total, score.total2, score.totals, score.superTotal);
                                }
                            }).then((res) => {
                                setTimeout(() => {
                                    
                                    // var retVal = confirm("Do you want to continue ?");
                                    // if( retVal == true ) {
                                   
                                        options.context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');
    
                                        if (options.wizletInfo.timer) {
                                            options.context.find('.countDownTimerHolder').remove();
                                            $(document).trigger("wizer:wizlet:command:countdowntimer:finish."+options.wizletInfo.timer.key);
                                        }
                                        
                                        if (options.wizletInfo.checkBtn) {
                                            if (isCorrect && options.wizletInfo.checkBtn.idToShowIfCorrect)
                                                $('#'+options.wizletInfo.checkBtn.idToShowIfCorrect).removeAttr('hidden')
                                                                                                .find('a').removeAttr('disabled');
                                        
                                            if (!isCorrect && options.wizletInfo.checkBtn.idToShowIfIncorrect)
                                                $('#'+options.wizletInfo.checkBtn.idToShowIfIncorrect).removeAttr('hidden')
                                                                                                .find('a').removeAttr('disabled');        
                                            
                                            if (options.wizletInfo.checkBtn.idsToShow) {
                                                if (isCorrect && options.wizletInfo.checkBtn.newIcon) $(options.wizletInfo.checkBtn.idsToShow+' i.material-icons').html(options.wizletInfo.checkBtn.newIcon);
                                                $(options.wizletInfo.checkBtn.idsToShow).removeAttr('hidden')
                                                                                        .find('a:not(.follower)').removeAttr('disabled');
                                            }
    
                                            if (options.wizletInfo.checkBtn.idToClick) {
                                                $('#'+options.wizletInfo.checkBtn.idToClick+' a').first().click();
                                            }
    
                                            //automatically scroll to the top of the page after confirm
                                            if (options.wizletInfo.checkBtn.scrollToTop) {
                                                $('HTML, BODY').animate({ scrollTop: 0 }, 1000);
                                            }
                                            //automatically scroll to the bottom of the page after confirm
                                            if (options.wizletInfo.checkBtn.scrollToDown) {
                                                options.wizletInfo.checkBtn.scrollToDown = false;
                                                $('HTML, BODY').animate({ scrollTop: $('body').height() }, 1000);
                                            }
                                        
                                        }
                                        
                                        if (options.wizletInfo.resetBtn) options.context.find('#resetBtn').removeAttr('hidden');
                                        
                                        if (options.wizletInfo.resultsBtn) {
                                            var button = options.context.find('#resultsBtn');
    
                                            button.removeAttr('hidden');
    
                                            if (options.wizletInfo.resultsBtn.autoShow) {
                                                M.Modal.getInstance( $('#'+options.wizletInfo.resultsBtn.popupID) ).open();
                                                self.showResultModal ( $(button).attr('href'), $(button).data('modal'));
                                            }
                                        }
                                    // } else {
                                    //    return false;
                                    // }
                                    // // return confirm dialog. 
                                    // return true;
                                }, 850);
                                
                            });
                            
                        
                        
                    } else {
                        
                        if (options.wizletInfo.turnoffAnswer) $(option).closest('li').addClass('turnedoff');
                    }
                });


                

            });


            
            
            options.context.find("#resetBtn").on("click", function (e) {

                $(this).prop('hidden', true);
                if (options.wizletInfo.resultsBtn) options.context.find('#resultsBtn').prop('hidden', true);

                self.removeVotes(bind);
                
                $.each(options.context.find('input[type="radio"]'), function (idx, option) {                    
                    $(option).closest('li').removeClass('disabled');
                    $(option).closest('li').removeClass('chosen');
                    $(option).prop('checked',false);  
                });

                //Check if the answer is correct
                $('#toast-container .toast').hide()
                if (options.wizletInfo.resetBtn.toastMsg) {
                    M.toast({
                        html: options.wizletInfo.resetBtn.toastMsg,
                        classes: 'rounded'
                    });
                }

                options.context.find("i.check-icon").addClass('scaled-out').removeClass('scale-in');

            });

            

            options.context.find("#resultsBtn").off("click").on("click", function (e) {
                
                self.showResultModal ( $(this).attr('href'), $(this).data('modal'));

            });


            //The follower listens the model to load the leader's selected option and enable the navigation button
            if (self.iAmFollower()) {
                        
                //check if the leader had already answered
                var teamId = self.wizerApi.getQuestionIdByName(options.wizletInfo.trackTeam);
                var questionId = self.wizerApi.getQuestionIdByName(bind);
                self.wizerApi.getForemanVotes(teamId,[questionId]).then(function (response) {     
                                                
                    if ( response.votes[questionId].length > 0 ) {
                        self.optionSelected (
                            {data: {
                                self: self, 
                                context: options.context, 
                                info: options.wizletInfo,
                                value: response.votes[questionId][0]
                            }})
                    } else {
                        //If not already answered, keep listening the model to change
                        $(document).on("wizer:model:change."+self.id, { 
                                            self: self, 
                                            //context: options.context, 
                                            //info: options.wizletInfo,
                                            value: null
                                        }, self.optionSelected);
                    }
                });  
            };

            
            // *** Countdowntimer ***
            if (options.wizletInfo.timer) {  
                if (Wizer.ResetCountDownTimer) {
                    self.reset(options);
                }
        
                if (!self.resetCallBack) {
                    self.resetCallBack = function () {
                        return self.reset(options);
                    };
                }
                if (!self.restartCallBack) {
                    self.restartCallBack = function () {
                        // return self.restart(options);
                        return self.onReadyToRender(options);
                    };
                }

                if (!self.finishCallBack) {
                    self.finishCallBack = function () {
                        return self.finish(options);
                    };
                }
        
                if (!self.refreshTimer) {
                    self.refreshTimer = function () {
                        self.wizerApi.getCurrentAction().then(function (resp) {
                            self.wizerApi.getWizletInfos(resp.scriptName, Wizer.ParticipationId).then(function (response) {
                                $.each(response, function (i, res) {
                                    if (res.wizletType === "RadioButtons") {
                                        options.wizletInfo.isRegistered = res.wizletInfo.isRegistered;
                                        return false;
                                    }
                                });
        
                                return self.reset(options);
                            });
                        });
                    };
                }
        
                $(document).unbind("wizer:wizlet:command:countdowntimer:reset", self.resetCallBack).bind("wizer:wizlet:command:countdowntimer:reset", self.resetCallBack);
                $(document).off('wizer:action:tilepage:self-registration:refresh', self.refreshTimer).on('wizer:action:tilepage:self-registration:refresh', self.refreshTimer);
                $(document).unbind("wizer:wizlet:command:countdowntimer:restart."+options.wizletInfo.timer.key, self.restartCallBack).bind("wizer:wizlet:command:countdowntimer:restart."+options.wizletInfo.timer.key, self.restartCallBack);
                $(document).unbind("wizer:wizlet:command:countdowntimer:finish."+options.wizletInfo.timer.key, self.finishCallBack).bind("wizer:wizlet:command:countdowntimer:finish."+options.wizletInfo.timer.key, self.finishCallBack);
        
                //var startTime = this.wizletInfo.startTime;
                var startTimePromise = null;
                if (options.wizletInfo.timer.startTime) {
                    options.wizletInfo.timer.startTimeMs = TimerUtil.parseTimeStringToMs(options.wizletInfo.timer.startTime);
                    startTimePromise = Q(options.wizletInfo.timer.startTimeMs);
        
                    // continue timer from where the user left off - but only do it for users where start time is defined in xml
                    if (self.timerUtil.isTimerRunning()) {
                        startTimePromise = Q(self.timerUtil.getTimerValue());
                    }
                }
                else { // always fetch new time for users where assessment time is used.
                    //var startTimeQid = self.wizerApi.getQuestionIdByName(options.wizletInfo.timer.startTimeQuestion);
                    startTimePromise = self.getParticipantAssessmentTime(options.wizletInfo.timer);
                }
        
                Q.all([self.serverCaching.getValue(self.serverCachingKey), self.templateDefer.promise, startTimePromise]).spread(function (val, template, startTime) {

                    self.val = val;
                    self.startTime = startTime;
                    template({ timeInMs: startTime });
                    //self.onReadyToRender(val, template, startTime, options);
                });

            }

            return true;
        })
        .fail(this.wizerApi.showError);

        });
    };

    
    RadioButtons.prototype.finish = function (options) {
        this.timerUtil.stopTimer();
        // var self = this;
        // setTimeout(function () {
        //     self.serverCaching.setValue(self.serverCachingKey, { "value": "00:00" }, { immediately: true })
        //     //return self.render(options)
        //     .then(function () {         
        //         options.context.find('.countDownTimerHolder').remove();
        //         // options.context.find('.countDownleadText').remove();
        //         // options.context.addClass('finished');  
        //         var link = options.context.find('.countDownTimer');
        //         link.html(link.data('name'));
        //     });
        // }, 500);
    }
    
    RadioButtons.prototype.actionsWhenDone = function (options) {
        
        var self = this;

        options.context.find('.countDownTimerHolder').addClass('finished');
        if (!options.wizletInfo.noBlockAnswer) options.context.find('form>ul>li').addClass('disabled');
        if (options.wizletInfo.isCheck) options.context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');
        // if (options.wizletInfo.checkBtn.idToClick)
        //     $('#'+options.wizletInfo.checkBtn.idToClick).removeAttr('hidden').find('a').removeAttr('disabled');   
        //     $('#'+options.wizletInfo.checkBtn.idToClick+' a').first().click();   
        if (options.wizletInfo.checkBtn.idsToShow)
            $(options.wizletInfo.checkBtn.idsToShow).removeAttr('hidden')
                                                            .find('a').removeAttr('disabled');    
        if (options.wizletInfo.checkBtn.idToShowIfIncorrect)
            $('#'+options.wizletInfo.checkBtn.idToShowIfIncorrect).removeAttr('hidden')
                                                            .find('a').removeAttr('disabled');    
    }

    RadioButtons.prototype.onReadyToRender = function (options) {
        var self = this;
        var val=self.val;
        var startTime=self.startTime;
        
        if (startTime && isNaN(Number(startTime))) {
            startTime = TimerUtil.parseTimeStringToMs(startTime);
        }
        if (val && val.value && self.wizletInfo.timer.startTime) {
            if (isNaN(Number(val.value))) {
                val.value = TimerUtil.parseTimeStringToMs(val.value);
            }
            var serverDate = val.value;
            var localDate = startTime;
            if (serverDate < localDate) {
                startTime = serverDate;
            }
        }     
        var link = options.context.find('.countDownTimer');
        var isCustomizedTime = false;
        if (Math.round(startTime) == 0) {
            link.html(link.data('name'));
            link.addClass("timer00-00")
            if (self.wizletInfo.timer.jumpto && !self.bypassTimer) {
                self.wizerApi.jumpToSection(self.wizletInfo.timer.jumpto);
            }
            self.actionsWhenDone(options);
            //$(document).unbind("wizer:wizlet:command:countdowntimer:reset", self.resetCallBack);
            return;
        }

        self.timerUtil.startTimer(link, startTime, {
            done: function done() {
                console.log('done')
                if (self.wizletInfo.timer.jumpto && !self.bypassTimer) {
                    self.wizerApi.jumpToSection(self.wizletInfo.timer.jumpto);
                }
                self.serverCaching.setValue(self.serverCachingKey, { "value": "00:00" }, { immediately: true });
                
                var bind = self.wizletInfo.bind || self.wizletInfo.binding;

                self.getVote(bind).then(function(value) {                         
                    if (!value) {           
                        self.addVote(bind, 0);         
                    }

                });

                self.actionsWhenDone(options);
                //$(document).unbind("wizer:wizlet:command:countdowntimer:reset", self.resetCallBack);
            },
            updateTimer: function (newTime, elapsed) {
                var elapsedInt = parseInt(elapsed / 1000);
                var saveStateBool = (elapsedInt % self.serverCachingEveryXSecs) === 0 ? true : false;
                if (saveStateBool && self.serverCachingCounter !== elapsedInt) {
                    self.serverCachingCounter = elapsedInt;
                    self.serverCaching.setValue(self.serverCachingKey, newTime, { immediately: true });
                    // self.serverCachingCounter = self.serverCachingEveryXSecs;
                }
            },
            error: function errorHandler(msg) {
                logger.log(true, "CountDownTimer is in trouble: " + msg);
            }
        }, isCustomizedTime);
        return startTime;
    }

    RadioButtons.prototype.onReadyToRender_old = function (val, template, startTime, options) {
        var self = this;
        var userNotRegisteredMessage = (options.wizletInfo.timer.userNotRegisteredMessage) ? options.wizletInfo.timer.userNotRegisteredMessage : "User not registered";
        // user not registered for session or any other failure
        if (startTime === null) {
            var fragment = template({ time: userNotRegisteredMessage, timeInMs: startTime, leadText: "" });
            // options.context.html(fragment);
            return;
        }
        if (startTime && isNaN(Number(startTime))) {
            startTime = TimerUtil.parseTimeStringToMs(startTime);
        }
        if (val && val.value && self.wizletInfo.timer.startTime) {
            if (isNaN(Number(val.value))) {
                val.value = TimerUtil.parseTimeStringToMs(val.value);
            }
            var serverDate = val.value;
            var localDate = startTime;
            if (serverDate < localDate) {
                startTime = serverDate;
            }
        }
        var formattedStartTime = TimerUtil.formatTimeFromMs(startTime);
        var fragment = template({ time: formattedStartTime, timeInMs: startTime, leadText: self.wizletInfo.timer.leadText, timeUpText: self.wizletInfo.timer.timeUpText, isAssessmentStartTimer: self.wizletInfo.timer.isAssessmentStartTimer });
        // options.context.html(fragment);
        var link = options.context.find('.countDownTimer');
        var isCustomizedTime = true;
        if (Math.round(startTime) == 0) {
            link.html(link.data('name'));
            link.addClass("timer00-00")
            if (self.wizletInfo.timer.jumpto && !self.bypassTimer) {
                self.wizerApi.jumpToSection(self.wizletInfo.timer.jumpto);
            }
            //$(document).unbind("wizer:wizlet:command:countdowntimer:reset", self.resetCallBack);
            return;
        }

        self.timerUtil.startTimer(link, startTime, {
            done: function done() {
                console.log('done')
                if (self.wizletInfo.timer.jumpto && !self.bypassTimer) {
                    self.wizerApi.jumpToSection(self.wizletInfo.timer.jumpto);
                }
                self.serverCaching.setValue(self.serverCachingKey, { "value": "00:00" }, { immediately: true });
                //$(document).unbind("wizer:wizlet:command:countdowntimer:reset", self.resetCallBack);
            },
            updateTimer: function (newTime, elapsed) {
                var elapsedInt = parseInt(elapsed / 1000);
                var saveStateBool = (elapsedInt % self.serverCachingEveryXSecs) === 0 ? true : false;
                if (saveStateBool && self.serverCachingCounter !== elapsedInt) {
                    self.serverCachingCounter = elapsedInt;
                    self.serverCaching.setValue(self.serverCachingKey, newTime, { immediately: true });
                    // self.serverCachingCounter = self.serverCachingEveryXSecs;
                }
            },
            error: function errorHandler(msg) {
                logger.log(true, "CountDownTimer is in trouble: " + msg);
            }
        }, isCustomizedTime);
        return startTime;
    }

    RadioButtons.prototype.getParticipantAssessmentTime = function (wizletInfo) {
        var assessmentTimeDefer = Q.defer();
        var timeInMs;
        var moduleId = Wizer.assessmentModule;
        var data = { participantId: Wizer.ParticipationId };
        if (typeof wizletInfo.isRegistered !== "undefined" && wizletInfo.isRegistered && wizletInfo.isRegistered.toLowerCase() !== "true") {
            return Q(null);
        }
        data.moduleId = moduleId;
        // call api
        Q(AjaxGetJson("Assessment", "AssessmentDuration", data)).then(function (response) {
            if (response.success) {
                var startTime = moment.utc(response.startTime).valueOf();
                var endTime = moment.utc(response.endTime).valueOf();
                var now = moment.utc().valueOf();
                if (wizletInfo.isAssessmentStartTimer) {
                    timeInMs = startTime - now;
                }
                else {
                    timeInMs = endTime - now;
                }
                assessmentTimeDefer.resolve(timeInMs);
            }
            else {
                // not registered for session
                assessmentTimeDefer.resolve(null);
            }
        })
        return assessmentTimeDefer.promise;
    }


            
    RadioButtons.prototype.iAmFollower = function () {
        var self = this;
        return (self.wizletInfo.isFollower && 
                self.wizletInfo.DB[self.wizletInfo.isFollower] == 1);
    };


    RadioButtons.prototype.optionSelected = function (event) {
        var self = event.data.self;

        var value = event.data.value;
        
        if (value) {
            self.refreshOptions(self, self.wizletContext, self.wizletInfo, value);
            $(document).off("wizer:model:change."+self.id, self.optionSelected);
        } else {
            //check the leader's answer to refresh
            var teamId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackTeam);
            
            var bind = self.wizletInfo.bind || self.wizletInfo.binding;  
            var questionId = self.wizerApi.getQuestionIdByName(bind);
            
            self.wizerApi.getForemanVotes(teamId,[questionId]).then(function (response) { 
                var leaderVal =  response.votes[questionId][0];
                
                if (leaderVal) {
                    self.refreshOptions(self, self.wizletContext, self.wizletInfo, leaderVal);
                    $(document).off("wizer:model:change."+self.id,self.optionSelected);
                }
            });  
        }


    };


    RadioButtons.prototype.refreshOptions = function (self, context, info, value) {

        var isCorrect = false;
        $.each(context.find('input[type="radio"]'), function (idx, option) {
            
            if (!info.noBlockAnswer) $(option).closest('li').addClass('disabled');

            if ($(option).prop('value') === value) {

                $(option).closest('li').addClass('chosen');
                $(option).prop('checked',true);
                
                isCorrect = $(this).data('correct'); 
                
                //save score depending on wheteher if it's correct and update total
                if (info.score) {
                    var score = info.score;
                    self.updateTotal(score.question, 
                                    isCorrect ? score.points.ifCorrect : score.points.ifIncorrect, 
                                    score.questions, score.total, score.total2, score.totals, score.superTotal);
                }

                //Check if the answer is correct
                $('#toast-container .toast').hide()
                if (info.checkBtn)
                if (info.checkBtn.toast) {
                    var toast = info.checkBtn.toast;                    
                    self.toast ($(option).data('correct'), 
                                toast,
                                toast.points ? {score:info.score.points.ifCorrect , text: toast.points} : null);
                } else if (info.checkBtn.toastMsg) {
                    M.toast({
                        html: info.checkBtn.toastMsg,
                        classes: 'rounded'
                    });
                }
                
                if (info.resetBtn) context.find('#resetBtn').removeAttr('hidden');
                if (info.resultsBtn) context.find('#resultsBtn').removeAttr('hidden');
            

            } else {
                
                if (info.turnoffAnswer) $(option).closest('li').addClass('turnedoff');
            }

        });
        
        context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');
                
        //show the hidden component (navigation button)
        if (info.checkBtn) {
            if (info.checkBtn.idToHide) 
                $('#'+info.checkBtn.idToHide).attr('hidden',true);      
                
            if (isCorrect && info.checkBtn.idToShowIfCorrect)
                $('#'+info.checkBtn.idToShowIfCorrect).removeAttr('hidden')
                                                                    .find('a').removeAttr('disabled');
            
            if (!isCorrect && info.checkBtn.idToShowIfIncorrect)
                $('#'+info.checkBtn.idToShowIfIncorrect).removeAttr('hidden')
                                                                    .find('a').removeAttr('disabled');

            if (info.checkBtn.idsToShow) {
                if (isCorrect && info.checkBtn.newIcon) $(info.checkBtn.idsToShow+' i.material-icons').html(info.checkBtn.newIcon);
                $(info.checkBtn.idsToShow).removeAttr('hidden')
                                            .find('a:not(.follower)').removeAttr('disabled');
            }

            if (info.checkBtn.idToClick) {
                $('#'+info.checkBtn.idToClick+' a').first().click();
            }

            if (info.checkBtn.scrollToTop) {
                $('HTML, BODY').animate({ scrollTop: 0 }, 1000);
            }
            if (info.checkBtn.scrollToDown) {
                info.checkBtn.scrollToDown = false;
                $('HTML, BODY').animate({ scrollTop: $('body').height() }, 1000);
            }
        }
    };

    


    RadioButtons.prototype.showCorrectOptions = function (context) {

        $.each(context.find('input[type="radio"]'), function (idx, option) {
            $(option).closest('li').addClass('disabled');
            if ($(option).data('correct')) {
                $(option).closest('li').addClass('chosen');
                $(option).prop('checked',true);     
            }
        });
        
        context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');
         
    };

    
    RadioButtons.prototype.init = function (inputs) {
        var self = this; 

        var binds = _.unique(
                        inputs.filter('[data-bind]').map(function (idx,ele) {
                            return $(ele).data('bind');
                        }).get()
                    );
        self.removeVotes(binds);
    };

    RadioButtons.prototype.toast = function (isCorrect, toast, points) {
        var self = this; 
        
        var toast;
        if (isCorrect) {
            toast = toast.ifCorrect;
            if (points) toast += '<br><br>+' + points.score + ' ' + points.text;
        } else {
            toast = toast.ifIncorrect;
        }

        M.toast({
            html: toast,
            classes: 'rounded'
        });
    };
    

    RadioButtons.prototype.updateTotal = function (questionName, val, questions, qTotal, qTotal2, questionsTot, qTotalTol) {
        var self = this; 

        var promise1 = self.addVote(questionName, val);
        promise1.then(function () {
                
            var questionIds = [];
            //the total score is the sum of all the individual scores
            $.each(questions, function (idx, question) {
                questionIds.push( self.wizerApi.getQuestionIdByName(question) );
            });
            
            var totalPoints = 0;
            
            self.wizerApi.getMyVotes(questionIds).then(function(response) {
                $.each(response.votes, function(k,v) {
                    if (v[0]) totalPoints += parseInt(v[0]);
                });
                
                if (qTotal2) self.addVote(qTotal2, totalPoints);

                var promise2 = self.addVote(qTotal, totalPoints);
                promise2.then(function () {
                    //if there's a super-total metric, sum the partial total
                    if (qTotalTol) {

                        var questionTotIds = [];
                        //the total-total score is the sum of all the individual total-scores
                        $.each(questionsTot, function (idx, question) {
                            questionTotIds.push( self.wizerApi.getQuestionIdByName(question) );
                        });
                        
                        var totalTotPoints = 0;
                        self.wizerApi.getMyVotes(questionTotIds).then(function(response) {
                            $.each(response.votes, function(k,v) {
                                if (v[0]) totalTotPoints += parseInt(v[0]);
                            });
                            console.log("Total total:" + totalTotPoints);
                            
                            self.addVote(qTotalTol, totalTotPoints);
                        });
                    }
                });



            });
        });
            
    };

    /**
     * Promise function: Get vote value from a questionName
     */
    RadioButtons.prototype.getVote = function (questionName) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = new Q.defer();

        var waiting =  self.wizerApi.getMyVotes([questionId]);

        waiting.then(function (result) {            
            defer.resolve(result.votes[questionId][0]);
        });          
            
        
        return defer.promise;
    };

    RadioButtons.prototype.removeVotes = function (questionName) {
        var self = this; 

        var myVotes = [];
        if (Array.isArray(questionName)) {
            questionName.forEach(function(q) { myVotes.push( { questionId: self.wizerApi.getQuestionIdByName(q) } ); });
        } else {
            myVotes.push({ questionId: self.wizerApi.getQuestionIdByName(questionName) });
        }
        self.wizerApi.removeVotes({ votes: myVotes });
    };
    
    RadioButtons.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };

    RadioButtons.prototype.addVotes = function (questionNames, values) {
        var self = this; 

        var myVotes = [];
        questionNames.forEach(function(q,pos) { 
            myVotes.push( { 
                questionId: self.wizerApi.getQuestionIdByName(q), 
                responseText: values[pos] } ); 
        });
        self.wizerApi.addVotes({ votes: myVotes });
    };
    
    


    /**
     * Load an action screen inside a modal window
     */
    RadioButtons.prototype.showResultModal = function(href, modal) {
        var self = this;  

        var $modal = $( href );
        $modal.detach().appendTo('.mainArea');

        self.loadPage(
            modal, 
            self.wizletContext, 
            self.unsedContext, 
            $( $modal.find('.modal-content') ) 
        );
    }

    RadioButtons.prototype.loadPage = function (actionXMLName, context, unusedContext, tabContainer) {
        var self = this;       

        //unload previous 
        if (self.currentWizletModule && self.currentWizletModule.length > 0) {
            $.each(self.currentWizletModule, function (index, module) {
                if (module.wizletInstance.unloadHandler) {
                    module.wizletInstance.unloadHandler();
                }
            });
        }
        
        $('.material-tooltip').remove();
        
        var loading = self.wizerApi.loadActionInContainer(actionXMLName, context, unusedContext, tabContainer);
        loading.then(function (loads) {
            
            self.currentWizletModule = loads;
            
            var page = "wizer:action:init.mainArea";
                        
            $(document).trigger(page, actionXMLName);
        });
        
    };


    RadioButtons.getRegistration = function () {
        return new RadioButtons();
    };

    return RadioButtons;

});