﻿
<div class="container {{?it.animate}}animate__animated animate__{{=it.animate}}{{?}}{{?it.fullscreen}} fullscreen{{?}}{{?it.isHiddenWhenSmall}} hide-on-small-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-med-and-up{{?}}"
     {{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    <div class="_row_">
        <div>
            
            {{?it.header}}<h4 class="header title">{{=it.header}}</h4>{{?}}
            {{?it.subheader}}<h5 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.subheader}}</h5>{{?}}
            {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

            <div class="card hoverable {{?it.size}}{{=it.size}}{{?}}">
                
                {{?it.content}}
                <div class="card-content no-pad-bot">
                    {{?it.content.title}}<span class="card-title">{{=it.content.title}}</span>{{?}}
                    {{?it.content.body}}<p {{?it.content.flowtext}}class="flow-text"{{?}}>{{=it.content.body}}</p>{{?}}
                </div>  
                {{?}}

                <div class="card-image">

                    {{?it.background && it.background.src}}
                    <img class="responsive-img background" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.background.src}}">
                    {{?}}

                    {{?it.grid}}<div class="{{?it.grid}}row center{{?}} no-padding"> {{?}}               
                        {{~it.images :image:idx}}
                        <a class="{{?it.grid}}col {{=it.grid}}{{?}} image-holder{{?image.pulse}} pulse{{?}}{{?image.bounce}} bounce{{?}}{{?it.background}} background{{?}}
                                  {{?image.modalID || image.popup}}modal-trigger{{?}}"
                                {{?it.background}}
                                    style="top:{{=image.top}}; left:{{=image.left}}; 
                                        {{?image.width}}width:{{=image.width}};{{?}} 
                                        {{?image.height}}height:{{=image.height}};{{?}}"
                                    {{?}}                           
                                    {{?image.popup}}href="#{{=image.popupID}}" data-modal="{{=image.popup}}"{{?}}
                                    {{?image.modalID}}href="#{{=image.modalID}}"{{?}}>
                            
                            {{?image.src}} <img class="responsive-img" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=image.src}}" alt="{{=image.alt}}"/> {{?}}
                        </a>
                        {{~}}
                        {{?it.grid}}</div>{{?}}
                    </img>
                 

                </div>
      
                
            </div>
        </div>
    </div>
</div>




<!-- ********************************** -->
<!-- *********  MODAL-POPUPS  ********* -->
<!-- ********************************** -->
{{?it.modals}}
{{~it.modals :modal:idx}}
    <div id="{{=modal.id}}" class="modal large">
        <div class="modal-content">
                <h4>{{=modal.header}}</h4>
                <span class="flow-text">{{=modal.text}}</span>
        </div>
        <div class="modal-footer">
            <a class="btn modal-close client-colors button2">
                <i class="small material-icons right">close</i>{{=modal.close}}
            </a>
        </div>
    </div>
{{~}}
{{?}}