
<div class="container"> 
    
    <div class="row"> 
        
        <!-- Breadcrumbs progress -->
        {{=it.components[0]}}
        
        <!-- Tabs header -->
        {{=it.components[1]}}
         
        <!-- Tabs panels -->
        {{for (var idx = 2; idx < it.components.length; idx++) {}}
            <div id="tabPanel{{=idx-2}}" class="col s12 tab-component">
                {{=it.components[idx]}}
            </div>
        {{};}}  

        
    </div>

</div>
    
