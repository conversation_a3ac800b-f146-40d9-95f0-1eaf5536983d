<div class="grid_CaseStudy">

    <!-- Tabs header -->
    {{=it.components[0]}}
        
    <!-- Chart -->
    <div id="tabPanelCase0" class="col s12 tab-component details">
        <!-- Info -->   {{=it.components[1]}}
        <!-- Button --> {{=it.components[2]}}
    </div>

    <!-- Roles -->
    <div id="tabPanelCase1" class="col s12 tab-component details">
        <!-- Info -->   {{=it.components[3]}}
        <!-- Button --> {{=it.components[4]}}
    </div>

    
    <!-- Rest of components-->
    {{for (var idx = 5; idx < it.components.length; idx++) {}}
        {{=it.components[idx]}}
    {{};}}  

</div>