<?xml version="1.0" encoding="utf-8" ?>
<Action layout="../../../layout/tabsLayoutModal">
  

  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R2_Roles_Header}",
      swipeable: false,
      tabPanelID: "tabPanelModal",
      tabs: [
          "!{SIM_R2_Roles_Tab2}"
      ]
  }]]></Component>

  <!-- Your Team  -->    
  <Component type="Roles" customJS="true"><![CDATA[{
    templateInEvent: "html/roles.dot",
    css: "styles/roles.css",
    _animate: "fadeIn",
    header: "!{} ",
    subheader: "!{}",
    instructions: "!{}",
    intro: "!{}",
    characters: [
      {
        var: "Q_My_Avatar",
        path: "images/roles/",
        name: "!{SIM_R1_Roles_Title0}"
      },
      {
        image: "!{SIM_R1_Roles_Img1}",
        name: "!{SIM_R1_Roles_Title1}"
      },
      {
        image: "!{SIM_R1_Roles_Img2}",
        name: "!{SIM_R1_Roles_Title2}"
      },
      {
        image: "!{SIM_R1_Roles_Img3}",
        name: "!{SIM_R1_Roles_Title3}"
      },
      {
        image: "!{SIM_R1_Roles_Img4}",
        name: "!{SIM_R1_Roles_Title4}"
      },
      {
        image: "!{SIM_R1_Roles_Img5}",
        name: "!{SIM_R1_Roles_Title5}"
      }
    ],
    "charactersInfo": [
      {
        "title": "!{SIM_R1_Roles_Title0}",
        "label": "!{}",
        "text": "!{SIM_R1_Roles_Text0}",
        "image": "",
        "var": {
          path: "images/roles/",
          src: "Q_My_Avatar",
          position: "left",
          size: "medium",
          embed:false
        }
      },
      {
        "title": "!{SIM_R1_Roles_Title1}",
        "text": "!{SIM_R1_Roles_Text1}",
        "image": { src: "!{SIM_R1_Roles_Img1}", position: "left", size: "medium", embed:false }
      },
      {
        "title": "!{SIM_R1_Roles_Title2}",
        "text": "!{SIM_R1_Roles_Text2}",
        "image": { src: "!{SIM_R1_Roles_Img2}", position: "left", size: "medium", embed:false }
      },
      {
        "title": "!{SIM_R1_Roles_Title3}",
        "text": "!{SIM_R1_Roles_Text3}",
        "image": { src: "!{SIM_R1_Roles_Img3}", position: "left", size: "medium", embed:false }
      },
      {
        "title": "!{SIM_R1_Roles_Title4}",
        "text": "!{SIM_R1_Roles_Text4}",
        "image": { src: "!{SIM_R1_Roles_Img4}", position: "left", size: "medium", embed:false }
      },
      {
        "title": "!{SIM_R1_Roles_Title5}",
        "text": "!{SIM_R1_Roles_Text5}",
        "image": { src: "!{SIM_R1_Roles_Img5}", position: "left", size: "medium", embed:false }
      }
    ],
    "isFollower": "Follower",
    "trackTeam": "Team",
    "scope": ["Q_My_Avatar", "Follower"]
  }]]></Component>

  <Component type="Vanilla"> 
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css"
    }]]>  
  </Component>



  <!-- Scroll to top button working within modals -->
  <!-- <Include name="ScrollToButton_menu"></Include> -->

</Action>