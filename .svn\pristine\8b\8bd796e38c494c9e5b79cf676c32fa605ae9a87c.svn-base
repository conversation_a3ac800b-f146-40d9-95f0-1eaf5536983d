﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min', './jquery.keyframes.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var RollingRoulette = function () {
        this.type = 'RollingRoulette';
        this.level = 1;
        
        this.rotationsTime = 7;
        this.wheelSpinTime = 5;
        this.ballSpinTime = 4;
        this.numorder = [ 0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26 ];
        this.numred = [ 32, 19, 21, 25, 34, 27, 36, 30, 23, 5, 16, 1, 14, 9, 18, 7, 12, 3 ];
        this.numblack = [ 15, 4, 2, 17, 6, 13, 11, 8, 10, 24, 33, 20, 31, 22, 29, 28, 35, 26 ];
        this.numgreen = [0];
        this.numberLoc = [];
    };

    RollingRoulette.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    RollingRoulette.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };

    RollingRoulette.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
                
            if (! options.context.find('.container').attr('hidden')) {

                self.createWheel();

                //Check if already played the Roulette
                self.getVote(options.wizletInfo.score).then(function(value) { 
                    if (value) {                  
                            
                        var questId = self.wizerApi.getQuestionIdByName(options.wizletInfo.score);
                        self.wizerApi.getMyVotes([questId]).then(function(response) {
                            var number = parseInt (response.votes[questId][0]);              
                            
                            self.setScore(number);                     
                            
                        });

                    } else {
                        
                         options.context.find('#playBtn').removeAttr('hidden');
                        
                    }
                });

                options.context.find('#playBtn').click(function(){
                    $(this).css('visibility', 'hidden');
                    //$(this).hide();
                    self.spinRoulette (options.wizletInfo.range_min, options.wizletInfo.range_max, undefined);

                });
            }
            

            return true;
        })
        .fail(this.wizerApi.showError);
    };

    /* Spin the wheel and get the random number */
    RollingRoulette.prototype.createWheel = function() {

        var self = this;

        var rinner = self.wizletContext.find("#rcircle");

        var temparc = 360 / this.numorder.length;
        for (var i = 0; i < this.numorder.length; i++) {
            this.numberLoc[this.numorder[i]] = [];
            this.numberLoc[this.numorder[i]][0] = i * temparc;
            this.numberLoc[this.numorder[i]][1] = i * temparc + temparc;
        
            newSlice = document.createElement("div");
            $(newSlice).addClass("hold");
            newHold = document.createElement("div");
            $(newHold).addClass("pie");
            newNumber = document.createElement("div");
            $(newNumber).addClass("num");
        
            newNumber.innerHTML = this.numorder[i];
            $(newSlice).attr("id", "rSlice" + i);
            $(newSlice).css(
                "transform",
                "rotate(" + this.numberLoc[this.numorder[i]][0] + "deg)"
            );
        
            $(newHold).css("transform", "rotate(9.73deg)");
            $(newHold).css("-webkit-transform", "rotate(9.73deg)");
        
            if ($.inArray(this.numorder[i], this.numgreen) > -1) {
            $(newHold).addClass("greenbg");
            } else if ($.inArray(this.numorder[i], this.numred) > -1) {
            $(newHold).addClass("redbg");
            } else if ($.inArray(this.numorder[i], this.numblack) > -1) {
            $(newHold).addClass("greybg");
            }
        
            $(newNumber).appendTo(newSlice);
            $(newHold).appendTo(newSlice);
            $(newSlice).appendTo(rinner);
        }
    }

    /* Spin the wheel and get the random number */
    RollingRoulette.prototype.spinRoulette = function(rangeMin,rangeMax,value) {
        
        var self = this;

        var numbg = self.wizletContext.find(".pieContainer");
        var ballbg = self.wizletContext.find(".ball");
        var toppart = self.wizletContext.find("#toppart");


        function resetAni() {
            animationPlayState = "animation-play-state";
            playStateRunning = "running";
          
            $(ballbg)
              .css(animationPlayState, playStateRunning)
              .css("animation", "none");
          
            $(numbg)
              .css(animationPlayState, playStateRunning)
              .css("animation", "none");
            $(toppart)
              .css(animationPlayState, playStateRunning)
              .css("animation", "none");
          
            $("#rotate2").html("");
            $("#rotate").html("");
        }
        
        function ballrotateTo(deg) {
            var temptime = self.rotationsTime + 's';
            var dest = -360 * self.ballSpinTime - (360 - deg);
            $.keyframe.define({
            name: "rotate2",
            from: {
                transform: "rotate(0deg)"
            },
            to: {
                transform: "rotate(" + dest + "deg)"
            }
            });
        
            $(ballbg).playKeyframe({
                name: "rotate2", // name of the keyframe you want to bind to the selected element
                duration: temptime, // [optional, default: 0, in ms] how long you want it to last in milliseconds
                timingFunction: "ease-in-out", // [optional, default: ease] specifies the speed curve of the animation
                complete: function() {
                    if (!value) self.showScore(number);
                } //[optional]  Function fired after the animation is complete. If repeat is infinite, the function will be fired every time the animation is restarted.
            });
        }

        
        function ballrotateTo__(deg) {
            
            var temptime = self.rotationsTime * 1000;
            var dest = -360 * self.ballSpinTime - (360 - deg);

            $(ballbg)[0].animate([
                // keyframes
                { transform: 'rotate(0deg)' },
                { transform: 'rotate(' + dest + 'deg)' }
              ], {
                // timing options
                duration: temptime,
                iterations: 1
              });
        }
        
        function bgrotateTo(deg) {
            var dest = 360 * self.wheelSpinTime + deg;
            var temptime = (self.rotationsTime * 1000 - 1000) / 1000 + 's';
        
            $.keyframe.define({
                name: "rotate",
                from: {
                    transform: "rotate(0deg)"
                },
                to: {
                    transform: "rotate(" + dest + "deg)"
                }
            });
        
            $(numbg).playKeyframe({
                name: "rotate", // name of the keyframe you want to bind to the selected element
                duration: temptime, // [optional, default: 0, in ms] how long you want it to last in milliseconds
                timingFunction: "ease-in-out", // [optional, default: ease] specifies the speed curve of the animation
                complete: function() {} //[optional]  Function fired after the animation is complete. If repeat is infinite, the function will be fired every time the animation is restarted.
            });
        
            $(toppart).playKeyframe({
                name: "rotate", // name of the keyframe you want to bind to the selected element
                duration: temptime, // [optional, default: 0, in ms] how long you want it to last in milliseconds
                timingFunction: "ease-in-out", // [optional, default: ease] specifies the speed curve of the animation
                complete: function() {} //[optional]  Function fired after the animation is complete. If repeat is infinite, the function will be fired every time the animation is restarted.
            });
        }

        function bgrotateTo__(deg) {
            var dest = 360 * self.wheelSpinTime + deg;
            var temptime = (self.rotationsTime * 1000 - 1000) ;
            
            $(numbg)[0].animate([
                // keyframes
                { transform: 'rotate(0deg)' },
                { transform: 'rotate(' + dest + 'deg)' }
              ], {
                // timing options
                duration: temptime,
                iterations: 1
              });
            $(toppart)[0].animate([
                // keyframes
                { transform: 'rotate(0deg)' },
                { transform: 'rotate(' + dest + 'deg)' }
            ], {
                // timing options
                duration: temptime,
                iterations: 1
            });
        }
        

        function getRandomNumber(min, max) {
            console.log(min,max)
            min = Math.ceil(min);
            max = Math.floor(max);
            return Math.floor(Math.random() * (max - min + 1)) + min;
          }

        var number;
        if (value) {
            number = value;
            this.rotationsTime = 3;
            this.wheelSpinTime = 2;
            this.ballSpinTime = 1;
        } else {           
            number = getRandomNumber(rangeMin, rangeMax); 
            self.addVote(self.wizletInfo.score, number)
        }

        var temp = this.numberLoc[number][0] + 4;
        var rndSpace = Math.floor(Math.random() * 360 + 1);

        resetAni();
        setTimeout(function() {
            bgrotateTo(rndSpace);
            ballrotateTo(rndSpace + temp);
        }, 500);


        console.log('Extra score: ',number)

    };
      
         

    // Show score in the roulette
    RollingRoulette.prototype.setScore = function (number) {
        var self = this;

        self.spinRoulette (0, 0, number);

        self.showScore(number);

    };



    RollingRoulette.prototype.showScore = function (number) {
        var self = this;
        
        self.wizletContext.find('#scoreBox').removeAttr('hidden').find('span.points').html(number);
        if (number>1) {
            var post = self.wizletContext.find('#scoreBox span.post');
            post.html(post.html()+'s');
        }
        if (self.wizletInfo.idToShow) {
            $('#'+self.wizletInfo.idToShow).removeAttr('hidden');
        }
    };

    
    RollingRoulette.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };

    RollingRoulette.prototype.addVotes = function (questionNames, values) {
        var self = this; 

        var myVotes = [];
        questionNames.forEach(function(q,pos) { 
            myVotes.push( { 
                questionId: self.wizerApi.getQuestionIdByName(q), 
                responseText: values[pos] } ); 
        });
        self.wizerApi.addVotes({ votes: myVotes });
    };
    
    /**
     * Promise function: Get vote value from a questionName
     */
    RollingRoulette.prototype.getVote = function (questionName) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = new Q.defer();

        var waiting =  self.wizerApi.getMyVotes([questionId]);

        waiting.then(function (result) {            
            defer.resolve(result.votes[questionId][0]);
        });          
            
        
        return defer.promise;
    };

    RollingRoulette.getRegistration = function () {
        return new RollingRoulette();
    };

    return RollingRoulette;

});