@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";



// .tab-component .wizlet.wizletRadioButtons > .container {
//     margin: 0;
//     width: initial;
// }


.wizlet.wizletRadioButtons { 
    
    @include header-help();
    

    .container {
        
        &.borders {
            @include card-border(10px,double); 
        }
    }
    
    @include header-badge;

    .row.same-heigh-columns {
        @include side-image;
        
        .card-panel {
            margin-top: 0;
            margin-bottom: 0;
            padding-top: 12px;
            padding-bottom: 12px;
        }
    }

    .card {
        .card-content {
            background-color: color("client-colors", "white");

            .row {
                margin: 0;
            }
            //@include header-badge;
            .header {
                color: color("client-colors", "font");
                font-size: 125%;
                margin-top: 0;
            }
            
            @include card-title;
            .embed {
                clear: both;
                width: 40%;
                @media #{$small-and-down} {
                    width: 100%;
                }
                // @media #{$large-and-up} {
                //     width: 50%;
                // }
                &.left {
                    float: left;
                    margin-right: 12px;
                }
                &.right {
                    float: right;
                    margin-left: 12px;
                }
                &.xtiny {
                    width: 10%;
                }
                &.tiny {
                    width: 20%;
                }
                &.small {
                    width: 30%;
                }
                &.large {
                    width: 50%;
                }
                &.extralarge {
                    width: 70%;
                }
            }
        }
    }
    
    form {
    
        &>ul{
            &>li{
                $color: color("client-colors", "button");
                $color-off: color("client-colors","grey");
                $color-on: color("client-colors", "border");

                &:not(:last-child) {
                    border-bottom: 1px solid color("client-colors", "border");
                }
                padding: 10px 5px;
                margin: 0;
                
                @include no-select-highlight;
      
                label {
                    color: color("client-colors","font");

                    .question-label {
                        color: color("client-colors","font2");
                        position: absolute;
                        left: 0;
                        top: 0;
                        transform: translateX(-75%) translateY(-25%);
                        padding: 0 2px;
                        min-width: 2rem;
                        background-color: $color;
                        
                        @media #{$small-and-down} { 
                            transform: translateX(-110%) translateY(-25%);
                            min-width: 1.3rem;
                        }
                    }  
                }

                &.chosen {                    
                    .hoverable {
                        box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
                    }
                    
                    label .question-label {
                        background-color: $color-on;
                    }
                }

                &.turnedoff { 
                    opacity: 0.8;
                    label, label span.title {
                        color: $color-off !important;
                    }
                    .question-label {
                        background-color: $color;
                    }
                }

                &.disabled {
                    pointer-events: none;                
                    ul.collapsible {
                        pointer-events: initial;
                    }

                    &:not(.chosen) {
                        opacity: 0.8;
                        label, label span.title {
                            color: $color-off;
                        }
                        label span.title {
                            &::before {
                                border-color: $color-off;
                            }
                        }
                        &[data-correct="true"] {
                            filter: drop-shadow(2px 4px 6px black);
                            opacity: 0.9;
                            label {
                                color: color("client-colors","dark-grey");
                            }
                        }
                        .question-label {
                            // background-color: $color;
                            background-color: $color-off;
                        }
                    }
                    &.checked {
                        label span.title {
                            &::after {
                                border-color: $color-off;
                                background: $color-off;
                            }
                        }

                    }
                }

                &:not(.disabled) {
                    cursor: pointer;
                    &:hover {
                        label{
                            color: $color-on;
                        }   
                            
                        [type="radio"] + span:before {
                            border-color: $color-on;
                        }
                    }
                }       
                

                &.no-separator {
                    border-bottom: none;
                }
                &.reduced {
                    padding-top: 2px;
                    padding-bottom: 0px;

                    .card-panel {
                        padding: 15px;
                        
                        span.question-label {
                            transform: translateX(-85%) translateY(-25%);
                            min-width: 1.5rem;
                        }

                        span.title {
                            font-weight: normal;
                        }
                    }
                }



                [type="radio"] {
                    + span {
                        font-weight: bold;
                        font-size: 1.3rem;
                        height: auto;
                        padding-left: 50px;
                        
                        &:before {
                            border: 5px solid $color;
                            height: 32px;
                            width: 32px;
                            top: 50%;
                            transform: translateY(-65%);
                        }
                        &:after {
                            height: 36px;
                            width: 36px;
                            margin-top: -7px;
                            margin-left: 2px;
                            top: 50%;
                            transform: scale(0.5) translateY(-65%);
                            border: none;
                        }
                        
                        @media #{$small-and-down} { 
                            padding-left: 40px; 
                            &:before {
                                // display: none;
                                height: 24px;
                                width: 24px;
                            }
                            &:after {
                                // display: none;
                                height: 20px;
                                width: 20px;
                                margin-top: -3px;
                                margin-left: 6px;
                            }         
                        }
                    }
                    &:checked + span {
                        // & {
                        //     color: $color-on;
                        // }
                        &:before {
                            border-color: $color-on;
                        }
                        &:after {
                            border-color: $color-on;
                            background: $color-on;
                        }
                    }

                    &:disabled + span:before {
                        border-color: inherit;
                    }
                }
                
    
                .card-panel {
                    position: relative;

                    @media #{$small-and-down} { 
                        padding: 12px;
                    }
                    
                    span.title {
                        width: 100%;
                        >div.col {
                            padding: 0;
                        }
                        @media #{$small-and-down} { 
                            .col.image {
                                display: none;
                            }
                            .col.option {
                                width: 100%;
                            }
                        }

                        &.hide-radio {
                            padding-left: 0px;
                            &:before,
                            &:after {
                                display: none;
                            }
                        }
                    }

                    i.check-icon {
                        position: absolute;
                        top: 0;
                        z-index: 1;
    
                        &.left {
                            left: 0;
                        }
                        &.right {
                            right: 0;
                        }
                        &.scaled-out {
                            transform: scale(0);
                        }
                        
                    }

                }
      

                ul.collapsible {
                    >li {
                        background-color: color("client-colors", "white") ;

                        .collapsible-header {
                            font-size: 90%;
                            border: none;
                            padding: 0.5rem;
                        }
                        .collapsible-body {
                            // font-size: 95%;
                            border: none;
                            padding: 0 1rem 1rem 1rem;
                        }
                    }
                }
      
            }

            
            &.inline>li{
                @media #{$medium-and-up} { 

                    &:not(:last-child) {
                        border-bottom: none;
                    }
                    
                    [type="radio"] {
                        + span {
                            font-size: 1.25em;
                            padding-left: 40px;
                        }
                    }
                    
                    &.big [type="radio"] {
                        + span {
                            font-size: 200%;
                            padding-left: 50px;
                        }
                    }
                    
                    &.small {
                        .card-panel {
                            padding: 12px;
                        }
                        [type="radio"] {
                            + span {
                                // font-size: 100%;
                                i { font-size: 3rem; }
                            }
                        }
                    }
                }
                @media #{$small-and-down} {                     
                    &.col {
                        width: 100%
                    }
                }
                
                span.title, 
                span.title > div {
                    display: flex;
                    align-items: center;
                }
            }
        }
    }


    .countDownTimerHolder {
        padding: 5px 10px;
        min-width: 150px;
        background-color: color("client-colors", "primary");
        // border-top-right-radius: 15px;
        text-align: center;
        
        &.big {
          padding: 50px 20px 20px 20px;
          border-radius: 15px;
          .countDownTimer {
            font-size: 100px;
          }
        }
        
        &.center {
          margin: 40vh 0 0 30vw;
          width: 30vw;
        }
        
        .countDownTimer {
          font-size: 20px;
          font-weight: bold;
          background-color: transparent !important;
        }
      }
    
    
      .row.timer {

        clear:both;
        .countDownTimerHolder {

            &.finished {      
                &.big {
                    padding-bottom: 30px;
                    .countDownTimer {
                    font-size: 50px;
                    }
                }                
                &.center {
                    margin: 40vh 0 0 5vw;
                    width: 90vw;
                }      
            }

            &.disabled {
              background-color: color("client-colors", "secondary");
            }
        }
        

      }


    @include row-submit;
    #checkBtn {
        margin-left: 15px;
    }

    #resultsBtn[href] {
        color: color("client-colors","font2");
    }

}
  