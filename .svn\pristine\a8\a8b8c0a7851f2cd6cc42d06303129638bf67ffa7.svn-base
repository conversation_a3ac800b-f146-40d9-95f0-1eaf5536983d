﻿
<div class="collection-menu">

    {{?it.with<PERSON>ontainer}}
    <div class="container">
        <div class="row">
            <div class="col s12">
    {{?}}  
    
        {{?it.header}}<h4 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.header}}</h4>{{?}}
            
        <div class="card-panel client-colors {{=it.bgColor}}">
    
            {{?it.intro}}<span class="intro white-text">{{=it.intro}}</span>{{?}}
    
            {{?it.image}}
            <div class="card-image">
                <img class="{{?it.image.src_vert}}hide-on-med-and-down{{?}} responsive-img {{?it.image.zoom}}materialboxed{{?}}" 
                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.image.src}}" alt="{{=it.image.alt}}"/>
    
                {{?it.image.src_vert}}
                <img class="hide-on-large-only responsive-img {{?it.image.zoom}}materialboxed{{?}}" 
                    src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.image.src_vert}}" alt="{{=it.image.alt}}"/>
                {{?}}
                
            </div>
            {{?}}
            
            <ul class="collection">
                {{~it.items: option:idx}}
                    {{?option.image}}
                    <li class="collection-item avatar">
                        <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=option.image}}" alt="" class="circle">
                        <span class="title">{{=option.label}}</span>
                        <p >{{=option.desc}}</p>
                    {{??}}
                    <li class="collection-item"
                        {{?option.hide}}
                            {{?option.hide_condition && it.DB[option.hide_condition] }}
                                {{?option.hides[it.DB[option.hide_condition]]}}hidden{{?}}
                            {{??}}
                                hidden
                            {{?}}
                        {{?}}>
                    {{?}}
                    
                        <a class="{{?option.image}}secondary-content{{?}} {{?option.tooltip}}tooltipped tmenu{{?}} {{?it.DB[option.visited]==1}}visited{{?}}" 
    
    
                        {{?option.targetSection}}
                            {{?option.jump_condition && it.DB[option.jump_condition] && option.jumps[it.DB[option.jump_condition]]}}
                                data-action="{{=option.jumps[it.DB[option.jump_condition]]}}"
                            {{??}}
                                data-action="{{=option.targetSection}}"
                            {{?}}
                            {{?option.gdActionCommand}}data-gdcommand="{{=option.gdActionCommand}}" data-gdcommandxml="{{=option.gdActionCommandXML}}"{{?}}
                            {{?option.gdActionEmbed}}data-gdembed="{{=option.gdActionEmbed}}" data-index="{{=idx}}"{{?}}
                            {{?option.gdActionSection}}data-gdaction="{{=option.gdActionSection}}"{{?}}
                            {{?option.gdActionTrack}}data-gdtrack="{{=option.gdActionTrack}}"{{?}}
                        {{?}}
    
                        {{?option.tooltip}}data-tooltip="{{=option.tooltip}}"{{?}}
                        
                        {{?option.link}}                    
                            {{?option.link_condition && it.DB[option.link_condition] && option.links[it.DB[option.link_condition]]}}
                                href="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=option.links[it.DB[option.link_condition]]}}"
                            {{??}}
                                href="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=option.link}}"
                            {{?}}
                        {{?}} 
                        {{?option.download}}download{{??}}target="_blank"{{?}}>
    
                            {{?!option.image}}
                            <span class="word-wrap">{{=option.label}}</span>
                            {{?}}
                            <i class="material-icons right">{{?it.DB[option.visited]==1}}check_box{{??}}{{=option.icon}}{{?}}</i>
                        </a>
                    
                    </li>
                    
                    
                    {{?option.gdActionEmbed}}
                    <div class="embeddingWrapper" id="embeded-{{=idx}}" data-embed></div>
                    {{?}}
                {{~}}
            </ul>
    
        </div>
    
    {{?it.withContainer}}</div></div></div>{{?}}
        
    </div>
    