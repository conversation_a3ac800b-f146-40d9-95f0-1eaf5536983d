@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";

.modal-content .tab-component .wizlet.wizletLottie>.container {
    .card {
        width: 97%;
    }
}

.wizlet.wizletLottie {
    .container {
        &.borders {
            @include card-border(10px,double);
        }
    }
    .card {
        &.no-header {
            margin-top: -20px;
            margin-bottom: 4rem;
        }
        &.transparent {
            background: transparent;
            border: none;
            box-shadow: none;
            .card-image {
                background: transparent;
            }
        }
        &.horizontal {
            .card-content {
                width: 100%;
            }
        }
        .card-image {
            padding: 24px 0px;
        }
        &.up .card-image {
            padding-right: 24px;
        }
        &.down .card-image {
            padding-left: 24px;
        }
    }
    .card-image {
        min-width: 40%;
        max-width: 60%;
        @media #{$large-and-up} {
            min-width: 30%;
            max-width: 40%;
        }
        &.small {
            width: 40%;
            @media #{$large-and-up} {
                width: 30%;
            }
        }
        &.full-width {
            max-width: 100%;
        }
        display: block;
        // lottie { margin: auto; }
        
        &.borders {
            @include card-border(2px,solid,""); 
        }
    }
    .card-image,
    .card-content {
        background-color: color("client-colors", "white");
    }
    .card-content {
        @include card-title;
        p {
            // text-align: justify;
            &:not(.flow-text) {
                font-size: inherit;
            }
        }
        @media #{$medium-and-down} {
            padding: 12px;
        }
        &.centered {
            //width: 100%;
            text-align: center;
            span, p {
                text-align: center;
            }
        }
        &.borders {
            @include card-border(10px,outset,""); 
        }
        &.onlyImage {
            padding: 0;
        }
        .embed {
            clear: both;
            width: 40%;
            @media #{$small-and-down} {
                width: 100%;
            }
            // @media #{$large-and-up} {
            //     width: 50%;
            // }
            &.left {
                float: left;
                margin-right: 12px;
            }
            &.right {
                float: right;
                margin-left: 12px;
            }
            &.xtiny {
                width: 10%;
            }
            &.tiny {
                width: 20%;
            }
            &.small {
                width: 30%;
            }
            &.large {
                width: 50%;
            }
            &.extralarge {
                width: 70%;
            }
        }

        .body {
            display: table;
        }
    }
}