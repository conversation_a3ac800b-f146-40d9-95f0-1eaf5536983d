﻿
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', './api/wizer-api-extended', 'evaluator', '../styles/materialize-src/js/materialize.min'], function($, Q, WizerApi, WizletBase, doT, wizerApiExt, Evaluator) {

    var SubmitButton = function() {
        this.type = 'SubmitButton';
        this.level = 1;
    };

    SubmitButton.prototype.loadHandler = function(unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.wizerApiExt = wizerApiExt.getRegistration(wizerApi);

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        if (wizletInfo.templateInEvent) {
            requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        }
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function(doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    SubmitButton.prototype.unloadHandler = function() {
        $(document).off("wizer:model:change", this.evaluation_event);
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
    };

    
    SubmitButton.prototype.evaluation = function () {
        var self = this;
            
        var isEnabled = Evaluator.parseAndEval(self.wizletInfo.enable_eval, self.wizletInfo);
        
        if (isEnabled) {
            // console.log('** Navigation Enabled **')
            self.wizletContext.find('#'+self.id+':not([disabled])').removeAttr('hidden invisible');
        } else {
            // console.log('** Navigation Not-Enabled **')
            self.wizletContext.find('#'+self.id+':not([disabled])').attr('invisible',true);
        }
    }
    SubmitButton.prototype.evaluation_event = function (event) {
        var self = event.data.self;
        
            //Load the updated values in all the scoped variables
            var questionIds = [];
            $.each(self.wizletInfo.scope, function (idx, condition) {
                questionIds.push( self.wizerApi.getQuestionIdByName(condition) );
            });

            self.wizerApi.getMyVotes(questionIds).then(function (response) { 
                
                $.each(questionIds, function (idx, questionId) {

                    if ( response.votes[questionId] && response.votes[questionId][0] ) {

                        self.wizletInfo.DB[ self.wizletInfo.scope[idx] ] = response.votes[questionId][0];
    
                        //Check if the evaluation enables with the new updated values
                        var isEnabled = Evaluator.parseAndEval(self.wizletInfo.enable_eval, self.wizletInfo);
                        if (isEnabled) {
                            // console.log('** Navigation Enabled **')
                            self.wizletContext.find('#'+self.id+':not([disabled])').removeAttr('hidden invisible');
                        } else {
                            // console.log('** Navigation Not-Enabled **')
                            self.wizletContext.find('#'+self.id+':not([disabled])').attr('invisible',true);
                        }
    
                    }
                });

            });  

    }

    SubmitButton.prototype.render = function(options) {
        var self = this;
        return self.templateDefer.promise.then(function(template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);

            self.id = options.wizletInfo.id ? options.wizletInfo.id : '';

            //Init Materialize Modal
            if (options.wizletInfo.modal)
                options.context.find('#modal-submit').modal();


            self.validate().then(function (submitted) {

                if (submitted) {
                                  
                    $(document).one('wizer:action:init', function(e, currentAction) {
                        doSubmit();
                    });

                } else {

                    if (options.wizletInfo.enable_eval) {
                        self.evaluation();
                        $(document).on("wizer:model:change."+self.id, {self: self}, self.evaluation_event);
                    }
                    //Show the hidden navigation button after a delay
                    if (options.wizletInfo.showDelay)
                        setTimeout(
                            function() { 
                                options.context.find('#'+options.wizletInfo.id+':not([disabled])').removeAttr('hidden invisible');
                            }, 
                            options.wizletInfo.showDelay
                        );
                }

            });
                    

            if (options.wizletInfo.modal) {
            
                options.context.find("#modal-submit a.btn.modal-confirm").off("click").one("click", function (e) {
                    
                    $(document).off("wizer:model:change", self.evaluation_event);
    
                    $('.saveInModelLater').trigger('updateModelInput');
    
                    if (self.wizletInfo.submitted)
                        self.addVote( self.wizletInfo.submitted,  1);
                    
                    if (self.wizletInfo.updateGauges)   
                        $('span.gauge-blocked').removeClass('gauge-blocked').trigger('updateModelSpan')

                    doSubmit();                 
                    self.closeModal();
                    
                });
                options.context.find("#modal-submit a.btn modal-close").on("click", function (e) {                    
                    self.closeModal();
                });

            } else {
            
                options.context.find(".submit > a").off("click").one("click", function (e) {
                    
                    $(document).off("wizer:model:change", self.evaluation_event);
    
                    $('.saveInModelLater').trigger('updateModelInput');
    
                    if (self.wizletInfo.submitted)
                        self.addVote( self.wizletInfo.submitted,  1);
                       
    
                    doSubmit();
                    
                });

            }



            var doSubmit = function() {       
                
                var defer = new Q.defer();

                if(options.wizletInfo.updateModel){

                    defer.promise = self.wizerApi.calcBinderCache.setCalcValue(options.wizletInfo.updateModel.model, options.wizletInfo.updateModel.input, options.wizletInfo.updateModel.inputval).then(function () {
                        console.log(options.wizletInfo.updateModel.input, "updated to "+options.wizletInfo.updateModel.inputval);
                    });
                } else {
                    defer.resolve();
                }
                
                if (options.wizletInfo.updateFromModel && options.wizletInfo.updateFromModel.getquestions.length>0) {                         
                
                    defer.promise.then(function (response) {  
                        setTimeout(
                            function() { 
                                self.getModelVotes (options.wizletInfo.updateFromModel.model, options.wizletInfo.updateFromModel.getquestions).then(function(modelVotes){
            
                                    self.wizerApi.addVotes({votes:modelVotes}).then(function (result2) {   
                                        // console.log(modelVotes);
                                        console.log('*All votes from model included*');
                                    }); 
                                });
                            }, 
                            1000
                        );  
                    });  
                }

                if (self.wizletInfo.doSubmit.ranges) {
                    $('.wizletRange .range').addClass('disabled');
                    $('.wizletRange .gauge.total').addClass('disabled');
                }
                if (self.wizletInfo.doSubmit.dropdowns) $('.wizletDropDownButton .btn').addClass('disabled');
                if (self.wizletInfo.doSubmit.tableinputs) $('.wizletTableInputs .input-field').addClass('disabled');
                if (self.wizletInfo.doSubmit.inputs) $('.wizletRadioButtons form > ul > li').addClass('disabled');

                //Check, count and save total correct answers
                if (self.wizletInfo.doSubmit.inputs && self.wizletInfo.checkCorrect) {
                    $('.wizletRadioButtons form li.chosen i.check-icon').addClass('scale-in').removeClass('scaled-out');
                    var corrects = 0;
                    $.each( $('.wizletRadioButtons form > ul > li.chosen'), function (idx, option) {
                        if ( $(option).find('input[type="radio"]').data('correct') ) corrects ++;
                    });
                    console.log("Correct answers: ", corrects);
                    if (self.wizletInfo.score)
                        self.addVote( self.wizletInfo.score, corrects);

                        
                    //ALL CORRECT
                    if (self.wizletInfo.idsToShowIfCorrect && self.wizletInfo.scoreToBeCorrect==corrects) {
                        $(self.wizletInfo.idsToShowIfCorrect).removeAttr('hidden').find('a').removeAttr('disabled');

                        if (self.wizletInfo.ibider) 
                            self.sendDataToIbider(
                                self.wizletInfo.ibider.host,
                                self.wizletInfo.ibider.service,
                                self.wizletInfo.ibider.stringifyData,
                                self.wizletInfo.ibider.activityId,
                                self.wizletInfo.ibider.status);
                        
                        if (self.wizletInfo.postMessage) {
                            console.log('sending postMessage')
                            window.parent.postMessage(self.wizletInfo.postMessage,'*');
                        }

                    }
            
                    //INCORRECT
                    if (self.wizletInfo.idsToShowIfIncorrect && self.wizletInfo.scoreToBeCorrect!=corrects) 
                        $(self.wizletInfo.idsToShowIfIncorrect).removeAttr('hidden').find('a').removeAttr('disabled');   
                        
                  
                    //automatically scroll to the bottom of the page after confirm
                    if (self.wizletInfo.scrollToDown) {
                        $('HTML, BODY').animate({ scrollTop: $('#wizer').height() }, 1000);
                    }

                }

                $('#'+self.id).attr('hidden',true);
                if (self.wizletInfo.idToShow) {
                    $(self.wizletInfo.idToShow).attr('invisible',true).removeAttr('hidden').find('a').removeAttr('disabled');
                    setTimeout(
                        function() { 
                            $(self.wizletInfo.idToShow).removeAttr('invisible');
                        }, 
                        self.wizletInfo.idToShowDelay ? self.wizletInfo.idToShowDelay : 0
                    );
                }
            };
                

            return true;
        })
        .fail(this.wizerApi.showError)
    };

    
    SubmitButton.prototype.getModelVotes = function(model, questions) {
        var self = this;

        var defer = new Q.defer();   

        var votes = [];
        var counter = 1;
     
        questions.forEach(function(question, idx) {
            
            //self.wizerApi.calcBinderCache.getCalcValue(model, question).then(function(value){
            self.wizerApiExt.getCalcValue(model, question).then(function(value){
            
                console.log(counter,question,value)
                votes.push({
                    questionId: self.wizerApi.getQuestionIdByName(question), 
                    responseText: value                
                })

                if (counter == questions.length) {
                    defer.resolve(votes); //when all model votes loaded
                } else {
                    counter ++;
                }
            })
        });

        return defer.promise;
        
    }



    SubmitButton.prototype.sendDataToIbider = function (host, service, stringifyData, activityId, status) {

        var self = this;
                
        if (!host || (host && (location.hostname == host))) {

            var email = Wizer.ParticipantEmail;

            var dataObj = { 
                email: email,
                userEmail: email,
                team_role: email
            };

            var header={};

            if (status) dataObj.status = status;
            if (activityId) dataObj.activityId = activityId;

            if (stringifyData) dataObj = JSON.stringify(dataObj);
                            
            $.ajax({
                type: "POST",
                data: dataObj,
                url: service,
                headers: header,
                success: function (data) {
                    //var req = JSON.parse(data);
                    console.log (data.data.status);
                },
                error: function (request, status, error) {
                    if (request.readyState == 4) {
                        switch (request.status) {
                            case 200:                                    
                                console.log(request.responseText);
                                var req = JSON.parse(request.responseText);
                                console.log ("Problem sending data to iBider: " + req.data.message);
                                break;

                            case 404:
                                console.log ("** Service not found ** ");                                        
                                break;

                            case 500:
                                console.log ("** Server error ** ");                                        
                                break;
                        
                            default:
                                console.log ("** Communication error ** ");   
                                break;
                        }

                    } else {                            
                        console.log ("** Server not ready ** ");
                    }
                }
            });     


        }

    }

    

    /* Validate if already submitted when loads the screen */
    SubmitButton.prototype.validate = function () {
        var self = this;
        var validating = new Q.defer();
        
        //The follower listens the model to load the leader's selected option and enable the navigation button
        if (self.wizletInfo.submitted) {
            var questionID = self.wizerApi.getQuestionIdByName(self.wizletInfo.submitted)
            self.wizerApi.getMyVotes([questionID]).then(function (response) {   
                if (response.votes[questionID] && response.votes[questionID][0]) {
                    validating.resolve(true);
                } else {
                    validating.resolve(false);
                }                
            });

        } else {
            validating.resolve(false);
        };

        return validating.promise;
    };
    

    SubmitButton.prototype.closeModal = function () {
        var self = this;
        // $('.modal-overlay').hide();
        // $('.modal').hide();       
        self.wizletContext.find('#modal-submit').modal('close'); 
    };

    /**
     * Promise function: Addvote value to a questionName
     */
    SubmitButton.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = new Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };


    SubmitButton.prototype.removeVotes = function (questionName) {
        var self = this; 
        var myVotes = [];
        questionName.forEach(function(q) { 
            myVotes.push( { questionId: self.wizerApi.getQuestionIdByName(q) } ); 
        });        
        self.wizerApi.removeVotes({ votes: myVotes });
    };

    SubmitButton.getRegistration = function() {
        return new SubmitButton();
    };

    return SubmitButton;

});