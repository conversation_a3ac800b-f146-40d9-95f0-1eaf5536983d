@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";


.bubbles-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;

  $bubble-color: color("client-colors", "white");
  //$bubble-color: color("client-colors", "border");

  .bubble {
    @include border-radius(50%);
    @include box-shadow2(0 20px 30px rgba(0, 0, 0, 0.2), inset 0px 10px 30px 5px rgba($bubble-color, 1));

    position: absolute;
    $bubble-size: 200px;
      height: $bubble-size;
      width: $bubble-size;

    &.doubleBubble {
      &:after {
        @include radial-gradient-stops3(ellipse, rgba($bubble-color, 0.5), 0%, rgba($bubble-color, 0), 70%, rgba($bubble-color, 0), 100%);
        @include border-radius(50%);
        @include box-shadow(inset 0 20px 30px rgba($bubble-color, 0.3));
      
        content: "";
        $bubble2-size: 180px;
          height: $bubble2-size;
          width: $bubble2-size;
        left: 10px; top: 10px;
        position: absolute;
      }
    }


    @include animation-name(animateBubble, sideWays);
    @include animation-iteration-count(infinite, infinite);
    @include animation-timing-function(linear, ease-in-out);
    @include animation-direction(normal, alternate);

    $pieces:12;
    @for $i from 0 through $pieces {

      &.x#{$i} {
        $duration1: random(10)+20;
        $duration2: random(3)+1;
        $scale: random(6)+2;
        $left: ($i*10)-10;
        @if $left > 110 {
          $left: $left % $pieces;
        }
        $top: random(9)-1;

        @include animation-duration(#{$duration1}s, #{$duration2}s);    
        @include transform(scale(#{$scale/10}));
    
        left: #{$left * 1%};
        top: #{$top * 10%};    
      }
    }

  }



  @include keyframes (animateBubble) {
    0% {
        margin-top: 100%;
    }
    100% {
        margin-top: -100%;
    }
  }
  @include keyframes (sideWays) {
    0% { 
        margin-left:0px;
    }
    100% { 
        margin-left:50px;
    }
  }


}

