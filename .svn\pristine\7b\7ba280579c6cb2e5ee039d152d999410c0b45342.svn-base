<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutIntro_SIM">
  <Include name="Header_Intro"></Include>

  <Component type="ActionsLoader" customJS="true"><![CDATA[{
      templateInEvent:"html/actionsLoader.dot",
      css: "styles/actionsLoader.css",
      hiddenSections: true,
      nextSections: [
          { actionXML:"SIM_R1_CaseStudy1", instructions:"!{SIM_R1_CaseStudy1_Instructions}" },
          { actionXML:"SIM_R1_CaseStudy2", instructions:"!{SIM_R1_CaseStudy2_Instructions}" },
          { actionXML:"SIM_R1_CaseStudy3", instructions:"!{}" }
      ]
  }]]></Component>


  
  <Voting autoNext="false">
    <Score type="Vote" result="Q_CurrentScreen" response="!{SIM_R1_CaseStudy}"/> 
  </Voting>


</Action>