﻿/* 
    "HCPieChart" component that takes a template and some data as the input
    and renders the chart using highcharts library.

*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'highcharts-styled', 'css!lib/highcharts/code/css/highcharts.css', 'numeral', 'jsCalcLib/numberFormatting'], 
        function ($, Q, WizerApi, WizletBase, doT, Highcharts,  HighchartsCss, numeral, numberFormatting) {

    var HCPieChartModel = function () {
        this.type = 'HCPieChartModel';
        this.level = 1;
        
        this.chartDefaults = {
            "lang": {
                "thousandsSep": ",",
                "numericSymbols": ['k', 'm', 'b', 't']
            },
            "chart": {
                "type": 'pie'
            },
            "credits": {
                "enabled": false
            },
            "legend":{ },
            "title": {
                "text": ''
            },
           
            "tooltip": {
                "headerFormat": '<b>{point.x}</b><br/>',
                "pointFormat": '{series.name}: {point.y}'
            },
            "plotOptions": {
                "pie": {
                    "dataLabels": {
                        "enabled": true
                    }
                }
            }
        }

    };

    HCPieChartModel.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        

        this.templateDefer = new Q.defer();
        var self = this;
        var requirements = [];
        requirements.push(WizletBase.loadTemplate(wizletInfo, 'HCPieChartModel.dot'));

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    HCPieChartModel.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
        $(document).off("wizer:model:change", this.redrawChart);
    };

    HCPieChartModel.prototype.render = function (options) {
        var self = this;
		 var fetching = new Q.defer();
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
			self.isDelivery = false;
            
            if (!self.wizletInfo.id) self.wizletInfo.id = 'pieChart0';
                        
            // start rendering chart
            self.renderChart().then(function () {
                fetching.resolve(true);   
                $(document).off("wizer:model:change."+self.wizletInfo.id, self.redrawChart).
                            on("wizer:model:change."+self.wizletInfo.id, {self: self}, self.redrawChart);             
            });
        })
        .fail(this.wizerApi.showError);
    };

    HCPieChartModel.prototype.renderChart = function() {
        var self = this;
        var rendering = new Q.defer();
        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-piechartholder]");

        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);

        if (chartOptions.plotOptions.pie.dataLabels.iconsFormat)
            self.iconsFormat (chartOptions.plotOptions.pie.dataLabels);
        
        // check for db questions
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            
            var getVotes;
            if (self.wizletInfo.getVotesFromPulse) {
                getVotes = self.getDBVotes();
            } else {
                getVotes = self.getModelVotes();
            }


            getVotes.then(function(votes){
                self.votes = votes;
                var data = self.createSeries(votes);
                self.renderChartData.call(self, data, chartOptions, chartElem);
                rendering.resolve(true);
            });
        }
        else {
            //if no questions, direct values given in the XML by the series
            chartElem.highcharts(chartOptions);
            rendering.resolve(true);
        }

		return rendering.promise;
    };

    HCPieChartModel.prototype.iconsFormat = function(dataLabels) {

        if (dataLabels.iconsFormat) {
            var format=dataLabels.iconsFormat;
            dataLabels.formatter = function () {
                if (this.point.icon)
                    return '<img src="Wizer/Pages/Events/' + wizerApi.eventName() + this.point.icon+ '" '+ (this.point.style ? 'style="'+this.point.style : '') + '"><span>' + this.point.y + (format=="percentage"?" %":"") + '</span>'
                else
                    return '<span>' + this.point[format] + (format=="percentage"?" %":"") + '</span>'
            }
        }
    };
    
    HCPieChartModel.prototype.redrawChart = function(e) {
        var self = e.data.self;
        var redraw = new Q.defer();
        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-piechartholder]");
        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);

        if (chartOptions.plotOptions.pie.dataLabels.iconsFormat)
            self.iconsFormat (chartOptions.plotOptions.pie.dataLabels);
        
        
        // check for db questions
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            
            var getVotes;
            if (self.wizletInfo.getVotesFromPulse) {
                getVotes = self.getDBVotes();
            } else {
                getVotes = self.getModelVotes();
            }

            getVotes.then(function(votes){
                
                if (self.votesUpdated(votes) == true) {
                    var data = self.createSeries(votes);
                    self.renderChartData.call(self, data, chartOptions, chartElem);
                }
                redraw.resolve(true);
            });
        }
        else {
            redraw.resolve(true);
        }
        return redraw.promise;
    };



    HCPieChartModel.prototype.getDBVotes = function() {
        var self = this;

        var defer = new Q.defer();   

        var questions = self.wizletInfo.questions;
        var values = [];
     
        var questionIds = [];
        $.each(questions, function (idx, question) {
            questionIds.push( self.wizerApi.getQuestionIdByName(question.bind) );
        });

        self.wizerApi.getMyVotes(questionIds).then(function (response) { 
            
            $.each(questionIds, function (idx, questionId) {

                if ( response.votes[questionId] && response.votes[questionId][0] ) {
                    values.push ( numeral().unformat(response.votes[questionId][0]) )
                } else {
                    values.push (0)
                }

            });
            
            defer.resolve(values);
        });
        
        return defer.promise;        
    }


    HCPieChartModel.prototype.getModelVotes = function() {
        var self = this;

        var defer = new Q.defer();   

        var questions = self.wizletInfo.questions;
        var values = [];
        var counter = 1;
     
        questions.forEach(function(question, idx) {

            self.wizerApi.calcBinderCache.getCalcValue(question.model, question.bind).then(function(value){
                values[idx] = value;
                if (counter == questions.length) {
                    defer.resolve(values); //when all model values loaded
                } else {
                    counter ++;
                }
            });
        });

        return defer.promise;
        
    }

    
    
    HCPieChartModel.prototype.votesUpdated = function(values) {

        var self=this, isUpdated=false;

        self.votes.forEach(function(value, idx) {
            if (value !== values[idx]) {
                isUpdated = true;
            }
        })          
        if (isUpdated) self.votes = values;
        
        return isUpdated;
    };


    HCPieChartModel.prototype.createSeries = function(values) {

        var self = this, series;

        var series = self.wizletInfo.chartConfig.series[0].data;

        series.forEach(function(serie, idx) {
            serie.y = values[idx];
        })
                
        return series;
    };
    


    HCPieChartModel.prototype.renderChartData = function(data, chartOptions, chartElem) {

        chartOptions.series[0].data = $.extend(true, [], chartOptions.series[0].data, data);

        // this.applyNumberFormat(chartOptions);
        // this.formatTooltips(chartOptions);
        chartElem.highcharts(chartOptions);
    };

    HCPieChartModel.prototype.formatTooltips = function(chartOptions) {
        var formatters = discover(chartOptions, 'numformat');

        formatters.forEach(function(ob) {
            var format = ob.numformat,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0, point = this, out = '<strong>' + point.series.name + '</strong><br />';
                        out += '' + point.x + ': ';
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        out += numberFormatting.format(val, format, scaler);
                        return out;
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCPieChartModel.prototype.applyNumberFormat = function(chartOptions) {
        var formatters = discover(chartOptions, 'formatter');

        formatters.forEach(function(ob) {
            var format = ob.formatter,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0;
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        return numberFormatting.format(val, format, scaler);
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCPieChartModel.getRegistration = function () {
        return new HCPieChartModel();
    };


    function search(tree, propertyName, result) {
        if ($.isArray(tree)) {
            for (var i = 0; i < tree.length; ++i) {
                search(tree[i], propertyName, result);
            }
        } else if ($.isPlainObject(tree)) {
            for (var pName in tree) {
                if (tree.hasOwnProperty(pName)) {
                    var subTree = tree[pName];
                    if (pName == propertyName) {
                        result.push(tree);
                    } else {
                        search(subTree, propertyName, result);
                    }
                }
            }
        }
    };

    function discover(src, propertyName) { 
        var propertyName = propertyName || 'formatter';
        var formatters = [];
        search(src, propertyName, formatters);
        return formatters;
    };

	
	
    return HCPieChartModel;

});
